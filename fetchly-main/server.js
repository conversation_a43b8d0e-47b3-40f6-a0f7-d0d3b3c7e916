const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
// Temporarily disable socket server for production deployment
// const { initializeChatServer } = require('./src/lib/socket-server');

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOST || '0.0.0.0';
const port = parseInt(process.env.PORT || '3000');
const socketPort = parseInt(process.env.SOCKET_IO_PORT || '3002');
const isProduction = process.env.NODE_ENV === 'production';

// Initialize Next.js app
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

// Redirect HTTP to HTTPS in production
const forceHttps = (req, res, next) => {
  if (isProduction && req.headers['x-forwarded-proto'] !== 'https') {
    return res.redirect(301, `https://${req.headers.host}${req.url}`);
  }
  return next();
};

app.prepare().then(() => {
  // Create HTTP server for Next.js
  const server = createServer(async (req, res) => {
    try {
      // Apply HTTPS redirect in production
      if (isProduction) {
        forceHttps(req, res, () => {});
      }
      
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  });

  // Start Next.js server
  server.listen(port, hostname, (err) => {
    if (err) throw err;
    console.log(`> Next.js ready on http${isProduction ? 's' : ''}://${hostname}:${port}`);
  });

  // Temporarily disable Socket.IO server for production deployment
  // TODO: Re-enable after fixing TypeScript import issues
  /*
  // Create separate HTTP server for Socket.IO
  const socketServer = createServer();

  // Initialize Socket.IO chat server
  const chatServer = initializeChatServer(socketServer);

  // Start Socket.IO server
  socketServer.listen(socketPort, (err) => {
    if (err) throw err;
    console.log(`> Socket.IO server ready on http://${hostname}:${socketPort}`);
  });
  */

  // Graceful shutdown
  process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
      console.log('Next.js server closed');
    });
    // socketServer.close(() => {
    //   console.log('Socket.IO server closed');
    // });
  });

  process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
      console.log('Next.js server closed');
    });
    // socketServer.close(() => {
    //   console.log('Socket.IO server closed');
    // });
    process.exit(0);
  });
});
