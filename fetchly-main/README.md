# Fetchly Pet Services Platform

A comprehensive full-stack, mobile-first, cross-platform pet service ecosystem built with Next.js, TypeScript, Firebase, and Tailwind CSS.

## 🚀 Features

### For Pet Owners
- **Comprehensive Pet Profiles**: Detailed pet management with medical records, vaccinations, and emergency contacts
- **Service Provider Search**: Advanced filtering and search functionality
- **Booking System**: Easy appointment scheduling with service providers
- **Review System**: Rate and review service providers

### For Service Providers
- **Tier-Based Subscription System**: Free and Pro tiers with different features
- **Comprehensive Dashboard**: Business analytics, booking management, and earnings tracking
- **Profile Management**: Showcase services, gallery, and business information
- **Client Management**: Track clients, bookings, and communications

### Provider Tier System
#### 🟢 Free Tier (Starter)
- Basic booking & payments
- Client dashboard (CMS Lite)
- Community help center
- Platform rewards program
- 5% commission per transaction

#### 🟡 Pro Tier ($20/month)
- All Free tier features
- AI Assistant (FetchBot) with automated replies
- Google/Apple Calendar sync
- Client retention tools & auto review requests
- Marketing boost & featured listings
- Custom branding & logo upload
- Bulk messaging & SMS/Email automation
- Advanced analytics & CSV reports
- Fetchly Verified Pro badge
- 5% or 3% commission (optional perk)

### For Administrators
- **Complete Admin Portal**: User management, provider oversight, content management
- **Analytics Dashboard**: Platform-wide statistics and performance metrics
- **Content Management**: Manage platform content and announcements

## 🛠 Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4, Framer Motion
- **Backend**: Firebase (Auth, Firestore, Functions, Analytics)
- **Mobile**: Capacitor for cross-platform mobile apps
- **Payments**: Stripe integration
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React, Heroicons
- **Deployment**: Heroku-ready with optimized build

## 🚀 Heroku Deployment

This project is fully configured for Heroku deployment with the following optimizations:

### Prerequisites
1. Heroku CLI installed
2. Git repository initialized
3. Firebase project configured

### Deployment Steps

1. **Create Heroku App**
   ```bash
   heroku create your-fetchly-app-name
   ```

2. **Set Environment Variables**
   ```bash
   heroku config:set NODE_ENV=production
   heroku config:set NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
   heroku config:set NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain
   heroku config:set NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   # Add other Firebase config variables
   ```

3. **Deploy**
   ```bash
   git add .
   git commit -m "Deploy to Heroku"
   git push heroku main
   ```

### Heroku Configuration Files
- `Procfile`: Defines the web process
- `package.json`: Includes Node.js version requirements
- `next.config.js`: Optimized for production with security headers

## 🏃‍♂️ Local Development

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Set Up Environment Variables**
   Create `.env.local` with your Firebase configuration:
   ```
   NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   # ... other Firebase config
   ```

3. **Run Development Server**
   ```bash
   npm run dev
   ```

4. **Build for Production**
   ```bash
   npm run build
   npm start
   ```

## 📱 Mobile Development

The app is configured with Capacitor for mobile deployment:

```bash
# Add platforms
npx cap add ios
npx cap add android

# Build and sync
npm run build
npx cap sync

# Open in native IDEs
npx cap open ios
npx cap open android
```

## 🔐 Authentication

Demo credentials for testing:
- **Pet Owner**: <EMAIL> / password123
- **Service Provider**: <EMAIL> / password123
- **Admin**: <EMAIL> / password123

## 🎨 Design System

- **Primary Colors**: Blue (#0ea5e9), Hot Pink (#d946ef), Fresh Green (#22c55e), Hot Orange (#f97316)
- **Typography**: Cool Slate (#64748b) for text
- **Effects**: Glass morphism, gradient animations, backdrop blur
- **Mobile-First**: Responsive design optimized for all devices

## 📊 Performance Optimizations

- **Image Optimization**: Next.js Image component with remote patterns
- **Bundle Optimization**: Package imports optimization for Lucide React
- **Security Headers**: X-Frame-Options, Content-Type-Options, Referrer-Policy
- **Compression**: Gzip compression enabled
- **Caching**: Optimized caching strategies

## 🔧 Configuration

### Environment Variables Required
```
NEXT_PUBLIC_FIREBASE_API_KEY
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
NEXT_PUBLIC_FIREBASE_PROJECT_ID
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
NEXT_PUBLIC_FIREBASE_APP_ID
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
```

## 📄 License

Private project - All rights reserved.

## 🤝 Contributing

This is a private project. For questions or support, contact the development team.

---

Built with ❤️ by the Fetchly Team