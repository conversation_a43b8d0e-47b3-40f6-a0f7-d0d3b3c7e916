{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "esnext"], "types": ["node"], "allowJs": true, "skipLibCheck": true, "strict": false, "noImplicitAny": false, "forceConsistentCasingInFileNames": false, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "downlevelIteration": true, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "out/types/**/*.ts"], "exclude": ["node_modules"]}