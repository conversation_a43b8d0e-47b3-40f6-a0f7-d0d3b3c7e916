rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Users can upload and read their own profile images (avatar, banner, etc.)
    match /users/{userId}/{imageType}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Stories are publicly readable but only writable by the owner
    match /users/{userId}/stories/{allPaths=**} {
      allow read: if true; // Stories are public
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can upload and read their own pet images
    match /pets/{userId}/{petId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Service providers can upload and read their own images
    match /providers/{providerId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == providerId;
    }
    
    // Chat file uploads
    match /chat/{chatRoomId}/{allPaths=**} {
      allow read, write: if request.auth != null;
    }
    
    // Public files (readable by everyone)
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
