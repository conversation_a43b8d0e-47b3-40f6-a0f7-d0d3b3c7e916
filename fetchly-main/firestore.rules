rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // ========================================
    // HELPER FUNCTIONS
    // ========================================

    // Check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Check if user owns the resource
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // ========================================
    // PUBLIC ACCESS COLLECTIONS (No auth required)
    // ========================================

    // Providers - Public read access for search functionality
    match /providers/{providerId} {
      allow read: if true; // Public read access for provider search
      allow write: if isAuthenticated(); // Only authenticated users can write
    }

    // Users - Public read access for basic profile info
    match /users/{userId} {
      allow read: if true; // Public read access for user profiles
      allow write: if isAuthenticated(); // Only authenticated users can write
    }

    // Posts - Public read access for community feed
    match /posts/{postId} {
      allow read: if true; // Public read access for community posts
      allow write: if isAuthenticated(); // Only authenticated users can write
    }

    // Services - Public read access for service browsing
    match /services/{serviceId} {
      allow read: if true; // Public read access for service listings
      allow write: if isAuthenticated(); // Only authenticated users can write
    }

    // Blog Posts - Public read access for blog content
    match /blogPosts/{postId} {
      allow read: if true; // Public read access for blog posts
      allow write: if isAuthenticated(); // Only authenticated users can write
    }

    // ========================================
    // AUTHENTICATED ACCESS COLLECTIONS
    // ========================================

    // All other collections require authentication
    match /{document=**} {
      allow read, write, create, update, delete: if isAuthenticated();
    }
  }
}
