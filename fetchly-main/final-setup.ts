import { Pool, PoolConfig } from 'pg';
import * as readline from 'readline';
import * as dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Database configuration
const dbConfig: PoolConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3005'),
  user: 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  database: 'postgres'
};

async function testConnection(): Promise<boolean> {
  const pool = new Pool(dbConfig);
  
  try {
    const client = await pool.connect();
    console.log('✅ PostgreSQL connection successful');
    client.release();
    await pool.end();
    return true;
  } catch (error) {
    console.error('❌ PostgreSQL connection failed:', (error as Error).message);
    await pool.end();
    return false;
  }
}

async function setupComplete(): Promise<void> {
  console.log('🚀 Starting Fetchly PostgreSQL Setup...');
  console.log('');
  
  // Test PostgreSQL connection
  console.log('🔍 Testing PostgreSQL connection...');
  const connected = await testConnection();
  
  if (!connected) {
    console.log('');
    console.log('❌ Cannot connect to PostgreSQL. Please check:');
    console.log('1. PostgreSQL service is running: net start postgresql-x64-17');
    console.log('2. PostgreSQL is running on port 3005');
    console.log('3. Your postgres user password is correct');
    console.log('');
    console.log('Update POSTGRES_PASSWORD in .env.local with the correct password');
    console.log('');
    rl.close();
    process.exit(1);
  }

  console.log('');
  console.log('🎉 Setup Complete!');
  console.log('');
  console.log('✅ PostgreSQL is connected and ready');
  console.log('✅ Environment variables are configured');
  console.log('');
  console.log('🚀 You can now start the development server:');
  console.log('   npm run dev');
  console.log('');
  console.log('📱 Or build for mobile:');
  console.log('   npm run cap:build');
  console.log('   npm run cap:ios');
  console.log('   npm run cap:android');
  console.log('');
  
  rl.close();
}

// Run setup
setupComplete().catch((error) => {
  console.error('Setup failed:', error);
  rl.close();
  process.exit(1);
});
