import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Use standalone for Heroku deployment, export for Capacitor builds
  output: process.env.CAPACITOR_BUILD ? 'export' : 'standalone',
  trailingSlash: process.env.CAPACITOR_BUILD ? true : false,
  distDir: process.env.CAPACITOR_BUILD ? 'out' : '.next',
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  experimental: {
    optimizePackageImports: ['lucide-react', '@heroicons/react']
  },
  webpack: (config: any, { isServer }: { isServer: boolean }) => {
    // Fix for Firebase modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
      };
    }

    // Ensure Firebase modules are properly resolved
    config.resolve.alias = {
      ...config.resolve.alias,
      'firebase/app': require.resolve('firebase/app'),
      'firebase/auth': require.resolve('firebase/auth'),
      'firebase/firestore': require.resolve('firebase/firestore'),
      'firebase/storage': require.resolve('firebase/storage'),
      'firebase/functions': require.resolve('firebase/functions'),
      'firebase/analytics': require.resolve('firebase/analytics'),
    };

    return config;
  },
  images: {
    domains: ['images.unsplash.com', 'via.placeholder.com', 'firebasestorage.googleapis.com'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'firebasestorage.googleapis.com',
        port: '',
        pathname: '/**',
      }
    ]
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  // Optimize for production
  compress: true,
  poweredByHeader: false,
  generateEtags: false,

  // HTTPS redirects and security headers
  async redirects() {
    return [
      // Redirect HTTP to HTTPS for Heroku domain
      {
        source: '/:path*',
        has: [
          {
            type: 'header',
            key: 'x-forwarded-proto',
            value: '(http)',
          },
        ],
        destination: 'https://fetchlypr-f1c481f969c3.herokuapp.com/:path*',
        permanent: true,
      },
      // Redirect custom domain to HTTPS when DNS is fixed
      {
        source: '/:path*',
        has: [
          {
            type: 'host',
            value: 'fetchlypr.com',
          },
          {
            type: 'header',
            key: 'x-forwarded-proto',
            value: '(http)',
          },
        ],
        destination: 'https://fetchlypr.com/:path*',
        permanent: true,
      },
      // Ensure www is handled
      {
        source: '/:path*',
        has: [
          {
            type: 'host',
            value: 'www.fetchlypr.com',
          },
        ],
        destination: 'https://fetchlypr.com/:path*',
        permanent: true,
      },
    ]
  },
  // Security headers
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Content-Security-Policy',
            value: `
              default-src 'self';
              script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com;
              style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
              img-src 'self' data: blob: https://*.stripe.com https://res.cloudinary.com https://images.unsplash.com https://via.placeholder.com https://firebasestorage.googleapis.com;
              font-src 'self' https://fonts.gstatic.com;
              connect-src 'self' https://api.stripe.com https://*.stripe.com https://firestore.googleapis.com https://*.googleapis.com;
              frame-src 'self' https://js.stripe.com https://hooks.stripe.com;
              media-src 'self' https://res.cloudinary.com;
            `.replace(/\s+/g, ' ').trim()
          }
        ]
      }
    ]
  }
};

export default nextConfig;
