{"name": "fetchly-pet-services", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "dev:stable": "next dev", "dev:full": "tsx server.ts", "build": "ESLINT_NO_DEV_ERRORS=true next build", "build:clean": "rm -rf .next && npm run build", "export": "CAPACITOR_BUILD=true next build", "start": "NODE_ENV=production tsx server.ts", "start:full": "NODE_ENV=production tsx server.ts", "lint": "next lint", "heroku-postbuild": "SKIP_ENV_VALIDATION=true ESLINT_NO_DEV_ERRORS=true next build", "firebase:emulators": "firebase emulators:start", "firebase:deploy": "firebase deploy", "firebase:init-db": "node scripts/init-firestore-database.js", "migrate:firebase": "tsx migrate-to-firebase.ts", "cap:build": "npm run export && npx cap sync", "cap:ios": "npm run cap:build && npx cap open ios", "cap:android": "npm run cap:build && npx cap open android", "cap:sync": "npx cap sync", "cap:add:ios": "npx cap add ios", "cap:add:android": "npx cap add android"}, "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.4.2", "@capacitor/core": "^7.4.2", "@capacitor/geolocation": "^7.1.4", "@capacitor/haptics": "^7.0.1", "@capacitor/ios": "^7.4.2", "@capacitor/keyboard": "^7.0.1", "@capacitor/local-notifications": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/push-notifications": "^7.0.1", "@capacitor/share": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@giphy/react-components": "^10.1.0", "@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@sendgrid/mail": "^8.1.5", "@stripe/react-stripe-js": "^3.9.0", "@stripe/stripe-js": "^7.8.0", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "embla-carousel-react": "^8.6.0", "emoji-picker-react": "^4.13.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "firebase": "^12.0.0", "framer-motion": "^12.23.11", "helmet": "^8.1.0", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.532.0", "multer": "^2.0.2", "next": "15.4.4", "next-themes": "^0.4.6", "nodemailer": "^7.0.5", "plaid": "^37.0.0", "react": "19.1.0", "react-day-picker": "^9.8.1", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.5.2", "react-plaid-link": "^4.0.1", "react-resizable-panels": "^3.0.4", "recharts": "^2.15.4", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.7", "stripe": "^18.4.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^4.0.15", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pg": "^8.15.5", "@types/puppeteer": "^5.4.7", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "firebase-admin": "^13.4.0", "firebase-functions": "^6.4.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}