<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fetchly Mobile Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        .mobile-frame {
            width: 375px;
            height: 812px;
            border: 2px solid #333;
            border-radius: 25px;
            margin: 20px auto;
            overflow: hidden;
            position: relative;
            background: #000;
            padding: 10px;
        }
        .screen {
            width: 100%;
            height: 100%;
            border-radius: 20px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .controls {
            text-align: center;
            margin: 20px;
        }
        button {
            margin: 5px;
            padding: 10px 20px;
            background: #0ea5e9;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0284c7;
        }
        .info {
            text-align: center;
            margin: 20px;
            padding: 20px;
            background: #f0f9ff;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>📱 Fetchly Mobile Test</h1>
        <p>Testing mobile responsiveness for iOS and Android deployment</p>
        <p><strong>Current viewport:</strong> <span id="viewport">375x812 (iPhone 12/13/14)</span></p>
    </div>
    
    <div class="controls">
        <button onclick="setViewport(375, 812, 'iPhone 12/13/14')">iPhone 12/13/14</button>
        <button onclick="setViewport(390, 844, 'iPhone 14 Pro')">iPhone 14 Pro</button>
        <button onclick="setViewport(360, 800, 'Android')">Android</button>
        <button onclick="setViewport(768, 1024, 'iPad')">iPad</button>
    </div>
    
    <div class="mobile-frame" id="frame">
        <div class="screen">
            <iframe src="http://localhost:3000/provider/dashboard" id="iframe"></iframe>
        </div>
    </div>
    
    <div class="controls">
        <button onclick="testPage('/provider/dashboard')">Provider Dashboard</button>
        <button onclick="testPage('/auth/signin')">Sign In</button>
        <button onclick="testPage('/search')">Search</button>
        <button onclick="testPage('/')">Home</button>
    </div>

    <script>
        function setViewport(width, height, device) {
            const frame = document.getElementById('frame');
            frame.style.width = width + 'px';
            frame.style.height = height + 'px';
            document.getElementById('viewport').textContent = `${width}x${height} (${device})`;
        }
        
        function testPage(path) {
            document.getElementById('iframe').src = `http://localhost:3000${path}`;
        }
        
        // Test touch events
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 Mobile test loaded');
            console.log('🔧 Testing touch capabilities...');
            
            // Simulate touch events
            const iframe = document.getElementById('iframe');
            iframe.addEventListener('load', function() {
                console.log('✅ Page loaded successfully');
                
                // Test viewport meta tag
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const viewport = iframeDoc.querySelector('meta[name="viewport"]');
                    if (viewport) {
                        console.log('✅ Viewport meta tag found:', viewport.content);
                    } else {
                        console.log('⚠️ Viewport meta tag missing');
                    }
                } catch (e) {
                    console.log('ℹ️ Cannot access iframe content (CORS)');
                }
            });
        });
    </script>
</body>
</html>
