# Firebase Setup Guide for Fetchly

## Quick Fix (Current Issue)

The app is now configured to work with demo data when Firebase isn't set up. You should be able to run the app now and see the dashboard with sample data.

## Setting Up Real Firebase (Optional)

To use real Firebase services instead of demo data, follow these steps:

### 1. Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Click "Create a project" or "Add project"
3. Enter project name: `fetchly-app` (or your preferred name)
4. Enable Google Analytics (optional)
5. Click "Create project"

### 2. Enable Firebase Services

In your Firebase project console:

1. **Authentication**
   - Go to Authentication > Sign-in method
   - Enable Email/Password, Google, and Phone providers

2. **Firestore Database**
   - Go to Firestore Database
   - Click "Create database"
   - Choose "Start in test mode" for development
   - Select a location close to your users

3. **Storage**
   - Go to Storage
   - Click "Get started"
   - Choose "Start in test mode"

4. **Functions** (for payment processing)
   - Go to Functions
   - Click "Get started"

### 3. Get Firebase Configuration

1. Go to Project Settings (gear icon)
2. Scroll down to "Your apps"
3. Click "Add app" > Web app icon
4. Register your app with name "Fetchly Web"
5. Copy the configuration object

### 4. Create Environment File

1. Copy `.env.example` to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. Replace the placeholder values in `.env.local` with your Firebase config:
   ```env
   NEXT_PUBLIC_FIREBASE_API_KEY=your_actual_api_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_actual_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_actual_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_actual_app_id
   NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_actual_measurement_id
   ```

### 5. Restart Development Server

```bash
npm run dev
```

The app will now use real Firebase services instead of demo data.

## Current Status

✅ **Working Now**: App runs with demo data  
🔄 **Next Step**: Set up real Firebase (optional)  
🔄 **Future**: Add Stripe payment processing  

## Demo Data Included

The app currently shows:
- Sample user profile (Sarah Johnson)
- Demo pet (Buddy the Golden Retriever)
- Sample bookings and transactions
- Mock wallet balance and rewards points

This allows you to see the full functionality without setting up Firebase first.
