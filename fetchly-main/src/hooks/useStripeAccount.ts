import { useState, useEffect } from 'react';
import { doc, onSnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';

// Define the User interface that matches your application's user structure
interface AppUser {
  id: string;
  email?: string | null;
  // Add other user properties as needed
}

export const useStripeAccount = () => {
  const { user } = useAuth();
  // Safely get the user ID with proper type checking
  const userId = user?.id;
  const [stripeAccount, setStripeAccount] = useState<{
    id: string;
    isConnected: boolean;
    dashboardUrl: string | null;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) return;

    const userRef = doc(db, 'users', userId);
    const unsubscribe = onSnapshot(
      userRef,
      (doc) => {
        const data = doc.data();
        if (data?.stripeAccountId) {
          setStripeAccount({
            id: data.stripeAccountId,
            isConnected: data.isStripeConnected || false,
            dashboardUrl: data.stripeDashboardLink || null,
          });
        } else {
          setStripeAccount(null);
        }
        setLoading(false);
      },
      (err) => {
        console.error('Error fetching Stripe account:', err);
        setError('Failed to load wallet information');
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [userId]);

  return {
    stripeAccount,
    loading,
    error,
  };
};
