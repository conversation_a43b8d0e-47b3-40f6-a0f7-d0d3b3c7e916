'use client';

import { useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { onlineStatusService } from '@/lib/services/online-status-service';

export function useOnlineStatus() {
  const { user } = useAuth();
  const heartbeatRef = useRef<(() => void) | null>(null);
  const isSetupRef = useRef(false);

  useEffect(() => {
    if (!user || isSetupRef.current) return;

    console.log('🔔 Setting up online status for user:', user.name);
    isSetupRef.current = true;

    // Set user as online when component mounts
    const setUserOnline = async () => {
      try {
        await onlineStatusService.setUserOnline(user.id, {
          name: user.name,
          avatar: user.avatar,
          role: user.role,
          city: user.city,
          verified: user.verified
        });

        // Start heartbeat
        heartbeatRef.current = onlineStatusService.startHeartbeat(user.id);
        console.log('✅ User set as online with heartbeat');
      } catch (error) {
        console.error('❌ Error setting user online:', error);
      }
    };

    setUserOnline();

    // Set user as offline when page is about to unload
    const handleBeforeUnload = async () => {
      try {
        await onlineStatusService.setUserOffline(user.id);
        console.log('✅ User set as offline on page unload');
      } catch (error) {
        console.error('❌ Error setting user offline:', error);
      }
    };

    // Handle visibility change (tab switching)
    const handleVisibilityChange = async () => {
      if (document.hidden) {
        // Tab is hidden - set as offline after delay
        setTimeout(async () => {
          if (document.hidden) {
            try {
              await onlineStatusService.setUserOffline(user.id);
              console.log('✅ User set as offline (tab hidden)');
            } catch (error) {
              console.error('❌ Error setting user offline:', error);
            }
          }
        }, 30000); // 30 seconds delay
      } else {
        // Tab is visible - set as online
        try {
          await onlineStatusService.setUserOnline(user.id, {
            name: user.name,
            avatar: user.avatar,
            role: user.role,
            city: user.city,
            verified: user.verified
          });
          console.log('✅ User set as online (tab visible)');
        } catch (error) {
          console.error('❌ Error setting user online:', error);
        }
      }
    };

    // Add event listeners
    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup function
    return () => {
      console.log('🧹 Cleaning up online status for user:', user.name);
      
      // Stop heartbeat
      if (heartbeatRef.current) {
        heartbeatRef.current();
        heartbeatRef.current = null;
      }

      // Set user as offline
      onlineStatusService.setUserOffline(user.id).catch(console.error);

      // Remove event listeners
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      
      isSetupRef.current = false;
    };
  }, [user]);

  // Cleanup on user logout
  useEffect(() => {
    if (!user && isSetupRef.current) {
      console.log('🔓 User logged out, removing from online status');
      
      // Stop heartbeat
      if (heartbeatRef.current) {
        heartbeatRef.current();
        heartbeatRef.current = null;
      }
      
      isSetupRef.current = false;
    }
  }, [user]);
}

export default useOnlineStatus;
