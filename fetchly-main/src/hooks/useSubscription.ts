import { useState } from 'react';

interface UseSubscriptionReturn {
  subscribe: (email: string, source?: string) => Promise<{
    success: boolean;
    message: string;
    alreadySubscribed?: boolean;
  }>;
  unsubscribe: (email: string) => Promise<{
    success: boolean;
    message: string;
  }>;
  isLoading: boolean;
  error: string | null;
}

export function useSubscription(): UseSubscriptionReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const subscribe = async (email: string, source = 'website') => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, source }),
      });

      const result = await response.json();

      if (!result.success) {
        setError(result.message);
      }

      return result;

    } catch (err) {
      const errorMessage = 'Network error. Please check your connection and try again.';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };

  const unsubscribe = async (email: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/subscribe?email=${encodeURIComponent(email)}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (!result.success) {
        setError(result.message);
      }

      return result;

    } catch (err) {
      const errorMessage = 'Network error. Please check your connection and try again.';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    subscribe,
    unsubscribe,
    isLoading,
    error
  };
}
