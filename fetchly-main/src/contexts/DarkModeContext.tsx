'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

interface DarkModeContextType {
  isDarkMode: boolean;
  toggleDarkMode: () => void;
  setDarkMode: (enabled: boolean) => void;
}

const DarkModeContext = createContext<DarkModeContextType | undefined>(undefined);

export function DarkModeProvider({ children }: { children: React.ReactNode }) {
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Load dark mode preference from localStorage on mount
  useEffect(() => {
    const savedDarkMode = localStorage.getItem('fetchly-dark-mode');
    if (savedDarkMode !== null) {
      const darkModeEnabled = JSON.parse(savedDarkMode);
      setIsDarkMode(darkModeEnabled);
      updateDocumentClass(darkModeEnabled);
    } else {
      // Check system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDarkMode(prefersDark);
      updateDocumentClass(prefersDark);
    }
  }, []);

  // Update document class and localStorage when dark mode changes
  const updateDocumentClass = (enabled: boolean) => {
    if (enabled) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  const toggleDarkMode = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    updateDocumentClass(newDarkMode);
    localStorage.setItem('fetchly-dark-mode', JSON.stringify(newDarkMode));
  };

  const setDarkMode = (enabled: boolean) => {
    setIsDarkMode(enabled);
    updateDocumentClass(enabled);
    localStorage.setItem('fetchly-dark-mode', JSON.stringify(enabled));
  };

  const value = {
    isDarkMode,
    toggleDarkMode,
    setDarkMode
  };

  return (
    <DarkModeContext.Provider value={value}>
      {children}
    </DarkModeContext.Provider>
  );
}

export function useDarkMode() {
  const context = useContext(DarkModeContext);
  if (context === undefined) {
    throw new Error('useDarkMode must be used within a DarkModeProvider');
  }
  return context;
}
