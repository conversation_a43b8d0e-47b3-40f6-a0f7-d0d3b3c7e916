'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

export interface AdminUser {
  email: string;
  role: 'admin' | 'superadmin' | 'support';
  loginTime: number;
}

interface AdminContextType {
  admin: AdminUser | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

const ADMIN_PERMISSIONS = {
  support: [
    'view_bookings',
    'view_users',
    'view_providers',
    'manage_support_tickets',
    'send_messages'
  ],
  admin: [
    'view_bookings',
    'view_providers',
    'view_users',
    'view_analytics',
    'manage_bookings',
    'manage_providers',
    'manage_featured',
    'manage_reviews'
  ],
  superadmin: [
    'view_bookings',
    'view_providers',
    'view_users',
    'view_analytics',
    'manage_bookings',
    'manage_providers',
    'manage_users',
    'manage_settings',
    'view_payments',
    'manage_payments',
    'system_admin',
    'manage_featured',
    'manage_reviews',
    'manage_support_tickets',
    'send_messages'
  ]
};

export function AdminProvider({ children }: { children: React.ReactNode }) {
  const [admin, setAdmin] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);

  // Check authentication on mount
  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/admin/auth/verify', {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAdmin(data.admin);
        }
      }
    } catch (error) {
      console.error('Admin auth check failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/admin/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (data.success) {
        setAdmin(data.admin);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Admin login failed:', error);
      return false;
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/admin/auth/logout', {
        method: 'POST',
        credentials: 'include'
      });
    } catch (error) {
      console.error('Admin logout failed:', error);
    } finally {
      setAdmin(null);
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!admin) return false;
    return ADMIN_PERMISSIONS[admin.role].includes(permission);
  };

  const value: AdminContextType = {
    admin,
    loading,
    login,
    logout,
    checkAuth,
    hasPermission
  };

  return (
    <AdminContext.Provider value={value}>
      {children}
    </AdminContext.Provider>
  );
}

export function useAdmin() {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
}
