'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { collection, query, where, orderBy, limit, onSnapshot, doc, updateDoc, Timestamp, addDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from './AuthContext';

import { UnifiedNotification } from '@/lib/services/unified-notification-service';

// Use the unified notification interface
export type Notification = UnifiedNotification;

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  loading: boolean;
  // New methods for unified notifications
  getNotificationsByCategory: (category: string) => Notification[];
  getNotificationsByType: (type: string) => Notification[];
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    if (!user?.id) {
      setNotifications([]);
      setLoading(false);
      return;
    }

    console.log('🔔 Setting up notification listener for user:', user.id);

    // Set up real-time listener for notifications
    const notificationsRef = collection(db, 'notifications');
    const q = query(
      notificationsRef,
      where('userId', '==', user.id), // Fixed: use user.id instead of user.uid
      orderBy('createdAt', 'desc'),
      limit(50) // Get last 50 notifications
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const notificationData: Notification[] = [];
      snapshot.forEach((doc) => {
        notificationData.push({
          id: doc.id,
          ...doc.data()
        } as Notification);
      });

      console.log('🔔 NotificationContext received notifications:', {
        count: notificationData.length,
        userId: user.id, // Fixed: use user.id instead of user.uid
        notifications: notificationData.map(n => ({ id: n.id, type: n.type, title: n.title, userId: n.userId }))
      });

      console.log('🔍 NOTIFICATION DEBUG - Current user ID:', user.id);
      console.log('🔍 NOTIFICATION DEBUG - User role:', user.role);
      notificationData.forEach(n => {
        console.log('🔔 Notification:', {
          id: n.id,
          userId: n.userId,
          type: n.type,
          title: n.title,
          isForCurrentUser: n.userId === user.id ? '✅ YES' : '❌ NO'
        });
      });

      setNotifications(notificationData);
      setLoading(false);
    }, (error) => {
      console.error('❌ Error fetching notifications:', error);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [user?.id]); // Fixed: use user.id instead of user.uid

  const markAsRead = async (notificationId: string) => {
    try {
      const notificationRef = doc(db, 'notifications', notificationId);
      await updateDoc(notificationRef, {
        isRead: true // Updated to match unified notification schema
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const unreadNotifications = notifications.filter(n => !n.isRead);
      const promises = unreadNotifications.map(notification =>
        updateDoc(doc(db, 'notifications', notification.id!), { isRead: true })
      );
      await Promise.all(promises);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Helper functions for filtering notifications
  const getNotificationsByCategory = (category: string) => {
    return notifications.filter(n => n.category === category);
  };

  const getNotificationsByType = (type: string) => {
    return notifications.filter(n => n.type === type);
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const value = {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    loading,
    getNotificationsByCategory,
    getNotificationsByType
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}

// Helper function to create notifications
export async function createNotification(
  userId: string,
  title: string,
  message: string,
  type: Notification['type'],
  data?: any,
  actionUrl?: string
) {
  try {
    const notificationsRef = collection(db, 'notifications');
    await addDoc(notificationsRef, {
      userId,
      title,
      message,
      type,
      read: false,
      createdAt: Timestamp.now(),
      data,
      actionUrl
    });
  } catch (error) {
    console.error('Error creating notification:', error);
  }
}
