'use client';

import React, { createContext, useContext, useState } from 'react';

interface ChatModalContextType {
  isOpen: boolean;
  openChat: () => void;
  closeChat: () => void;
}

const ChatModalContext = createContext<ChatModalContextType | undefined>(undefined);

export function ChatModalProvider({ children }: { children: React.ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);

  const openChat = () => setIsOpen(true);
  const closeChat = () => setIsOpen(false);

  return (
    <ChatModalContext.Provider value={{ isOpen, openChat, closeChat }}>
      {children}
    </ChatModalContext.Provider>
  );
}

export function useChatModal() {
  const context = useContext(ChatModalContext);
  if (context === undefined) {
    throw new Error('useChatModal must be used within a ChatModalProvider');
  }
  return context;
}
