export interface PasswordStrength {
  score: number; // 0-4 (0=very weak, 4=very strong)
  level: 'very-weak' | 'weak' | 'fair' | 'good' | 'strong';
  color: string;
  percentage: number;
  feedback: string[];
  isValid: boolean;
}

export interface PasswordRequirement {
  met: boolean;
  text: string;
}

/**
 * Calculate password strength based on various criteria
 */
export function calculatePasswordStrength(password: string): PasswordStrength {
  if (!password) {
    return {
      score: 0,
      level: 'very-weak',
      color: '#ef4444',
      percentage: 0,
      feedback: ['Enter a password'],
      isValid: false
    };
  }

  let score = 0;
  const feedback: string[] = [];
  
  // Length check (minimum 12 characters)
  if (password.length >= 12) {
    score += 1;
  } else {
    feedback.push('Use at least 12 characters');
  }
  
  // Additional length bonus
  if (password.length >= 16) {
    score += 0.5;
  }
  
  // Lowercase letters
  if (/[a-z]/.test(password)) {
    score += 0.5;
  } else {
    feedback.push('Add lowercase letters');
  }
  
  // Uppercase letters
  if (/[A-Z]/.test(password)) {
    score += 0.5;
  } else {
    feedback.push('Add uppercase letters');
  }
  
  // Numbers
  if (/\d/.test(password)) {
    score += 0.5;
  } else {
    feedback.push('Add numbers');
  }
  
  // Special characters
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Add special characters (!@#$%^&*)');
  }
  
  // No common patterns
  const commonPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /abc123/i,
    /admin/i,
    /letmein/i,
    /welcome/i,
    /monkey/i,
    /dragon/i,
    /master/i
  ];
  
  const hasCommonPattern = commonPatterns.some(pattern => pattern.test(password));
  if (hasCommonPattern) {
    score -= 1;
    feedback.push('Avoid common words and patterns');
  }
  
  // No repeated characters (more than 2 in a row)
  if (/(.)\1{2,}/.test(password)) {
    score -= 0.5;
    feedback.push('Avoid repeated characters');
  }
  
  // No sequential characters
  const hasSequential = hasSequentialChars(password);
  if (hasSequential) {
    score -= 0.5;
    feedback.push('Avoid sequential characters (abc, 123)');
  }
  
  // Ensure score is between 0 and 4
  score = Math.max(0, Math.min(4, score));
  
  // Determine level and color
  let level: PasswordStrength['level'];
  let color: string;
  
  if (score < 1) {
    level = 'very-weak';
    color = '#ef4444'; // red-500
  } else if (score < 2) {
    level = 'weak';
    color = '#f97316'; // orange-500
  } else if (score < 2.5) {
    level = 'fair';
    color = '#eab308'; // yellow-500
  } else if (score < 3.5) {
    level = 'good';
    color = '#22c55e'; // green-500
  } else {
    level = 'strong';
    color = '#16a34a'; // green-600
  }
  
  const percentage = (score / 4) * 100;
  
  // Password is valid if it meets minimum requirements
  const isValid = password.length >= 12 && 
                  /[a-z]/.test(password) && 
                  /[A-Z]/.test(password) && 
                  /\d/.test(password) && 
                  /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(password) &&
                  !hasCommonPattern;
  
  if (feedback.length === 0 && isValid) {
    feedback.push('Strong password! ✓');
  }
  
  return {
    score,
    level,
    color,
    percentage,
    feedback,
    isValid
  };
}

/**
 * Get password requirements checklist
 */
export function getPasswordRequirements(password: string): PasswordRequirement[] {
  return [
    {
      met: password.length >= 12,
      text: 'At least 12 characters'
    },
    {
      met: /[a-z]/.test(password),
      text: 'One lowercase letter'
    },
    {
      met: /[A-Z]/.test(password),
      text: 'One uppercase letter'
    },
    {
      met: /\d/.test(password),
      text: 'One number'
    },
    {
      met: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(password),
      text: 'One special character'
    },
    {
      met: !hasCommonPatterns(password),
      text: 'No common words or patterns'
    }
  ];
}

/**
 * Check for sequential characters
 */
function hasSequentialChars(password: string): boolean {
  const sequences = [
    'abcdefghijklmnopqrstuvwxyz',
    'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    '0123456789',
    'qwertyuiopasdfghjklzxcvbnm',
    'QWERTYUIOPASDFGHJKLZXCVBNM'
  ];
  
  for (const sequence of sequences) {
    for (let i = 0; i <= sequence.length - 3; i++) {
      const subseq = sequence.substring(i, i + 3);
      if (password.includes(subseq)) {
        return true;
      }
    }
  }
  
  return false;
}

/**
 * Check for common patterns
 */
function hasCommonPatterns(password: string): boolean {
  const commonPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /abc123/i,
    /admin/i,
    /letmein/i,
    /welcome/i,
    /monkey/i,
    /dragon/i,
    /master/i,
    /login/i,
    /user/i,
    /guest/i,
    /test/i
  ];
  
  return commonPatterns.some(pattern => pattern.test(password));
}

/**
 * Generate password strength label
 */
export function getPasswordStrengthLabel(level: PasswordStrength['level']): string {
  switch (level) {
    case 'very-weak':
      return 'Very Weak';
    case 'weak':
      return 'Weak';
    case 'fair':
      return 'Fair';
    case 'good':
      return 'Good';
    case 'strong':
      return 'Strong';
    default:
      return 'Unknown';
  }
}
