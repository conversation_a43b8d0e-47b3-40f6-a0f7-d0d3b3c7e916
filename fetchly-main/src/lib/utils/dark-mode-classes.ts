/**
 * Dark Mode Utility Classes
 * Comprehensive mapping of light mode classes to dark mode equivalents
 */

export const darkModeClassMap = {
  // Background Colors
  'bg-white': 'dark:bg-dark-navy',
  'bg-gray-50': 'dark:bg-dark-navy',
  'bg-gray-100': 'dark:bg-dark-navy',
  'bg-gray-200': 'dark:bg-dark-slate',
  'bg-gray-300': 'dark:bg-dark-slate',
  'bg-green-50': 'dark:bg-dark-navy',
  'bg-blue-50': 'dark:bg-dark-navy',
  'bg-purple-50': 'dark:bg-dark-navy',
  'bg-pink-50': 'dark:bg-dark-navy',
  'bg-yellow-50': 'dark:bg-dark-navy',
  'bg-red-50': 'dark:bg-dark-navy',
  'bg-green-500': 'dark:bg-dark-electric',
  'bg-green-600': 'dark:bg-dark-electric',
  'bg-blue-500': 'dark:bg-dark-electric',
  'bg-blue-600': 'dark:bg-dark-electric',

  // Text Colors
  'text-gray-900': 'dark:text-dark-offWhite',
  'text-gray-800': 'dark:text-dark-offWhite',
  'text-gray-700': 'dark:text-dark-offWhite',
  'text-gray-600': 'dark:text-dark-lightGray',
  'text-gray-500': 'dark:text-dark-lightGray',
  'text-gray-400': 'dark:text-dark-slate',
  'text-gray-300': 'dark:text-dark-slate',
  'text-green-600': 'dark:text-dark-electric',
  'text-green-500': 'dark:text-dark-electric',
  'text-blue-600': 'dark:text-dark-electric',
  'text-blue-500': 'dark:text-dark-electric',
  'text-yellow-300': 'dark:text-dark-neon',
  'text-yellow-400': 'dark:text-dark-neon',

  // Border Colors
  'border-gray-200': 'dark:border-dark-slate',
  'border-gray-300': 'dark:border-dark-slate',
  'border-white': 'dark:border-dark-slate',

  // Hover States
  'hover:bg-gray-50': 'dark:hover:bg-dark-electric/10',
  'hover:bg-green-50': 'dark:hover:bg-dark-electric/10',
  'hover:bg-blue-50': 'dark:hover:bg-dark-electric/10',
  'hover:bg-green-700': 'dark:hover:bg-dark-sky',
  'hover:bg-blue-700': 'dark:hover:bg-dark-sky',
  'hover:text-green-600': 'dark:hover:text-dark-neon',
  'hover:text-blue-600': 'dark:hover:text-dark-electric',

  // Focus States
  'focus:ring-green-500': 'dark:focus:ring-dark-electric',
  'focus:ring-blue-500': 'dark:focus:ring-dark-electric',
  'focus:border-green-500': 'dark:focus:border-dark-electric',
  'focus:border-blue-500': 'dark:focus:border-dark-electric',
};

/**
 * Automatically add dark mode classes to a className string
 */
export function addDarkModeClasses(className: string): string {
  if (!className) return '';
  
  const classes = className.split(' ');
  const darkModeClasses: string[] = [];
  
  classes.forEach(cls => {
    // Add the original class
    darkModeClasses.push(cls);
    
    // Add dark mode equivalent if it exists
    if (darkModeClassMap[cls as keyof typeof darkModeClassMap]) {
      darkModeClasses.push(darkModeClassMap[cls as keyof typeof darkModeClassMap]);
    }
  });
  
  return darkModeClasses.join(' ');
}

/**
 * Common dark mode class combinations for different component types
 */
export const darkModePresets = {
  // Cards and containers
  card: 'bg-white dark:bg-dark-navy border border-gray-200 dark:border-dark-slate shadow-lg dark:shadow-2xl',
  
  // Buttons
  primaryButton: 'bg-green-600 dark:bg-dark-electric hover:bg-green-700 dark:hover:bg-dark-sky text-white dark:text-dark-offWhite',
  secondaryButton: 'bg-gray-200 dark:bg-dark-slate hover:bg-gray-300 dark:hover:bg-dark-electric/20 text-gray-800 dark:text-dark-offWhite',
  
  // Inputs
  input: 'bg-white dark:bg-dark-navy border border-gray-300 dark:border-dark-slate text-gray-900 dark:text-dark-offWhite placeholder-gray-500 dark:placeholder-dark-slate focus:border-green-500 dark:focus:border-dark-electric',
  
  // Text elements
  heading: 'text-gray-900 dark:text-dark-offWhite',
  subheading: 'text-gray-700 dark:text-dark-lightGray',
  body: 'text-gray-600 dark:text-dark-lightGray',
  muted: 'text-gray-500 dark:text-dark-slate',
  
  // Navigation
  nav: 'bg-white dark:bg-dark-navy border-b border-gray-200 dark:border-dark-slate',
  navLink: 'text-gray-700 dark:text-dark-lightGray hover:text-green-600 dark:hover:text-dark-electric',
  
  // Modals and overlays
  modal: 'bg-white dark:bg-dark-navy border border-gray-200 dark:border-dark-slate shadow-xl dark:shadow-2xl',
  overlay: 'bg-black/50 dark:bg-black/70',
  
  // Tables
  table: 'bg-white dark:bg-dark-navy',
  tableHeader: 'bg-gray-50 dark:bg-dark-slate text-gray-900 dark:text-dark-offWhite',
  tableRow: 'border-b border-gray-200 dark:border-dark-slate hover:bg-gray-50 dark:hover:bg-dark-electric/10',
  
  // Forms
  formGroup: 'space-y-2',
  label: 'text-gray-700 dark:text-dark-lightGray font-medium',
  
  // Status indicators
  success: 'text-green-600 dark:text-dark-neon bg-green-50 dark:bg-dark-neon/10',
  error: 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20',
  warning: 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20',
  info: 'text-blue-600 dark:text-dark-electric bg-blue-50 dark:bg-dark-electric/10',
};

/**
 * Helper function to get preset classes
 */
export function getDarkModePreset(preset: keyof typeof darkModePresets): string {
  return darkModePresets[preset] || '';
}

/**
 * Combine multiple presets
 */
export function combineDarkModePresets(...presets: (keyof typeof darkModePresets)[]): string {
  return presets.map(preset => getDarkModePreset(preset)).join(' ');
}
