import { httpsCallable, HttpsCallableResult } from 'firebase/functions';
import { functions } from '@/lib/firebase';

// Define types for the Stripe service
type StripeBalance = {
  available: number;
  pending: number;
  currency: string;
};

type StripeDashboardLinkResponse = {
  url: string;
};

// Custom error class for Stripe service errors
export class StripeServiceError extends Error {
  constructor(message: string, public readonly originalError?: unknown) {
    super(message);
    this.name = 'StripeServiceError';
    
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, StripeServiceError);
    }
  }
}

export const stripeService = {
  // Get the Stripe dashboard login link
  getDashboardLink: async (): Promise<string> => {
    try {
      const getDashboardLinkFn = httpsCallable<{}, StripeDashboardLinkResponse>(
        functions, 
        'getStripeDashboardLink'
      );
      const result = await getDashboardLinkFn({});
      return result.data.url;
    } catch (error) {
      console.error('Error getting Stripe dashboard link:', error);
      throw new StripeServiceError(
        'Failed to get Stripe dashboard link. Please try again later.',
        error
      );
    }
  },

  // Get the account balance
  getBalance: async (): Promise<StripeBalance> => {
    try {
      const getBalanceFn = httpsCallable<{}, StripeBalance>(
        functions, 
        'getStripeBalance'
      );
      const result = await getBalanceFn({});
      return result.data;
    } catch (error) {
      console.error('Error getting Stripe balance:', error);
      throw new StripeServiceError(
        'Failed to load balance information. Please try again later.',
        error
      );
    }
  },

  // Format currency
  formatCurrency: (amount: number, currency: string = 'usd'): string => {
    try {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency.toUpperCase(),
      }).format(amount / 100); // Convert from cents to dollars
    } catch (error) {
      console.error('Error formatting currency:', error);
      // Fallback to a basic format if Intl is not available
      return `${currency.toUpperCase()} ${(amount / 100).toFixed(2)}`;
    }
  },
};
