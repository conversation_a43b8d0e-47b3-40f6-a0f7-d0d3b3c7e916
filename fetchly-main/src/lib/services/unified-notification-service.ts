import { db } from '@/lib/firebase';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  query,
  where,
  getDocs,
  getDoc,
  orderBy,
  limit,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';

// Unified notification types for ALL platform activities
export type NotificationType = 
  // Chat & Messaging
  | 'new_message' | 'new_chat' | 'support_message'
  // Bookings
  | 'booking_request' | 'booking_confirmed' | 'booking_cancelled' | 'booking_completed'
  // Payments & Earnings
  | 'payment_received' | 'payout_processed' | 'subscription_renewed' | 'subscription_cancelled'
  // Social & Community
  | 'post_liked' | 'post_commented' | 'post_shared' | 'new_follower' | 'friend_request'
  // Reviews & Ratings
  | 'new_review' | 'review_response' | 'rating_received'
  // System & Admin
  | 'account_verified' | 'profile_approved' | 'system_update' | 'maintenance_notice'
  // Emergency & Alerts
  | 'emergency_alert' | 'service_reminder' | 'appointment_reminder';

export interface UnifiedNotification {
  id?: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  
  // Rich data for different notification types
  data?: {
    // Chat data
    chatId?: string;
    messageId?: string;
    senderId?: string;
    senderName?: string;
    senderAvatar?: string;
    
    // Booking data
    bookingId?: string;
    serviceType?: string;
    providerId?: string;
    providerName?: string;
    bookingDate?: Date;
    amount?: number;
    
    // Social data
    postId?: string;
    commentId?: string;
    authorId?: string;
    authorName?: string;
    authorAvatar?: string;
    
    // Review data
    reviewId?: string;
    rating?: number;
    reviewText?: string;
    
    // Payment data
    transactionId?: string;
    paymentAmount?: number;
    paymentMethod?: string;
    
    // Generic data
    targetId?: string;
    targetType?: string;
    actionUrl?: string;
    imageUrl?: string;
  };
  
  // Metadata
  createdAt: Date;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'chat' | 'booking' | 'payment' | 'social' | 'system' | 'emergency';
  
  // Actions
  actions?: {
    primary?: {
      label: string;
      action: string;
      url?: string;
    };
    secondary?: {
      label: string;
      action: string;
      url?: string;
    };
  };
}

class UnifiedNotificationService {
  private readonly COLLECTION = 'notifications';

  /**
   * Clean notification data to remove undefined values
   */
  private cleanNotificationData(data: any): any {
    const cleaned: any = {};

    for (const [key, value] of Object.entries(data)) {
      if (value !== undefined) {
        if (typeof value === 'object' && value !== null && !(value instanceof Date) && !value.toDate) {
          // Recursively clean nested objects
          cleaned[key] = this.cleanNotificationData(value);
        } else {
          cleaned[key] = value;
        }
      }
    }

    return cleaned;
  }

  /**
   * Create a unified notification for any platform activity
   */
  async createNotification(notification: Omit<UnifiedNotification, 'id' | 'createdAt'>): Promise<string> {
    try {
      // Clean up undefined values to prevent Firestore errors
      const cleanData = this.cleanNotificationData({
        ...notification,
        createdAt: Timestamp.now()
      });

      console.log('🔔 Creating unified notification:', {
        collection: this.COLLECTION,
        userId: notification.userId,
        type: notification.type,
        title: notification.title
      });

      const docRef = await addDoc(collection(db, this.COLLECTION), cleanData);

      console.log('✅ Unified notification created successfully:', docRef.id);

      // Send browser notification if high priority
      if (notification.priority === 'high' || notification.priority === 'urgent') {
        await this.sendBrowserNotification(notification);
      }

      return docRef.id;
    } catch (error) {
      console.error('❌ Error creating unified notification:', error);
      throw error;
    }
  }

  /**
   * CHAT NOTIFICATIONS
   */
  async notifyNewMessage(
    recipientId: string,
    senderId: string,
    senderName: string,
    messageText: string,
    chatId: string,
    messageId: string,
    senderAvatar?: string,
    isGroupChat: boolean = false
  ): Promise<void> {
    await this.createNotification({
      userId: recipientId,
      type: 'new_message',
      title: isGroupChat ? `New message in group` : `New message from ${senderName}`,
      message: messageText.length > 100 ? messageText.substring(0, 100) + '...' : messageText,
      data: {
        chatId,
        messageId,
        senderId,
        senderName,
        senderAvatar,
        actionUrl: `/chat?id=${chatId}`
      },
      priority: 'medium',
      category: 'chat',
      isRead: false,
      actions: {
        primary: {
          label: 'Reply',
          action: 'open_chat',
          url: `/chat?id=${chatId}`
        }
      }
    });
  }

  /**
   * BOOKING NOTIFICATIONS
   */
  async notifyBookingRequest(
    providerId: string,
    petOwnerId: string,
    petOwnerName: string,
    serviceType: string,
    bookingId: string,
    bookingDate: Date,
    amount: number
  ): Promise<void> {
    await this.createNotification({
      userId: providerId,
      type: 'booking_request',
      title: 'New Booking Request',
      message: `${petOwnerName} wants to book ${serviceType} for ${bookingDate.toLocaleDateString()}`,
      data: {
        bookingId,
        serviceType,
        authorId: petOwnerId,
        authorName: petOwnerName,
        bookingDate,
        amount,
        actionUrl: `/provider/bookings/${bookingId}`
      },
      priority: 'high',
      category: 'booking',
      isRead: false,
      actions: {
        primary: {
          label: 'View Booking',
          action: 'view_booking',
          url: `/provider/bookings/${bookingId}`
        },
        secondary: {
          label: 'Accept',
          action: 'accept_booking'
        }
      }
    });
  }

  async notifyBookingConfirmed(
    petOwnerId: string,
    providerId: string,
    providerName: string,
    serviceType: string,
    bookingId: string,
    bookingDate: Date
  ): Promise<void> {
    await this.createNotification({
      userId: petOwnerId,
      type: 'booking_confirmed',
      title: 'Booking Confirmed!',
      message: `${providerName} confirmed your ${serviceType} booking for ${bookingDate.toLocaleDateString()}`,
      data: {
        bookingId,
        serviceType,
        providerId,
        providerName,
        bookingDate,
        actionUrl: `/bookings/${bookingId}`
      },
      priority: 'high',
      category: 'booking',
      isRead: false,
      actions: {
        primary: {
          label: 'View Details',
          action: 'view_booking',
          url: `/bookings/${bookingId}`
        }
      }
    });
  }

  /**
   * SOCIAL NOTIFICATIONS
   */
  async notifyPostLiked(
    postAuthorId: string,
    likerId: string,
    likerName: string,
    postId: string,
    postContent: string,
    likerAvatar?: string
  ): Promise<void> {
    await this.createNotification({
      userId: postAuthorId,
      type: 'post_liked',
      title: 'Someone liked your post',
      message: `${likerName} liked your post: "${postContent.substring(0, 50)}..."`,
      data: {
        postId,
        authorId: likerId,
        authorName: likerName,
        authorAvatar: likerAvatar,
        actionUrl: `/community/post/${postId}`
      },
      priority: 'low',
      category: 'social',
      isRead: false,
      actions: {
        primary: {
          label: 'View Post',
          action: 'view_post',
          url: `/community/post/${postId}`
        }
      }
    });
  }

  async notifyNewFollower(
    userId: string,
    followerId: string,
    followerName: string,
    followerAvatar?: string
  ): Promise<void> {
    await this.createNotification({
      userId,
      type: 'new_follower',
      title: 'New Follower',
      message: `${followerName} started following you`,
      data: {
        authorId: followerId,
        authorName: followerName,
        authorAvatar: followerAvatar,
        actionUrl: `/profile/${followerId}`
      },
      priority: 'medium',
      category: 'social',
      isRead: false,
      actions: {
        primary: {
          label: 'View Profile',
          action: 'view_profile',
          url: `/profile/${followerId}`
        },
        secondary: {
          label: 'Follow Back',
          action: 'follow_back'
        }
      }
    });
  }

  /**
   * PAYMENT NOTIFICATIONS
   */
  async notifyPaymentReceived(
    providerId: string,
    amount: number,
    payerName: string,
    serviceType: string,
    transactionId: string
  ): Promise<void> {
    await this.createNotification({
      userId: providerId,
      type: 'payment_received',
      title: 'Payment Received',
      message: `You received $${amount} from ${payerName} for ${serviceType}`,
      data: {
        transactionId,
        paymentAmount: amount,
        authorName: payerName,
        serviceType,
        actionUrl: `/provider/earnings`
      },
      priority: 'high',
      category: 'payment',
      isRead: false,
      actions: {
        primary: {
          label: 'View Earnings',
          action: 'view_earnings',
          url: `/provider/earnings`
        }
      }
    });
  }

  /**
   * Send browser notification for high priority items
   */
  private async sendBrowserNotification(notification: Omit<UnifiedNotification, 'id' | 'createdAt'>): Promise<void> {
    if ('Notification' in window && Notification.permission === 'granted') {
      try {
        const browserNotification = new Notification(notification.title, {
          body: notification.message,
          icon: notification.data?.senderAvatar || notification.data?.authorAvatar || '/favicon.png',
          badge: '/favicon.png',
          tag: `fetchly-${notification.type}`,
          requireInteraction: notification.priority === 'urgent'
        });

        // Auto close after 5 seconds unless urgent
        if (notification.priority !== 'urgent') {
          setTimeout(() => browserNotification.close(), 5000);
        }

        // Handle click
        browserNotification.onclick = () => {
          window.focus();
          if (notification.data?.actionUrl) {
            window.location.href = notification.data.actionUrl;
          }
          browserNotification.close();
        };
      } catch (error) {
        console.error('Error sending browser notification:', error);
      }
    }
  }

  /**
   * Get notifications for user
   */
  async getUserNotifications(userId: string, limit: number = 50): Promise<UnifiedNotification[]> {
    try {
      const notificationsQuery = query(
        collection(db, this.COLLECTION),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc'),
        limit(limit)
      );

      const snapshot = await getDocs(notificationsQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt.toDate()
      })) as UnifiedNotification[];
    } catch (error) {
      console.error('Error getting user notifications:', error);
      return [];
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    try {
      await updateDoc(doc(db, this.COLLECTION, notificationId), {
        isRead: true
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  /**
   * Mark all notifications as read for user
   */
  async markAllAsRead(userId: string): Promise<void> {
    try {
      const unreadQuery = query(
        collection(db, this.COLLECTION),
        where('userId', '==', userId),
        where('isRead', '==', false)
      );

      const snapshot = await getDocs(unreadQuery);
      const batch = db.batch ? db.batch() : null;

      if (batch) {
        snapshot.docs.forEach(doc => {
          batch.update(doc.ref, { isRead: true });
        });
        await batch.commit();
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }
}

export const unifiedNotificationService = new UnifiedNotificationService();
export default unifiedNotificationService;
