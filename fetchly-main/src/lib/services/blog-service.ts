import { DatabaseService, COLLECTIONS } from '../database';
import { db } from '../firebase/config';
import { 
  collection, 
  doc, 
  updateDoc, 
  increment, 
  arrayUnion, 
  arrayRemove,
  getDoc,
  addDoc,
  serverTimestamp,
  query,
  where,
  orderBy,
  limit,
  getDocs
} from 'firebase/firestore';

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: {
    name: string;
    avatar: string;
    isPro: boolean;
    specialization: string;
  };
  category: string;
  tags: string[];
  publishedAt: Date;
  readTime: number;
  likes: number;
  comments: number;
  featured: boolean;
  image: string;
  likedBy?: string[]; // Array of user IDs who liked the post
}

export interface BlogComment {
  id: string;
  postId: string;
  author: {
    name: string;
    avatar: string;
    userId: string;
  };
  content: string;
  publishedAt: Date;
  likes: number;
  likedBy?: string[]; // Array of user IDs who liked the comment
}

export class BlogService {
  // Get all blog posts
  static async getAllPosts(): Promise<BlogPost[]> {
    try {
      const posts = await DatabaseService.query(
        COLLECTIONS.BLOG_POSTS,
        [],
        'publishedAt',
        'desc'
      );

      return posts.map(this.mapFirebasePostToBlogPost);
    } catch (error) {
      console.error('Error getting blog posts:', error);
      throw new Error('Failed to get blog posts');
    }
  }

  // Get a single blog post by ID
  static async getPostById(postId: string): Promise<BlogPost | null> {
    try {
      const post = await DatabaseService.getById(COLLECTIONS.BLOG_POSTS, postId);
      if (!post) return null;

      return this.mapFirebasePostToBlogPost(post);
    } catch (error) {
      console.error('Error getting blog post:', error);
      throw new Error('Failed to get blog post');
    }
  }

  // Get featured posts
  static async getFeaturedPosts(): Promise<BlogPost[]> {
    try {
      const posts = await DatabaseService.query(
        COLLECTIONS.BLOG_POSTS,
        [{ field: 'featured', operator: '==', value: true }],
        'publishedAt',
        'desc'
      );

      return posts.map(this.mapFirebasePostToBlogPost);
    } catch (error) {
      console.error('Error getting featured posts:', error);
      throw new Error('Failed to get featured posts');
    }
  }

  // Get posts by category
  static async getPostsByCategory(category: string): Promise<BlogPost[]> {
    try {
      const posts = await DatabaseService.query(
        COLLECTIONS.BLOG_POSTS,
        [{ field: 'category', operator: '==', value: category }],
        'publishedAt',
        'desc'
      );

      return posts.map(this.mapFirebasePostToBlogPost);
    } catch (error) {
      console.error('Error getting posts by category:', error);
      throw new Error('Failed to get posts by category');
    }
  }

  // Like/Unlike a blog post
  static async togglePostLike(postId: string, userId: string): Promise<{ liked: boolean; newLikeCount: number }> {
    try {
      const postRef = doc(db, COLLECTIONS.BLOG_POSTS, postId);
      const postDoc = await getDoc(postRef);
      
      if (!postDoc.exists()) {
        throw new Error('Post not found');
      }

      const postData = postDoc.data();
      const likedBy = postData.likedBy || [];
      const isLiked = likedBy.includes(userId);

      if (isLiked) {
        // Unlike the post
        await updateDoc(postRef, {
          likes: increment(-1),
          likedBy: arrayRemove(userId)
        });
        return { liked: false, newLikeCount: (postData.likes || 0) - 1 };
      } else {
        // Like the post
        await updateDoc(postRef, {
          likes: increment(1),
          likedBy: arrayUnion(userId)
        });
        return { liked: true, newLikeCount: (postData.likes || 0) + 1 };
      }
    } catch (error) {
      console.error('Error toggling post like:', error);
      throw new Error('Failed to toggle post like');
    }
  }

  // Add a comment to a blog post
  static async addComment(postId: string, userId: string, userName: string, userAvatar: string, content: string): Promise<BlogComment> {
    try {
      const commentData = {
        postId,
        author: {
          userId,
          name: userName,
          avatar: userAvatar
        },
        content,
        publishedAt: serverTimestamp(),
        likes: 0,
        likedBy: []
      };

      const commentRef = await addDoc(collection(db, 'blogComments'), commentData);
      
      // Update post comment count
      const postRef = doc(db, COLLECTIONS.BLOG_POSTS, postId);
      await updateDoc(postRef, {
        comments: increment(1)
      });

      return {
        id: commentRef.id,
        ...commentData,
        publishedAt: new Date()
      } as BlogComment;
    } catch (error) {
      console.error('Error adding comment:', error);
      throw new Error('Failed to add comment');
    }
  }

  // Get comments for a blog post
  static async getPostComments(postId: string): Promise<BlogComment[]> {
    try {
      const commentsQuery = query(
        collection(db, 'blogComments'),
        where('postId', '==', postId),
        orderBy('publishedAt', 'desc')
      );

      const snapshot = await getDocs(commentsQuery);
      const comments: BlogComment[] = [];

      snapshot.forEach((doc) => {
        const data = doc.data();
        comments.push({
          id: doc.id,
          postId: data.postId,
          author: data.author,
          content: data.content,
          publishedAt: data.publishedAt?.toDate() || new Date(),
          likes: data.likes || 0,
          likedBy: data.likedBy || []
        });
      });

      return comments;
    } catch (error) {
      console.error('Error getting post comments:', error);
      throw new Error('Failed to get post comments');
    }
  }

  // Like/Unlike a comment
  static async toggleCommentLike(commentId: string, userId: string): Promise<{ liked: boolean; newLikeCount: number }> {
    try {
      const commentRef = doc(db, 'blogComments', commentId);
      const commentDoc = await getDoc(commentRef);
      
      if (!commentDoc.exists()) {
        throw new Error('Comment not found');
      }

      const commentData = commentDoc.data();
      const likedBy = commentData.likedBy || [];
      const isLiked = likedBy.includes(userId);

      if (isLiked) {
        // Unlike the comment
        await updateDoc(commentRef, {
          likes: increment(-1),
          likedBy: arrayRemove(userId)
        });
        return { liked: false, newLikeCount: (commentData.likes || 0) - 1 };
      } else {
        // Like the comment
        await updateDoc(commentRef, {
          likes: increment(1),
          likedBy: arrayUnion(userId)
        });
        return { liked: true, newLikeCount: (commentData.likes || 0) + 1 };
      }
    } catch (error) {
      console.error('Error toggling comment like:', error);
      throw new Error('Failed to toggle comment like');
    }
  }

  // Check if user has liked a post
  static async hasUserLikedPost(postId: string, userId: string): Promise<boolean> {
    try {
      const post = await this.getPostById(postId);
      return post?.likedBy?.includes(userId) || false;
    } catch (error) {
      console.error('Error checking if user liked post:', error);
      return false;
    }
  }

  // Helper method to map Firebase post to BlogPost interface
  private static mapFirebasePostToBlogPost(fbPost: any): BlogPost {
    return {
      id: fbPost.id,
      title: fbPost.title,
      excerpt: fbPost.excerpt,
      content: fbPost.content,
      author: fbPost.author || {
        name: 'Fetchly Team',
        avatar: '/images/fetchly-logo.png',
        isPro: true,
        specialization: 'Pet Care Experts'
      },
      category: fbPost.category,
      tags: fbPost.tags || [],
      publishedAt: fbPost.publishedAt?.toDate() || new Date(),
      readTime: fbPost.readTime || 5,
      likes: fbPost.likes || 0,
      comments: fbPost.comments || 0,
      featured: fbPost.featured || false,
      image: fbPost.image,
      likedBy: fbPost.likedBy || []
    };
  }
}
