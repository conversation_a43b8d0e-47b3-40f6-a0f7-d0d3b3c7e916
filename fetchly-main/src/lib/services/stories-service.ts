import { 
  collection, 
  query, 
  where, 
  orderBy, 
  getDocs, 
  addDoc, 
  updateDoc, 
  doc, 
  serverTimestamp,
  Timestamp,
  arrayUnion
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Story, UserStories, STORY_CONFIG, groupStoriesByUser } from '@/types/stories';

export class StoriesService {
  private static readonly COLLECTION_NAME = 'stories';

  /**
   * Fetch active stories (not expired, within 24 hours)
   */
  static async getActiveStories(): Promise<UserStories[]> {
    try {
      console.log('🔍 Fetching active stories...');

      const storiesRef = collection(db, this.COLLECTION_NAME);
      // Simple query without orderBy to avoid index requirement
      const q = query(
        storiesRef,
        where('isPublic', '==', true)
      );

      const querySnapshot = await getDocs(q);
      const stories: Story[] = [];
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - STORY_CONFIG.EXPIRY_HOURS);

      console.log(`📊 Found ${querySnapshot.docs.length} total stories`);

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const createdAt = data.createdAt?.toDate() || data.timestamp?.toDate() || new Date();

        // Filter out expired stories client-side
        if (createdAt > twentyFourHoursAgo) {
          stories.push({
            id: doc.id,
            userId: data.userId,
            userName: data.userName,
            userAvatar: data.userAvatar,
            mediaUrl: data.mediaUrl || data.image || '', // Support both field names
            type: data.type || (data.image ? 'image' : 'video'),
            content: data.content || '',
            createdAt: createdAt,
            expiresAt: data.expiresAt?.toDate() || new Date(createdAt.getTime() + (24 * 60 * 60 * 1000)),
            isPublic: data.isPublic !== false, // Default to true
            views: data.views || 0,
            viewedBy: data.viewedBy || []
          });
        }
      });

      console.log(`✅ Found ${stories.length} active stories`);
      const groupedStories = groupStoriesByUser(stories);
      console.log(`👥 Grouped into ${groupedStories.length} user stories`);

      return groupedStories;
    } catch (error) {
      console.error('Error fetching active stories:', error);
      throw new Error('Failed to fetch stories');
    }
  }

  /**
   * Fetch stories for a specific user
   */
  static async getUserStories(userId: string): Promise<Story[]> {
    try {
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - STORY_CONFIG.EXPIRY_HOURS);

      const storiesRef = collection(db, this.COLLECTION_NAME);
      const q = query(
        storiesRef,
        where('userId', '==', userId),
        where('createdAt', '>', twentyFourHoursAgo),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const stories: Story[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        stories.push({
          id: doc.id,
          userId: data.userId,
          userName: data.userName,
          userAvatar: data.userAvatar,
          mediaUrl: data.mediaUrl || data.image,
          type: data.type || (data.image ? 'image' : 'video'),
          content: data.content,
          createdAt: data.createdAt?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate() || new Date(),
          isPublic: data.isPublic,
          views: data.views || 0,
          viewedBy: data.viewedBy || []
        });
      });

      return stories;
    } catch (error) {
      console.error('Error fetching user stories:', error);
      throw new Error('Failed to fetch user stories');
    }
  }

  /**
   * Create a new story
   */
  static async createStory(storyData: {
    userId: string;
    userName: string;
    userAvatar: string;
    mediaUrl: string;
    type: 'image' | 'video';
    content?: string;
    isPublic?: boolean;
  }): Promise<string> {
    try {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + (STORY_CONFIG.EXPIRY_HOURS * 60 * 60 * 1000));

      const docRef = await addDoc(collection(db, this.COLLECTION_NAME), {
        userId: storyData.userId,
        userName: storyData.userName,
        userAvatar: storyData.userAvatar,
        mediaUrl: storyData.mediaUrl,
        type: storyData.type,
        content: storyData.content || '',
        createdAt: serverTimestamp(),
        expiresAt: expiresAt,
        isPublic: storyData.isPublic !== false, // Default to true
        views: 0,
        viewedBy: [],
        // Legacy fields for compatibility
        image: storyData.type === 'image' ? storyData.mediaUrl : null,
        timestamp: serverTimestamp(),
        isStory: true
      });

      return docRef.id;
    } catch (error) {
      console.error('Error creating story:', error);
      throw new Error('Failed to create story');
    }
  }

  /**
   * Mark a story as viewed by a user
   */
  static async markStoryAsViewed(storyId: string, viewerId: string): Promise<void> {
    try {
      const storyRef = doc(db, this.COLLECTION_NAME, storyId);
      
      await updateDoc(storyRef, {
        viewedBy: arrayUnion(viewerId),
        views: arrayUnion(viewerId).length // This won't work directly, but we'll handle it in a transaction if needed
      });
    } catch (error) {
      console.error('Error marking story as viewed:', error);
      // Don't throw error for view tracking failures
    }
  }

  /**
   * Delete expired stories (cleanup function)
   */
  static async deleteExpiredStories(): Promise<number> {
    try {
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - STORY_CONFIG.EXPIRY_HOURS);

      const storiesRef = collection(db, this.COLLECTION_NAME);
      const q = query(
        storiesRef,
        where('createdAt', '<', twentyFourHoursAgo)
      );

      const querySnapshot = await getDocs(q);
      let deletedCount = 0;

      // Note: In a production app, you'd want to use a batch delete or Cloud Function
      // for better performance and to avoid hitting rate limits
      const deletePromises = querySnapshot.docs.map(async (docSnapshot) => {
        await docSnapshot.ref.delete();
        deletedCount++;
      });

      await Promise.all(deletePromises);
      
      console.log(`Deleted ${deletedCount} expired stories`);
      return deletedCount;
    } catch (error) {
      console.error('Error deleting expired stories:', error);
      throw new Error('Failed to delete expired stories');
    }
  }

  /**
   * Get story analytics for a user
   */
  static async getStoryAnalytics(userId: string): Promise<{
    totalStories: number;
    totalViews: number;
    averageViews: number;
    topStory?: Story;
  }> {
    try {
      const stories = await this.getUserStories(userId);
      
      if (stories.length === 0) {
        return {
          totalStories: 0,
          totalViews: 0,
          averageViews: 0
        };
      }

      const totalViews = stories.reduce((sum, story) => sum + (story.views || 0), 0);
      const averageViews = totalViews / stories.length;
      const topStory = stories.reduce((top, story) => 
        (story.views || 0) > (top.views || 0) ? story : top
      );

      return {
        totalStories: stories.length,
        totalViews,
        averageViews: Math.round(averageViews * 100) / 100,
        topStory
      };
    } catch (error) {
      console.error('Error getting story analytics:', error);
      throw new Error('Failed to get story analytics');
    }
  }
}

// Export default instance
export const storiesService = StoriesService;
