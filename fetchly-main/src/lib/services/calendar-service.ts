import { collection, addDoc, query, where, getDocs, updateDoc, doc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export interface CalendarEvent {
  id?: string;
  providerId: string;
  bookingId: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  serviceName: string;
  petName: string;
  petType: string;
  location?: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

export class CalendarService {
  /**
   * Add a booking to the provider's calendar
   */
  static async addBookingToCalendar(
    providerId: string,
    bookingId: string,
    bookingData: {
      customerName: string;
      customerEmail: string;
      customerPhone?: string;
      serviceName: string;
      petName: string;
      petType: string;
      scheduledDate: string;
      scheduledTime: string;
      duration?: number;
      notes?: string;
    }
  ): Promise<string> {
    try {
      console.log('📅 Adding booking to calendar:', { providerId, bookingId });

      // Parse the scheduled date and time
      const [year, month, day] = bookingData.scheduledDate.split('-').map(Number);
      const [hours, minutes] = bookingData.scheduledTime.split(':').map(Number);
      
      const startDate = new Date(year, month - 1, day, hours, minutes);
      const endDate = new Date(startDate.getTime() + (bookingData.duration || 60) * 60000); // Default 1 hour

      const calendarEvent: CalendarEvent = {
        providerId,
        bookingId,
        title: `${bookingData.serviceName} - ${bookingData.petName}`,
        description: `
Service: ${bookingData.serviceName}
Pet: ${bookingData.petName} (${bookingData.petType})
Customer: ${bookingData.customerName}
Email: ${bookingData.customerEmail}
${bookingData.customerPhone ? `Phone: ${bookingData.customerPhone}` : ''}
${bookingData.notes ? `Notes: ${bookingData.notes}` : ''}
        `.trim(),
        startDate,
        endDate,
        customerName: bookingData.customerName,
        customerEmail: bookingData.customerEmail,
        customerPhone: bookingData.customerPhone,
        serviceName: bookingData.serviceName,
        petName: bookingData.petName,
        petType: bookingData.petType,
        status: 'scheduled',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const docRef = await addDoc(collection(db, 'calendar_events'), calendarEvent);
      console.log('✅ Calendar event created:', docRef.id);
      
      return docRef.id;
    } catch (error) {
      console.error('❌ Error adding booking to calendar:', error);
      throw new Error('Failed to add booking to calendar');
    }
  }

  /**
   * Get all calendar events for a provider
   */
  static async getProviderCalendarEvents(providerId: string): Promise<CalendarEvent[]> {
    try {
      const q = query(
        collection(db, 'calendar_events'),
        where('providerId', '==', providerId)
      );

      const querySnapshot = await getDocs(q);
      const events: CalendarEvent[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        events.push({
          id: doc.id,
          ...data,
          startDate: data.startDate.toDate(),
          endDate: data.endDate.toDate(),
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate()
        } as CalendarEvent);
      });

      return events.sort((a, b) => a.startDate.getTime() - b.startDate.getTime());
    } catch (error) {
      console.error('❌ Error fetching calendar events:', error);
      throw new Error('Failed to fetch calendar events');
    }
  }

  /**
   * Update calendar event status
   */
  static async updateEventStatus(
    eventId: string,
    status: CalendarEvent['status']
  ): Promise<void> {
    try {
      const eventRef = doc(db, 'calendar_events', eventId);
      await updateDoc(eventRef, {
        status,
        updatedAt: new Date()
      });

      console.log('✅ Calendar event status updated:', { eventId, status });
    } catch (error) {
      console.error('❌ Error updating calendar event status:', error);
      throw new Error('Failed to update calendar event status');
    }
  }

  /**
   * Cancel a calendar event (when booking is cancelled)
   */
  static async cancelCalendarEvent(bookingId: string): Promise<void> {
    try {
      const q = query(
        collection(db, 'calendar_events'),
        where('bookingId', '==', bookingId)
      );

      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        const eventDoc = querySnapshot.docs[0];
        await updateDoc(eventDoc.ref, {
          status: 'cancelled',
          updatedAt: new Date()
        });

        console.log('✅ Calendar event cancelled for booking:', bookingId);
      }
    } catch (error) {
      console.error('❌ Error cancelling calendar event:', error);
      throw new Error('Failed to cancel calendar event');
    }
  }

  /**
   * Complete a calendar event (when service is completed)
   */
  static async completeCalendarEvent(bookingId: string): Promise<void> {
    try {
      const q = query(
        collection(db, 'calendar_events'),
        where('bookingId', '==', bookingId)
      );

      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        const eventDoc = querySnapshot.docs[0];
        await updateDoc(eventDoc.ref, {
          status: 'completed',
          updatedAt: new Date()
        });

        console.log('✅ Calendar event completed for booking:', bookingId);
      }
    } catch (error) {
      console.error('❌ Error completing calendar event:', error);
      throw new Error('Failed to complete calendar event');
    }
  }

  /**
   * Get calendar events for a specific date range
   */
  static async getEventsInDateRange(
    providerId: string,
    startDate: Date,
    endDate: Date
  ): Promise<CalendarEvent[]> {
    try {
      const q = query(
        collection(db, 'calendar_events'),
        where('providerId', '==', providerId)
      );

      const querySnapshot = await getDocs(q);
      const events: CalendarEvent[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const event = {
          id: doc.id,
          ...data,
          startDate: data.startDate.toDate(),
          endDate: data.endDate.toDate(),
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate()
        } as CalendarEvent;

        // Filter by date range
        if (event.startDate >= startDate && event.startDate <= endDate) {
          events.push(event);
        }
      });

      return events.sort((a, b) => a.startDate.getTime() - b.startDate.getTime());
    } catch (error) {
      console.error('❌ Error fetching events in date range:', error);
      throw new Error('Failed to fetch events in date range');
    }
  }
}
