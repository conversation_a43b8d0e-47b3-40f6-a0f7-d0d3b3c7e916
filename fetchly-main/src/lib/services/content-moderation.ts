import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  query, 
  where, 
  getDocs, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { ContentFlag, UserSuspension } from '@/types/chat';

// Comprehensive bad words list (you can expand this)
const BAD_WORDS = [
  // English profanity
  'fuck', 'shit', 'damn', 'bitch', 'asshole', 'bastard', 'crap', 'piss',
  'hell', 'stupid', 'idiot', 'moron', 'retard', 'gay', 'fag', 'nigger',
  'whore', 'slut', 'cunt', 'dick', 'cock', 'pussy', 'tits', 'ass',
  
  // Spanish profanity
  'puta', 'mierda', 'joder', 'coño', 'cabrón', 'pendejo', 'culero',
  'mamón', 'pinche', 'chingar', 'verga', 'puto', 'maricón', 'hijo de puta',
  
  // Variations and leetspeak
  'f*ck', 'sh*t', 'b*tch', 'a$$hole', 'f**k', 'sh**', 'fck', 'sht',
  'btch', 'fuk', 'shyt', 'azz', 'phuck', 'shiit', 'beatch',
  
  // Hate speech and discriminatory terms
  'nazi', 'hitler', 'terrorist', 'kill yourself', 'kys', 'die',
  'suicide', 'rape', 'murder', 'bomb', 'gun', 'weapon',
  
  // Spam and inappropriate
  'viagra', 'casino', 'porn', 'sex', 'nude', 'naked', 'xxx',
  'adult', 'escort', 'prostitute', 'drug', 'cocaine', 'weed'
];

// Severity levels for different words
const WORD_SEVERITY = {
  high: ['nigger', 'kill yourself', 'kys', 'rape', 'murder', 'terrorist', 'nazi'],
  medium: ['fuck', 'shit', 'bitch', 'asshole', 'cunt', 'whore', 'slut'],
  low: ['damn', 'crap', 'hell', 'stupid', 'idiot']
};

class ContentModerationService {
  private readonly COLLECTIONS = {
    FLAGS: 'content_flags',
    SUSPENSIONS: 'user_suspensions',
    MODERATION_LOG: 'moderation_log'
  };

  /**
   * Check content for bad words and profanity
   */
  checkContent(content: string, depth: number = 0): {
    isClean: boolean;
    flaggedWords: string[];
    severity: 'low' | 'medium' | 'high';
    score: number;
  } {
    const normalizedContent = content.toLowerCase()
      .replace(/[^a-zA-Z0-9\s]/g, '') // Remove special characters
      .replace(/\s+/g, ' ') // Normalize spaces
      .trim();

    const words = normalizedContent.split(' ');
    const flaggedWords: string[] = [];
    let maxSeverity: 'low' | 'medium' | 'high' = 'low';
    let score = 0;

    // Check each word against bad words list
    for (const word of words) {
      for (const badWord of BAD_WORDS) {
        if (word.includes(badWord) || this.isVariation(word, badWord)) {
          flaggedWords.push(badWord);
          
          // Determine severity
          if (WORD_SEVERITY.high.includes(badWord)) {
            maxSeverity = 'high';
            score += 10;
          } else if (WORD_SEVERITY.medium.includes(badWord)) {
            if (maxSeverity !== 'high') maxSeverity = 'medium';
            score += 5;
          } else {
            if (maxSeverity === 'low') maxSeverity = 'low';
            score += 1;
          }
        }
      }
    }

    // Check for leetspeak and character substitution (prevent infinite recursion)
    if (depth < 2) {
      const leetContent = this.convertFromLeetspeak(normalizedContent);
      if (leetContent !== normalizedContent) {
        const leetCheck = this.checkContent(leetContent, depth + 1);
        flaggedWords.push(...leetCheck.flaggedWords);
        score += leetCheck.score;
        if (leetCheck.severity === 'high') maxSeverity = 'high';
        else if (leetCheck.severity === 'medium' && maxSeverity !== 'high') maxSeverity = 'medium';
      }
    }

    return {
      isClean: flaggedWords.length === 0,
      flaggedWords: [...new Set(flaggedWords)], // Remove duplicates
      severity: maxSeverity,
      score
    };
  }

  /**
   * Check if a word is a variation of a bad word
   */
  private isVariation(word: string, badWord: string): boolean {
    // Check for character repetition (e.g., "fuuuck")
    const normalizedWord = word.replace(/(.)\1+/g, '$1');
    if (normalizedWord === badWord) return true;

    // Check for character substitution
    const substitutions: { [key: string]: string[] } = {
      'a': ['@', '4'],
      'e': ['3'],
      'i': ['1', '!'],
      'o': ['0'],
      's': ['$', '5'],
      't': ['7'],
      'g': ['9']
    };

    let variations = [badWord];
    for (const [letter, subs] of Object.entries(substitutions)) {
      const currentVariations = [...variations]; // Create a copy to avoid infinite loop
      for (const variation of currentVariations) {
        for (const sub of subs) {
          const newVariation = variation.replace(new RegExp(letter, 'g'), sub);
          if (!variations.includes(newVariation) && variations.length < 1000) { // Safety limit
            variations.push(newVariation);
          }
        }
      }
    }

    return variations.includes(word);
  }

  /**
   * Convert leetspeak to normal text
   */
  private convertFromLeetspeak(text: string): string {
    const leetMap: { [key: string]: string } = {
      '4': 'a', '@': 'a', '3': 'e', '1': 'i', '!': 'i',
      '0': 'o', '$': 's', '5': 's', '7': 't', '9': 'g'
    };

    let converted = text;
    for (const [leet, normal] of Object.entries(leetMap)) {
      converted = converted.replace(new RegExp(leet, 'g'), normal);
    }
    return converted;
  }

  /**
   * Flag content and take appropriate action
   */
  async flagContent(
    userId: string,
    content: string,
    contentType: 'message' | 'post' | 'comment',
    contentId: string
  ): Promise<{
    action: 'warning' | 'deleted' | 'suspended';
    flagCount: number;
    message: string;
  }> {
    const moderationResult = this.checkContent(content);
    
    if (moderationResult.isClean) {
      return { action: 'warning', flagCount: 0, message: 'Content is clean' };
    }

    // Create content flag record
    const flagData: Omit<ContentFlag, 'id'> = {
      userId,
      content,
      contentType,
      contentId,
      flaggedWords: moderationResult.flaggedWords,
      flagCount: await this.getUserFlagCount(userId) + 1,
      timestamp: new Date(),
      action: 'warning'
    };

    // Determine action based on severity and user history
    const userFlagCount = flagData.flagCount;
    let action: 'warning' | 'deleted' | 'suspended' = 'warning';

    if (moderationResult.severity === 'high' || userFlagCount >= 3) {
      action = 'suspended';
      await this.suspendUser(userId, userFlagCount, moderationResult.flaggedWords.join(', '));
    } else if (moderationResult.severity === 'medium' || userFlagCount >= 2) {
      action = 'deleted';
    }

    flagData.action = action;

    // Save flag record
    await addDoc(collection(db, this.COLLECTIONS.FLAGS), {
      ...flagData,
      timestamp: Timestamp.fromDate(flagData.timestamp)
    });

    // Log moderation action
    await this.logModerationAction(userId, action, moderationResult.flaggedWords, content);

    const messages = {
      warning: `Your content contains inappropriate language. Please keep discussions respectful. (Warning ${userFlagCount}/3)`,
      deleted: `Your content has been deleted for containing inappropriate language. (Warning ${userFlagCount}/3)`,
      suspended: `Your account has been suspended for repeated violations of our community guidelines.`
    };

    return {
      action,
      flagCount: userFlagCount,
      message: messages[action]
    };
  }

  /**
   * Get user's flag count
   */
  private async getUserFlagCount(userId: string): Promise<number> {
    const flagsQuery = query(
      collection(db, this.COLLECTIONS.FLAGS),
      where('userId', '==', userId)
    );
    
    const snapshot = await getDocs(flagsQuery);
    return snapshot.size;
  }

  /**
   * Suspend user for violations
   */
  private async suspendUser(userId: string, flagCount: number, reason: string): Promise<void> {
    const suspensionDuration = this.getSuspensionDuration(flagCount);
    const suspendedUntil = new Date();
    suspendedUntil.setDate(suspendedUntil.getDate() + suspensionDuration);

    const suspensionData: Omit<UserSuspension, 'id'> = {
      userId,
      reason: `Inappropriate language: ${reason}`,
      suspendedAt: new Date(),
      suspendedUntil,
      flagCount,
      isActive: true
    };

    await addDoc(collection(db, this.COLLECTIONS.SUSPENSIONS), {
      ...suspensionData,
      suspendedAt: Timestamp.fromDate(suspensionData.suspendedAt),
      suspendedUntil: Timestamp.fromDate(suspensionData.suspendedUntil)
    });
  }

  /**
   * Get suspension duration based on flag count
   */
  private getSuspensionDuration(flagCount: number): number {
    if (flagCount >= 5) return 30; // 30 days
    if (flagCount >= 4) return 14; // 14 days
    return 5; // 5 days for first suspension
  }

  /**
   * Check if user is currently suspended
   */
  async isUserSuspended(userId: string): Promise<{
    isSuspended: boolean;
    suspendedUntil?: Date;
    reason?: string;
  }> {
    const suspensionsQuery = query(
      collection(db, this.COLLECTIONS.SUSPENSIONS),
      where('userId', '==', userId),
      where('isActive', '==', true),
      orderBy('suspendedAt', 'desc'),
      limit(1)
    );

    const snapshot = await getDocs(suspensionsQuery);
    
    if (snapshot.empty) {
      return { isSuspended: false };
    }

    const suspension = snapshot.docs[0].data();
    const suspendedUntil = suspension.suspendedUntil.toDate();
    
    if (suspendedUntil > new Date()) {
      return {
        isSuspended: true,
        suspendedUntil,
        reason: suspension.reason
      };
    } else {
      // Suspension expired, deactivate it
      await updateDoc(snapshot.docs[0].ref, { isActive: false });
      return { isSuspended: false };
    }
  }

  /**
   * Log moderation action
   */
  private async logModerationAction(
    userId: string,
    action: string,
    flaggedWords: string[],
    content: string
  ): Promise<void> {
    await addDoc(collection(db, this.COLLECTIONS.MODERATION_LOG), {
      userId,
      action,
      flaggedWords,
      content: content.substring(0, 100), // Store first 100 chars for reference
      timestamp: Timestamp.now(),
      moderatedBy: 'system'
    });
  }

  /**
   * Clean content by replacing bad words with asterisks
   */
  cleanContent(content: string): string {
    let cleanedContent = content;
    const moderationResult = this.checkContent(content);
    
    for (const badWord of moderationResult.flaggedWords) {
      const regex = new RegExp(badWord, 'gi');
      const replacement = '*'.repeat(badWord.length);
      cleanedContent = cleanedContent.replace(regex, replacement);
    }
    
    return cleanedContent;
  }
}

export const contentModerationService = new ContentModerationService();
export default contentModerationService;
