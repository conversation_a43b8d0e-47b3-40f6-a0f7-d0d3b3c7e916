// Simple Firebase token verification without admin SDK
// This replaces the admin SDK functionality for basic token verification

import { auth, db } from './firebase/config';

export async function verifyIdToken(token: string) {
  try {
    // For client-side verification, we can use the current user's token
    // This is a simplified approach that doesn't require Firebase Admin SDK
    const currentUser = auth.currentUser;

    if (!currentUser) {
      throw new Error('No authenticated user');
    }

    // Get fresh token and compare
    const currentToken = await currentUser.getIdToken();

    if (token !== currentToken) {
      throw new Error('Token mismatch');
    }

    return {
      uid: currentUser.uid,
      email: currentUser.email,
      email_verified: currentUser.emailVerified
    };
  } catch (error) {
    throw new Error('Token verification failed');
  }
}

// Export a simple auth object for compatibility
export const adminAuth = {
  verifyIdToken
};

// Export db as adminDb for compatibility
export const adminDb = db;
