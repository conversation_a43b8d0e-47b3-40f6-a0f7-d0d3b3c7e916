'use client';

import { io, Socket } from 'socket.io-client';

export interface Message {
  id: string;
  chatRoomId: string;
  senderId: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'system';
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  createdAt: string;
  sender: {
    id: string;
    name: string;
    avatar?: string;
  };
  readBy: string[];
}

export interface ChatRoom {
  id: string;
  type: 'direct' | 'group' | 'support';
  name?: string;
  description?: string;
  participants: string[];
  lastMessageAt?: string;
  isActive: boolean;
}

export interface TypingUser {
  userId: string;
  userName: string;
}

export class SocketClient {
  private socket: Socket | null = null;
  private token: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  // Event handlers
  private onConnectHandler?: () => void;
  private onDisconnectHandler?: () => void;
  private onMessageHandler?: (message: Message) => void;
  private onRoomMessagesHandler?: (data: { roomId: string; messages: Message[] }) => void;
  private onTypingHandler?: (data: TypingUser) => void;
  private onStoppedTypingHandler?: (data: { userId: string }) => void;
  private onErrorHandler?: (error: string) => void;
  private onNotificationHandler?: (notification: any) => void;

  constructor() {
    // Auto-reconnect on page visibility change
    if (typeof window !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden && this.token && !this.isConnected()) {
          this.connect(this.token);
        }
      });
    }
  }

  public connect(token: string): void {
    this.token = token;

    if (this.socket?.connected) {
      return;
    }

    const socketUrl = process.env.NODE_ENV === 'development' 
      ? 'http://localhost:3002' 
      : 'https://yourdomain.com';

    this.socket = io(socketUrl, {
      auth: { token },
      transports: ['websocket', 'polling'],
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
    });

    this.setupEventListeners();
  }

  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.token = null;
    this.reconnectAttempts = 0;
  }

  public isConnected(): boolean {
    return this.socket?.connected || false;
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to chat server');
      this.reconnectAttempts = 0;
      this.onConnectHandler?.();
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from chat server:', reason);
      this.onDisconnectHandler?.();
    });

    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      this.reconnectAttempts++;
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        this.onErrorHandler?.('Failed to connect to chat server');
      }
    });

    this.socket.on('new_message', (message: Message) => {
      this.onMessageHandler?.(message);
    });

    this.socket.on('room_messages', (data: { roomId: string; messages: Message[] }) => {
      this.onRoomMessagesHandler?.(data);
    });

    this.socket.on('user_typing', (data: TypingUser) => {
      this.onTypingHandler?.(data);
    });

    this.socket.on('user_stopped_typing', (data: { userId: string }) => {
      this.onStoppedTypingHandler?.(data);
    });

    this.socket.on('notification', (notification: any) => {
      this.onNotificationHandler?.(notification);
    });

    this.socket.on('error', (error: { message: string }) => {
      this.onErrorHandler?.(error.message);
    });
  }

  // Room management
  public joinRoom(roomId: string): void {
    this.socket?.emit('join_room', roomId);
  }

  public leaveRoom(roomId: string): void {
    this.socket?.emit('leave_room', roomId);
  }

  public createRoom(data: {
    type: 'direct' | 'group';
    participants: string[];
    name?: string;
    description?: string;
  }): void {
    this.socket?.emit('create_room', data);
  }

  // Message management
  public sendMessage(data: {
    roomId: string;
    content: string;
    type?: 'text' | 'image' | 'file';
    fileUrl?: string;
    fileName?: string;
    fileSize?: number;
  }): void {
    this.socket?.emit('send_message', data);
  }

  public markMessagesAsRead(roomId: string, messageIds: string[]): void {
    this.socket?.emit('mark_messages_read', { roomId, messageIds });
  }

  // Typing indicators
  public startTyping(roomId: string): void {
    this.socket?.emit('typing_start', { roomId });
  }

  public stopTyping(roomId: string): void {
    this.socket?.emit('typing_stop', { roomId });
  }

  // Event handler setters
  public onConnect(handler: () => void): void {
    this.onConnectHandler = handler;
  }

  public onDisconnect(handler: () => void): void {
    this.onDisconnectHandler = handler;
  }

  public onMessage(handler: (message: Message) => void): void {
    this.onMessageHandler = handler;
  }

  public onRoomMessages(handler: (data: { roomId: string; messages: Message[] }) => void): void {
    this.onRoomMessagesHandler = handler;
  }

  public onTyping(handler: (data: TypingUser) => void): void {
    this.onTypingHandler = handler;
  }

  public onStoppedTyping(handler: (data: { userId: string }) => void): void {
    this.onStoppedTypingHandler = handler;
  }

  public onError(handler: (error: string) => void): void {
    this.onErrorHandler = handler;
  }

  public onNotification(handler: (notification: any) => void): void {
    this.onNotificationHandler = handler;
  }
}

// Singleton instance
let socketClient: SocketClient | null = null;

export function getSocketClient(): SocketClient {
  if (!socketClient) {
    socketClient = new SocketClient();
  }
  return socketClient;
}

// React hook for using socket client
export function useSocket() {
  const client = getSocketClient();
  
  return {
    connect: client.connect.bind(client),
    disconnect: client.disconnect.bind(client),
    isConnected: client.isConnected.bind(client),
    joinRoom: client.joinRoom.bind(client),
    leaveRoom: client.leaveRoom.bind(client),
    createRoom: client.createRoom.bind(client),
    sendMessage: client.sendMessage.bind(client),
    markMessagesAsRead: client.markMessagesAsRead.bind(client),
    startTyping: client.startTyping.bind(client),
    stopTyping: client.stopTyping.bind(client),
    onConnect: client.onConnect.bind(client),
    onDisconnect: client.onDisconnect.bind(client),
    onMessage: client.onMessage.bind(client),
    onRoomMessages: client.onRoomMessages.bind(client),
    onTyping: client.onTyping.bind(client),
    onStoppedTyping: client.onStoppedTyping.bind(client),
    onError: client.onError.bind(client),
    onNotification: client.onNotification.bind(client),
  };
}
