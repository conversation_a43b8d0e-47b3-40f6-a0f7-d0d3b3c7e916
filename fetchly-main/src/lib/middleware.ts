import { NextRequest, NextResponse } from 'next/server';
import { verifyToken, getUserById, JWTPayload } from './auth';
import { auth } from './firebase/admin-config';
import {
  securityHeaders,
  corsConfig,
  logSecurityEvent,
  validateAndSanitizeInput,
  isAdminIPWhitelisted
} from './security';

// Re-export securityHeaders for convenience
export { securityHeaders };

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string;
    email: string;
    name: string;
    role: string;
    verified: boolean;
  };
}

// Authentication middleware
export async function authenticateRequest(request: NextRequest): Promise<{
  success: boolean;
  user?: any;
  error?: string;
}> {
  try {
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { success: false, error: 'No valid authorization header' };
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    let user;

    try {
      // Try Firebase ID token verification first
      const decodedToken = await auth.verifyIdToken(token);
      user = await getUserById(decodedToken.uid);
    } catch (firebaseError) {
      try {
        // Fallback to JWT token verification
        const payload: JWTPayload = verifyToken(token);
        user = await getUserById(payload.userId);
      } catch (jwtError) {
        console.error('Both Firebase and JWT token verification failed:', { firebaseError, jwtError });
        return { success: false, error: 'Invalid or expired token' };
      }
    }

    if (!user) {
      return { success: false, error: 'User not found' };
    }

    return { success: true, user };
  } catch (error) {
    console.error('Authentication error:', error);
    return { success: false, error: 'Invalid or expired token' };
  }
}

// Role-based authorization middleware
export function requireRole(allowedRoles: string[]) {
  return (user: any) => {
    if (!user) {
      throw new Error('Authentication required');
    }
    
    if (!allowedRoles.includes(user.role)) {
      throw new Error('Insufficient permissions');
    }
    
    return true;
  };
}

// Middleware wrapper for API routes
export function withAuth(
  handler: (req: NextRequest, context: any) => Promise<NextResponse>,
  options: {
    roles?: string[];
    requireVerified?: boolean;
  } = {}
) {
  return async (req: NextRequest, context: any) => {
    try {
      const authResult = await authenticateRequest(req);
      
      if (!authResult.success) {
        return NextResponse.json(
          { error: authResult.error },
          { status: 401 }
        );
      }
      
      const user = authResult.user;
      
      // Check role requirements
      if (options.roles && !options.roles.includes(user.role)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        );
      }
      
      // Check verification requirements
      if (options.requireVerified && !user.verified) {
        return NextResponse.json(
          { error: 'Email verification required' },
          { status: 403 }
        );
      }
      
      // Add user to request object
      (req as any).user = user;
      
      return handler(req, context);
    } catch (error) {
      console.error('Auth middleware error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

// Rate limiting middleware
const rateLimitMap = new Map();

export function rateLimit(options: {
  windowMs: number;
  maxRequests: number;
  keyGenerator?: (req: NextRequest) => string;
}) {
  return (req: NextRequest) => {
    const key = options.keyGenerator 
      ? options.keyGenerator(req)
      : req.ip || 'anonymous';
    
    const now = Date.now();
    const windowStart = now - options.windowMs;
    
    // Get or create rate limit data for this key
    if (!rateLimitMap.has(key)) {
      rateLimitMap.set(key, []);
    }
    
    const requests = rateLimitMap.get(key);
    
    // Remove old requests outside the window
    const validRequests = requests.filter((timestamp: number) => timestamp > windowStart);
    
    // Check if limit exceeded
    if (validRequests.length >= options.maxRequests) {
      throw new Error('Rate limit exceeded');
    }
    
    // Add current request
    validRequests.push(now);
    rateLimitMap.set(key, validRequests);
    
    return true;
  };
}



// Enhanced CORS middleware
export function corsHeaders(origin?: string) {
  const allowedOrigins = Array.isArray(corsConfig.origin) ? corsConfig.origin : [corsConfig.origin];
  const isAllowedOrigin = !origin || allowedOrigins.includes(origin) || corsConfig.origin === '*';

  return {
    'Access-Control-Allow-Origin': isAllowedOrigin ? (origin || '*') : 'null',
    'Access-Control-Allow-Methods': corsConfig.methods.join(', '),
    'Access-Control-Allow-Headers': corsConfig.allowedHeaders.join(', '),
    'Access-Control-Allow-Credentials': corsConfig.credentials.toString(),
    'Access-Control-Max-Age': corsConfig.maxAge.toString(),
  };
}

// Enhanced security headers middleware
export function getSecurityHeaders() {
  return securityHeaders;
}

// Admin access middleware
export function requireAdminAccess() {
  return (req: NextRequest) => {
    const ip = req.ip || req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';

    if (!isAdminIPWhitelisted(ip)) {
      logSecurityEvent({
        type: 'admin_access',
        ip,
        userAgent: req.headers.get('user-agent') || undefined,
        details: { path: req.nextUrl.pathname }
      });
      throw new Error('Access denied: IP not whitelisted for admin access');
    }

    return true;
  };
}

// Input validation middleware
export function validateInput(schema: Record<string, {
  type: 'string' | 'email' | 'phone' | 'uuid' | 'date' | 'number';
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
}>) {
  return async (req: NextRequest) => {
    try {
      const body = await req.json();
      const validatedData: Record<string, any> = {};
      const errors: Record<string, string[]> = {};

      for (const [field, rules] of Object.entries(schema)) {
        const validation = validateAndSanitizeInput(body[field], rules.type, rules);

        if (!validation.isValid) {
          errors[field] = validation.errors;
        } else {
          validatedData[field] = validation.value;
        }
      }

      if (Object.keys(errors).length > 0) {
        return NextResponse.json(
          { error: 'Validation failed', details: errors },
          { status: 400 }
        );
      }

      // Attach validated data to request
      (req as any).validatedData = validatedData;
      return null; // Continue to next middleware
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }
  };
}

// Apply all middleware to a handler
export function withMiddleware(
  handler: Function,
  options: {
    auth?: boolean;
    roles?: string[];
    requireVerified?: boolean;
    rateLimit?: boolean;
    validation?: Record<string, any>;
    adminOnly?: boolean;
  } = {}
) {
  return async (req: NextRequest) => {
    try {
      const origin = req.headers.get('origin');
      const ip = req.ip || req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';

      // Apply CORS headers
      const corsHeadersObj = corsHeaders(origin);

      // Handle preflight requests
      if (req.method === 'OPTIONS') {
        return new NextResponse(null, {
          status: 200,
          headers: {
            ...corsHeadersObj,
            ...getSecurityHeaders()
          }
        });
      }

      // Apply admin access check if required
      if (options.adminOnly) {
        requireAdminAccess()(req);
      }

      // Apply rate limiting if enabled
      if (options.rateLimit) {
        await applyRateLimit(req);
      }

      // Apply input validation if schema provided
      if (options.validation && req.method !== 'GET') {
        const validationResult = await validateInput(options.validation)(req);
        if (validationResult) {
          return validationResult; // Return validation error response
        }
      }

      // Apply authentication if required
      if (options.auth) {
        const authResult = await withAuth(handler, {
          roles: options.roles,
          requireVerified: options.requireVerified,
        })(req);

        // Add security headers to response
        if (authResult instanceof NextResponse) {
          Object.entries(getSecurityHeaders()).forEach(([key, value]) => {
            authResult.headers.set(key, value);
          });
          Object.entries(corsHeadersObj).forEach(([key, value]) => {
            authResult.headers.set(key, value);
          });
        }

        return authResult;
      }

      // Call the handler directly if no auth required
      const response = await handler(req);

      // Add security headers to response
      if (response instanceof NextResponse) {
        Object.entries(getSecurityHeaders()).forEach(([key, value]) => {
          response.headers.set(key, value);
        });
        Object.entries(corsHeadersObj).forEach(([key, value]) => {
          response.headers.set(key, value);
        });
      }

      return response;
    } catch (error) {
      console.error('Middleware error:', error);

      // Log security events
      const ip = req.ip || req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';

      if (error instanceof Error) {
        if (error.message.includes('rate limit')) {
          logSecurityEvent({
            type: 'rate_limit',
            ip,
            userAgent: req.headers.get('user-agent') || undefined,
            details: { path: req.nextUrl.pathname }
          });
        } else if (error.message.includes('Access denied')) {
          logSecurityEvent({
            type: 'auth_failure',
            ip,
            userAgent: req.headers.get('user-agent') || undefined,
            details: { path: req.nextUrl.pathname, error: error.message }
          });
        }
      }

      const errorResponse = NextResponse.json(
        { error: error instanceof Error ? error.message : 'Internal server error' },
        {
          status: error instanceof Error && error.message.includes('rate limit') ? 429 :
                 error instanceof Error && error.message.includes('Access denied') ? 403 : 500
        }
      );

      // Add security headers even to error responses
      Object.entries(getSecurityHeaders()).forEach(([key, value]) => {
        errorResponse.headers.set(key, value);
      });

      return errorResponse;
    }
  };
}
