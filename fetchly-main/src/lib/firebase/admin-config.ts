import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';

// Initialize Firebase Admin SDK only when needed
let app: any = null;

function getFirebaseAdminApp() {
  if (app) return app;

  const existingApps = getApps();
  if (existingApps.length > 0) {
    app = existingApps[0];
    return app;
  }

  // Only initialize if we have the required environment variables
  if (!process.env.FIREBASE_ADMIN_PROJECT_ID ||
      !process.env.FIREBASE_ADMIN_CLIENT_EMAIL ||
      !process.env.FIREBASE_ADMIN_PRIVATE_KEY) {
    console.warn('Firebase Admin environment variables not found');
    return null;
  }

  // Handle private key properly - it might be base64 encoded or have escaped newlines
  let privateKey = process.env.FIREBASE_ADMIN_PRIVATE_KEY;

  try {
    // If it's base64 encoded, decode it
    if (privateKey && !privateKey.includes('-----BEGIN')) {
      privateKey = Buffer.from(privateKey, 'base64').toString('utf8');
    }

    // Replace escaped newlines
    if (privateKey) {
      privateKey = privateKey.replace(/\\n/g, '\n');
    }
  } catch (error) {
    console.error('Error processing private key:', error);
  }

  const firebaseAdminConfig = {
    credential: cert({
      projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
      clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
      privateKey: privateKey,
    }),
    projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  };

  app = initializeApp(firebaseAdminConfig);
  return app;
}

// Export lazy-loaded instances with error handling
export const auth = (() => {
  try {
    const app = getFirebaseAdminApp();
    if (!app) {
      console.error('Firebase Admin app not initialized - check environment variables');
      return null;
    }
    return getAuth(app);
  } catch (error) {
    console.error('Error initializing Firebase Admin Auth:', error);
    return null;
  }
})();

export const adminDb = (() => {
  try {
    const app = getFirebaseAdminApp();
    if (!app) {
      console.error('Firebase Admin app not initialized - check environment variables');
      return null;
    }
    return getFirestore(app);
  } catch (error) {
    console.error('Error initializing Firebase Admin Firestore:', error);
    return null;
  }
})();

export default getFirebaseAdminApp();
