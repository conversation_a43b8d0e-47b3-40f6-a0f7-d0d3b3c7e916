import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { db } from './config';
import { parseLocation, getCityCoordinates, filterProvidersByDistance, type Coordinates } from '../utils/geolocation';

export interface Provider {
  id?: string;
  userId: string; // Link to auth user

  // Basic Info
  businessName: string;
  ownerName: string;
  email: string;
  phone: string;
  serviceType: string;
  website?: string;
  hasStore?: boolean;

  // Location
  address: string;
  city: string;
  state: string;
  zipCode: string;
  coordinates?: {
    lat: number;
    lng: number;
  };

  // Business Details
  description: string;
  experience: string;
  specialties: string[];

  // Status & Verification
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  verified: boolean;
  featured: boolean;

  // Business Hours
  businessHours: {
    [key: string]: { open: string; close: string; closed: boolean };
  };

  // Documents
  documents: {
    businessLicense?: string;
    insurance?: string;
    certifications?: string[];
  };

  // Media
  profilePhoto?: string;
  bannerPhoto?: string;
  businessPhotos: string[];

  // Stats (live data)
  rating: number;
  reviewCount: number;
  totalBookings: number;
  totalRevenue: number;
  completionRate: number;
  responseTime: string;
  responseRate: number;

  // Membership & Subscription
  membershipTier: 'free' | 'pro';
  membershipExpiry?: Timestamp;
  fetchPoints: number;
  commissionsaved: number;

  // Social Media
  socialMedia: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
  };

  // Settings
  settings: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    bookingNotifications: boolean;
    marketingEmails: boolean;
    autoAcceptBookings: boolean;
    requireDeposit: boolean;
    cancellationPolicy: string;
  };

  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
  lastActive?: Timestamp;
}

export interface Service {
  id?: string;
  providerId: string;
  name: string;
  category: string;
  description: string;
  price: number;
  duration: number; // in minutes
  active: boolean;
  petTypes: string[];
  requirements?: string[];
  addOns?: { name: string; price: number }[];
  images: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Booking {
  id?: string;
  providerId: string;
  customerId: string;
  serviceId: string;
  
  // Pet & Customer Info
  petName: string;
  petType: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  
  // Booking Details
  date: Timestamp;
  startTime: string;
  endTime: string;
  duration: number;
  status: 'pending' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled' | 'no-show';
  
  // Pricing
  basePrice: number;
  addOns: { name: string; price: number }[];
  totalAmount: number;
  platformFee: number;
  providerEarnings: number;
  
  // Additional Info
  notes?: string;
  address?: string;
  specialRequests?: string;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Earning {
  id?: string;
  providerId: string;
  bookingId: string;
  
  // Amounts
  grossAmount: number;
  platformFee: number;
  netAmount: number;
  
  // Status
  status: 'pending' | 'available' | 'paid';
  payoutId?: string;
  
  // Timestamps
  earnedAt: Timestamp;
  availableAt: Timestamp;
  paidAt?: Timestamp;
}

export interface Payout {
  id?: string;
  providerId: string;
  
  // Amount Details
  totalAmount: number;
  earningIds: string[];
  
  // Payout Method
  payoutMethod: 'bank' | 'paypal';
  payoutDetails: {
    accountId: string;
    accountName: string;
  };
  
  // Status
  status: 'pending' | 'processing' | 'completed' | 'failed';
  
  // Timestamps
  requestedAt: Timestamp;
  processedAt?: Timestamp;
  completedAt?: Timestamp;
}

// Provider CRUD operations
export const createProvider = async (providerData: Omit<Provider, 'id' | 'createdAt' | 'updatedAt'>) => {
  const now = Timestamp.now();
  const provider = {
    ...providerData,
    // Set default values
    rating: 0,
    reviewCount: 0,
    totalBookings: 0,
    totalRevenue: 0,
    completionRate: 0,
    responseRate: 0,
    fetchPoints: 0,
    commissionsaved: 0,
    verified: false,
    featured: false,
    status: 'pending' as const,
    membershipTier: 'free' as const,
    businessPhotos: [],
    specialties: [],
    socialMedia: {},
    settings: {
      emailNotifications: true,
      smsNotifications: false,
      bookingNotifications: true,
      marketingEmails: false,
      autoAcceptBookings: false,
      requireDeposit: false,
      cancellationPolicy: 'flexible'
    },
    businessHours: {
      monday: { open: '09:00', close: '17:00', closed: false },
      tuesday: { open: '09:00', close: '17:00', closed: false },
      wednesday: { open: '09:00', close: '17:00', closed: false },
      thursday: { open: '09:00', close: '17:00', closed: false },
      friday: { open: '09:00', close: '17:00', closed: false },
      saturday: { open: '09:00', close: '17:00', closed: false },
      sunday: { open: '09:00', close: '17:00', closed: true }
    },
    documents: {},
    createdAt: now,
    updatedAt: now
  };

  const docRef = await addDoc(collection(db, 'providers'), provider);
  return docRef.id;
};

export const getProvider = async (providerId: string): Promise<Provider | null> => {
  const docRef = doc(db, 'providers', providerId);
  const docSnap = await getDoc(docRef);

  if (docSnap.exists()) {
    return { id: docSnap.id, ...docSnap.data() } as Provider;
  }
  return null;
};

export const getProviderByUserId = async (userId: string): Promise<Provider | null> => {
  try {
    console.log('🔍 getProviderByUserId called with userId:', userId);
    const q = query(
      collection(db, 'providers'),
      where('userId', '==', userId),
      limit(1)
    );

    const querySnapshot = await getDocs(q);
    console.log('📊 Query result - empty:', querySnapshot.empty, 'size:', querySnapshot.size);

    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const providerData = { id: doc.id, ...doc.data() } as Provider;
      console.log('✅ Provider found:', providerData);
      return providerData;
    }

    console.log('❌ No provider found for userId:', userId);
    return null;
  } catch (error) {
    console.error('❌ Error getting provider by user ID:', error);
    throw error;
  }
};

export const updateProvider = async (providerId: string, updates: Partial<Provider>) => {
  const docRef = doc(db, 'providers', providerId);
  await updateDoc(docRef, {
    ...updates,
    updatedAt: Timestamp.now()
  });
};

export const updateProviderStats = async (providerId: string, stats: {
  totalBookings?: number;
  totalRevenue?: number;
  rating?: number;
  reviewCount?: number;
  completionRate?: number;
  responseRate?: number;
}) => {
  const docRef = doc(db, 'providers', providerId);
  await updateDoc(docRef, {
    ...stats,
    updatedAt: Timestamp.now(),
    lastActive: Timestamp.now()
  });
};

export const getProvidersByLocation = async (city: string, serviceType?: string) => {
  let q = query(
    collection(db, 'providers'),
    where('city', '==', city),
    where('status', '==', 'approved'),
    orderBy('rating', 'desc')
  );

  if (serviceType) {
    q = query(q, where('serviceType', '==', serviceType));
  }

  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Provider));
};

// Debug function to check all providers in database
export const debugProviders = async () => {
  try {
    console.log('🔍 DEBUG: Checking all providers in database...');

    // Get ALL providers (no filters)
    const allProvidersQuery = collection(db, 'providers');
    const allProvidersSnapshot = await getDocs(allProvidersQuery);
    console.log(`📊 Total providers in database: ${allProvidersSnapshot.size}`);

    allProvidersSnapshot.docs.forEach((doc, index) => {
      const provider = doc.data();
      console.log(`Provider ${index + 1}:`, {
        id: doc.id,
        businessName: provider.businessName,
        status: provider.status,
        email: provider.email,
        serviceType: provider.serviceType,
        hasStatus: 'status' in provider,
        allFields: Object.keys(provider)
      });
    });

    // Check services collection
    const allServicesQuery = collection(db, 'services');
    const allServicesSnapshot = await getDocs(allServicesQuery);
    console.log(`🛠️ Total services in database: ${allServicesSnapshot.size}`);

    allServicesSnapshot.docs.forEach((doc, index) => {
      const service = doc.data();
      console.log(`Service ${index + 1}:`, {
        id: doc.id,
        providerId: service.providerId,
        name: service.name,
        active: service.active,
        category: service.category
      });
    });

  } catch (error) {
    console.error('❌ Error debugging providers:', error);
  }
};

// Get all approved providers with active services
export const getActiveProviders = async (searchQuery?: string, serviceType?: string) => {
  try {
    console.log('🔍 Getting active providers...');

    // Debug: First check all providers
    await debugProviders();

    // First get all approved providers (without orderBy to avoid index issues)
    let providersQuery = query(
      collection(db, 'providers'),
      where('status', '==', 'approved')
    );

    const providersSnapshot = await getDocs(providersQuery);
    console.log(`📊 Found ${providersSnapshot.size} approved providers`);

    const providers = providersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Provider));

    // Get active services for each provider
    const providersWithServices = await Promise.all(
      providers.map(async (provider) => {
        try {
          const servicesQuery = query(
            collection(db, 'services'),
            where('providerId', '==', provider.id),
            where('active', '==', true)
          );

          const servicesSnapshot = await getDocs(servicesQuery);
          const activeServices = servicesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Service));

          console.log(`📋 Provider ${provider.businessName}: ${activeServices.length} active services`);

          return {
            ...provider,
            activeServices,
            hasActiveServices: activeServices.length > 0
          };
        } catch (error) {
          console.error(`❌ Error getting services for provider ${provider.id}:`, error);
          return {
            ...provider,
            activeServices: [],
            hasActiveServices: false
          };
        }
      })
    );

    // Filter providers that have active services
    let filteredProviders = providersWithServices.filter(provider => provider.hasActiveServices);
    console.log(`✅ Found ${filteredProviders.length} providers with active services`);

    // Apply search filter if provided
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filteredProviders = filteredProviders.filter(provider =>
        provider.businessName?.toLowerCase().includes(query) ||
        provider.description?.toLowerCase().includes(query) ||
        provider.serviceType?.toLowerCase().includes(query) ||
        provider.specialties?.some(specialty => specialty.toLowerCase().includes(query)) ||
        provider.activeServices.some(service =>
          service.name?.toLowerCase().includes(query) ||
          service.description?.toLowerCase().includes(query) ||
          service.category?.toLowerCase().includes(query)
        )
      );
      console.log(`🔍 After search filter: ${filteredProviders.length} providers`);
    }

    // Apply service type filter if provided
    if (serviceType && serviceType !== 'All Services') {
      filteredProviders = filteredProviders.filter(provider =>
        provider.serviceType === serviceType ||
        provider.activeServices.some(service => service.category === serviceType)
      );
      console.log(`🏷️ After service type filter: ${filteredProviders.length} providers`);
    }

    // Sort by rating (client-side to avoid index issues)
    filteredProviders.sort((a, b) => (b.rating || 0) - (a.rating || 0));

    console.log(`🎯 Returning ${filteredProviders.length} providers`);
    return filteredProviders;
  } catch (error) {
    console.error('Error getting active providers:', error);
    throw error;
  }
};

// Get ALL providers regardless of status (for debugging)
export const getAllProviders = async () => {
  try {
    console.log('🔍 Getting ALL providers (no status filter)...');

    const providersQuery = collection(db, 'providers');
    const providersSnapshot = await getDocs(providersQuery);
    console.log(`📊 Found ${providersSnapshot.size} total providers`);

    const providers = providersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Provider));

    // Get services for each provider
    const providersWithServices = await Promise.all(
      providers.map(async (provider) => {
        try {
          const servicesQuery = query(
            collection(db, 'services'),
            where('providerId', '==', provider.id)
          );

          const servicesSnapshot = await getDocs(servicesQuery);
          const allServices = servicesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Service));
          const activeServices = allServices.filter(service => service.active);

          console.log(`📋 Provider ${provider.businessName}: ${allServices.length} total services, ${activeServices.length} active`);

          return {
            ...provider,
            activeServices,
            allServices,
            hasActiveServices: activeServices.length > 0
          };
        } catch (error) {
          console.error(`❌ Error getting services for provider ${provider.id}:`, error);
          return {
            ...provider,
            activeServices: [],
            allServices: [],
            hasActiveServices: false
          };
        }
      })
    );

    console.log(`✅ Returning ${providersWithServices.length} providers`);
    return providersWithServices;

  } catch (error) {
    console.error('❌ Error getting all providers:', error);
    throw error;
  }
};

// Get emergency/veterinary providers
export const getEmergencyProviders = async (keywords: string[] = ['emergency', 'vet', 'veterinary', 'hospital', 'clinic']) => {
  try {
    const providersQuery = query(
      collection(db, 'providers'),
      where('status', '==', 'approved'),
      orderBy('rating', 'desc')
    );

    const providersSnapshot = await getDocs(providersQuery);
    const providers = providersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Provider));

    // Filter providers that match emergency/vet keywords
    const emergencyProviders = providers.filter(provider => {
      const searchText = `${provider.businessName} ${provider.description} ${provider.serviceType} ${provider.specialties.join(' ')}`.toLowerCase();
      return keywords.some(keyword => searchText.includes(keyword.toLowerCase()));
    });

    // Get active services for emergency providers
    const emergencyProvidersWithServices = await Promise.all(
      emergencyProviders.map(async (provider) => {
        const servicesQuery = query(
          collection(db, 'services'),
          where('providerId', '==', provider.id),
          where('active', '==', true)
        );

        const servicesSnapshot = await getDocs(servicesQuery);
        const activeServices = servicesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Service));

        return {
          ...provider,
          activeServices,
          hasActiveServices: activeServices.length > 0
        };
      })
    );

    return emergencyProvidersWithServices.filter(provider => provider.hasActiveServices);
  } catch (error) {
    console.error('Error getting emergency providers:', error);
    throw error;
  }
};

// Service CRUD operations
export const createService = async (serviceData: Omit<Service, 'id' | 'createdAt' | 'updatedAt'>) => {
  const now = Timestamp.now();
  const service = {
    ...serviceData,
    createdAt: now,
    updatedAt: now
  };

  const docRef = await addDoc(collection(db, 'services'), service);
  return docRef.id;
};

export const updateService = async (serviceId: string, updates: Partial<Service>) => {
  const docRef = doc(db, 'services', serviceId);
  const updateData = {
    ...updates,
    updatedAt: Timestamp.now()
  };

  await updateDoc(docRef, updateData);
};

export const deleteService = async (serviceId: string) => {
  const docRef = doc(db, 'services', serviceId);
  await deleteDoc(docRef);
};

export const getProviderServices = async (providerId: string) => {
  const q = query(
    collection(db, 'services'),
    where('providerId', '==', providerId)
    // Removed orderBy to avoid index requirement - will sort client-side
  );
  
  const querySnapshot = await getDocs(q);
  const services = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Service));

  // Sort client-side by createdAt descending (most recent first)
  return services.sort((a, b) => {
    const aDate = a.createdAt?.toDate?.() || new Date(a.createdAt || 0);
    const bDate = b.createdAt?.toDate?.() || new Date(b.createdAt || 0);
    return bDate.getTime() - aDate.getTime();
  });
};

// Booking CRUD operations
export const createBooking = async (bookingData: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>) => {
  const now = Timestamp.now();
  const booking = {
    ...bookingData,
    createdAt: now,
    updatedAt: now
  };
  
  const docRef = await addDoc(collection(db, 'bookings'), booking);
  return docRef.id;
};

export const getProviderBookings = async (providerId: string, status?: string) => {
  let q = query(
    collection(db, 'bookings'),
    where('providerId', '==', providerId)
    // Removed orderBy to avoid index requirement - will sort client-side
  );
  
  if (status) {
    q = query(q, where('status', '==', status));
  }
  
  const querySnapshot = await getDocs(q);
  const bookings = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Booking));

  // Sort client-side by date descending (most recent first)
  return bookings.sort((a, b) => {
    const aDate = a.date?.toDate?.() || new Date(a.date || 0);
    const bDate = b.date?.toDate?.() || new Date(b.date || 0);
    return bDate.getTime() - aDate.getTime();
  });
};

// Earnings operations
export const getProviderEarnings = async (providerId: string) => {
  const q = query(
    collection(db, 'earnings'),
    where('providerId', '==', providerId)
    // Removed orderBy to avoid index requirement - will sort client-side
  );
  
  const querySnapshot = await getDocs(q);
  const earnings = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Earning));

  // Sort client-side by earnedAt descending (most recent first)
  return earnings.sort((a, b) => {
    const aDate = a.earnedAt?.toDate?.() || new Date(a.earnedAt || 0);
    const bDate = b.earnedAt?.toDate?.() || new Date(b.earnedAt || 0);
    return bDate.getTime() - aDate.getTime();
  });
};

export const createPayout = async (payoutData: Omit<Payout, 'id' | 'requestedAt'>) => {
  const payout = {
    ...payoutData,
    requestedAt: Timestamp.now()
  };

  const docRef = await addDoc(collection(db, 'payouts'), payout);
  return docRef.id;
};

// Enhanced search function with location, service, date, and pet type filtering
export interface SearchFilters {
  location?: string;
  service?: string;
  date?: string;
  petType?: string;
  maxDistance?: number; // in miles, default 20
}

export const searchProviders = async (filters: SearchFilters) => {
  try {
    console.log('🔍 Searching providers with filters:', filters);

    // First get all approved providers with active services
    const providersWithServices = await getActiveProviders();

    let filteredProviders = providersWithServices.filter(provider => provider.hasActiveServices);

    // Filter by service type
    if (filters.service && filters.service !== 'All Services') {
      filteredProviders = filteredProviders.filter(provider => {
        const serviceMatch = provider.serviceType?.toLowerCase().includes(filters.service!.toLowerCase()) ||
          provider.activeServices.some(service =>
            service.category?.toLowerCase().includes(filters.service!.toLowerCase()) ||
            service.name?.toLowerCase().includes(filters.service!.toLowerCase())
          );
        return serviceMatch;
      });
    }

    // Filter by pet type
    if (filters.petType && filters.petType !== 'All Pets') {
      filteredProviders = filteredProviders.filter(provider => {
        return provider.activeServices.some(service =>
          service.petTypes?.some(petType =>
            petType.toLowerCase().includes(filters.petType!.toLowerCase())
          )
        );
      });
    }

    // Filter by location (within specified radius)
    if (filters.location) {
      const locationData = parseLocation(filters.location);
      if (locationData) {
        const searchCoordinates = getCityCoordinates(locationData.city, locationData.state);
        if (searchCoordinates) {
          const maxDistance = filters.maxDistance || 20;
          filteredProviders = filterProvidersByDistance(filteredProviders, searchCoordinates, maxDistance);
        } else {
          // Fallback to city name matching if coordinates not found
          filteredProviders = filteredProviders.filter(provider =>
            provider.city?.toLowerCase().includes(locationData.city.toLowerCase()) ||
            provider.state?.toLowerCase().includes(locationData.state.toLowerCase())
          );
        }
      } else {
        // Simple text search in address fields
        const searchTerm = filters.location.toLowerCase();
        filteredProviders = filteredProviders.filter(provider =>
          provider.city?.toLowerCase().includes(searchTerm) ||
          provider.state?.toLowerCase().includes(searchTerm) ||
          provider.address?.toLowerCase().includes(searchTerm) ||
          provider.zipCode?.includes(searchTerm)
        );
      }
    }

    // Filter by date availability (basic implementation)
    if (filters.date) {
      // For now, we'll assume all providers are available
      // In a real implementation, you'd check against their calendar/availability
      console.log('Date filtering not fully implemented yet:', filters.date);
    }

    console.log(`✅ Found ${filteredProviders.length} providers matching search criteria`);
    return filteredProviders;

  } catch (error) {
    console.error('❌ Error searching providers:', error);
    throw error;
  }
};
