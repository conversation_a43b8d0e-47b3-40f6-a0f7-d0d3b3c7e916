import Stripe from 'stripe';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

/**
 * Get or create a Stripe customer ID from a Firebase UID
 * This ensures we always use proper Stripe customer IDs instead of Firebase UIDs
 */
export async function getOrCreateStripeCustomer(
  firebaseUid: string,
  userType: 'pet_owner' | 'provider' = 'pet_owner'
): Promise<string> {
  try {
    // Determine the correct collection based on user type
    const collection = userType === 'provider' ? COLLECTIONS.PROVIDERS : COLLECTIONS.USERS;
    const userRef = doc(db, collection, firebaseUid);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      throw new Error(`${userType} not found`);
    }

    const userData = userDoc.data();
    let stripeCustomerId = userData.stripeCustomerId;

    // If user already has a Stripe customer ID, verify it exists
    if (stripeCustomerId) {
      try {
        const customer = await stripe.customers.retrieve(stripeCustomerId);
        if (!customer.deleted) {
          return stripeCustomerId;
        }
      } catch (error: any) {
        if (error.code === 'resource_missing') {
          console.log(`Stripe customer ${stripeCustomerId} not found, creating new one`);
          stripeCustomerId = null; // Will create a new one below
        } else {
          throw error;
        }
      }
    }

    // Create new Stripe customer
    const customer = await stripe.customers.create({
      email: userData.email,
      name: userData.name || userData.businessName,
      metadata: {
        firebaseUid,
        userType,
        platform: 'fetchly',
      },
    });

    stripeCustomerId = customer.id;

    // Save customer ID to user record
    await updateDoc(userRef, {
      stripeCustomerId,
      updatedAt: new Date().toISOString(),
    });

    console.log(`✅ Created Stripe customer ${stripeCustomerId} for ${userType} ${firebaseUid}`);
    return stripeCustomerId;

  } catch (error: any) {
    console.error('Error getting/creating Stripe customer:', error);
    throw new Error(`Failed to get/create Stripe customer: ${error.message}`);
  }
}

/**
 * Validate that a customer ID is a proper Stripe customer ID (not a Firebase UID)
 */
export function isValidStripeCustomerId(customerId: string): boolean {
  // Stripe customer IDs start with 'cus_'
  return customerId.startsWith('cus_');
}

/**
 * Get Stripe customer details
 */
export async function getStripeCustomer(customerId: string): Promise<Stripe.Customer | null> {
  try {
    if (!isValidStripeCustomerId(customerId)) {
      throw new Error('Invalid Stripe customer ID format');
    }

    const customer = await stripe.customers.retrieve(customerId);
    
    if (customer.deleted) {
      return null;
    }

    return customer as Stripe.Customer;
  } catch (error: any) {
    if (error.code === 'resource_missing') {
      return null;
    }
    throw error;
  }
}

/**
 * Update Stripe customer information
 */
export async function updateStripeCustomer(
  customerId: string,
  updates: {
    email?: string;
    name?: string;
    metadata?: Record<string, string>;
  }
): Promise<Stripe.Customer> {
  try {
    if (!isValidStripeCustomerId(customerId)) {
      throw new Error('Invalid Stripe customer ID format');
    }

    const customer = await stripe.customers.update(customerId, updates);
    return customer;
  } catch (error: any) {
    console.error('Error updating Stripe customer:', error);
    throw new Error(`Failed to update Stripe customer: ${error.message}`);
  }
}

/**
 * Delete Stripe customer
 */
export async function deleteStripeCustomer(customerId: string): Promise<void> {
  try {
    if (!isValidStripeCustomerId(customerId)) {
      throw new Error('Invalid Stripe customer ID format');
    }

    await stripe.customers.del(customerId);
    console.log(`✅ Deleted Stripe customer ${customerId}`);
  } catch (error: any) {
    console.error('Error deleting Stripe customer:', error);
    throw new Error(`Failed to delete Stripe customer: ${error.message}`);
  }
}

/**
 * Clean up orphaned Stripe customer references in Firebase
 * This function can be used to fix existing data where Firebase UIDs were used as customer IDs
 */
export async function cleanupOrphanedCustomerReferences(
  firebaseUid: string,
  userType: 'pet_owner' | 'provider' = 'pet_owner'
): Promise<void> {
  try {
    const collection = userType === 'provider' ? COLLECTIONS.PROVIDERS : COLLECTIONS.USERS;
    const userRef = doc(db, collection, firebaseUid);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return;
    }

    const userData = userDoc.data();
    const stripeCustomerId = userData.stripeCustomerId;

    // If the stored customer ID is actually a Firebase UID, clear it
    if (stripeCustomerId && !isValidStripeCustomerId(stripeCustomerId)) {
      console.log(`Cleaning up invalid customer ID ${stripeCustomerId} for ${userType} ${firebaseUid}`);
      
      await updateDoc(userRef, {
        stripeCustomerId: null,
        updatedAt: new Date().toISOString(),
      });
    }
  } catch (error: any) {
    console.error('Error cleaning up orphaned customer reference:', error);
  }
}
