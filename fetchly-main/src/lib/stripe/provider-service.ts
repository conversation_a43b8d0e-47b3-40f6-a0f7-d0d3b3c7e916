import { stripe, STRIPE_CONFIG, calculatePlatformFee, formatAmountForStripe } from './config';
import { doc, updateDoc, addDoc, collection, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';

export interface ProviderOnboardingData {
  providerId: string;
  email: string;
  businessName: string;
  country?: string;
}

export interface InvoiceData {
  providerId: string;
  amount: number; // in dollars
  description: string;
  customerEmail?: string;
  dueDate?: string;
  metadata?: Record<string, string>;
}

export interface PaymentLinkData {
  providerId: string;
  amount: number; // in dollars
  description: string;
  successUrl?: string;
  cancelUrl?: string;
  metadata?: Record<string, string>;
}

/**
 * Provider Service for Stripe Connect
 * Handles provider onboarding, invoicing, and payouts
 */
export class ProviderService {
  
  /**
   * Create Stripe Connect Express account for provider
   */
  static async createConnectAccount(data: ProviderOnboardingData) {
    try {
      const account = await stripe.accounts.create({
        type: 'express',
        country: data.country || 'US',
        email: data.email,
        business_profile: {
          name: data.businessName,
          product_description: 'Pet care services',
        },
        metadata: {
          providerId: data.providerId,
          platform: 'fetchly',
        },
      });

      // Update provider document with Stripe account ID
      await updateDoc(doc(db, COLLECTIONS.PROVIDERS, data.providerId), {
        stripeAccountId: account.id,
        stripeOnboardingStatus: 'pending',
        updatedAt: new Date().toISOString(),
      });

      return {
        success: true,
        accountId: account.id,
        account,
      };
    } catch (error: any) {
      console.error('Error creating Connect account:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create onboarding link for provider
   */
  static async createOnboardingLink(accountId: string) {
    try {
      const accountLink = await stripe.accountLinks.create({
        account: accountId,
        refresh_url: STRIPE_CONFIG.CONNECT.REFRESH_URL,
        return_url: STRIPE_CONFIG.CONNECT.RETURN_URL,
        type: 'account_onboarding',
      });

      return {
        success: true,
        url: accountLink.url,
      };
    } catch (error: any) {
      console.error('Error creating onboarding link:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create login link for provider dashboard
   */
  static async createDashboardLink(accountId: string) {
    try {
      const loginLink = await stripe.accounts.createLoginLink(accountId);

      return {
        success: true,
        url: loginLink.url,
      };
    } catch (error: any) {
      console.error('Error creating dashboard link:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Check provider's onboarding status
   */
  static async checkOnboardingStatus(accountId: string) {
    try {
      const account = await stripe.accounts.retrieve(accountId);
      
      const isOnboarded = account.details_submitted && 
                         account.charges_enabled && 
                         account.payouts_enabled;

      return {
        success: true,
        isOnboarded,
        account,
        status: {
          detailsSubmitted: account.details_submitted,
          chargesEnabled: account.charges_enabled,
          payoutsEnabled: account.payouts_enabled,
        },
      };
    } catch (error: any) {
      console.error('Error checking onboarding status:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create payment link for provider invoicing
   */
  static async createPaymentLink(data: PaymentLinkData) {
    try {
      // Get provider's Stripe account
      const providerDoc = await getDoc(doc(db, COLLECTIONS.PROVIDERS, data.providerId));
      if (!providerDoc.exists()) {
        throw new Error('Provider not found');
      }

      const provider = providerDoc.data();
      if (!provider.stripeAccountId) {
        throw new Error('Provider has not completed Stripe onboarding');
      }

      const amountInCents = formatAmountForStripe(data.amount);
      const platformFee = calculatePlatformFee(amountInCents);

      const paymentLink = await stripe.paymentLinks.create({
        line_items: [
          {
            price_data: {
              currency: STRIPE_CONFIG.DEFAULT_CURRENCY,
              product_data: {
                name: data.description,
                description: `Service provided by ${provider.businessName || provider.ownerName}`,
              },
              unit_amount: amountInCents,
            },
            quantity: 1,
          },
        ],
        application_fee_amount: platformFee,
        transfer_data: {
          destination: provider.stripeAccountId,
        },
        after_completion: {
          type: 'redirect',
          redirect: {
            url: data.successUrl || `${STRIPE_CONFIG.CONNECT.DASHBOARD_URL}?payment=success`,
          },
        },
        metadata: {
          providerId: data.providerId,
          type: 'provider_invoice',
          ...data.metadata,
        },
      });

      // Log invoice in Firestore
      await addDoc(collection(db, COLLECTIONS.INVOICES), {
        providerId: data.providerId,
        amount: data.amount,
        platformFee: platformFee / 100,
        description: data.description,
        stripePaymentLinkId: paymentLink.id,
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      return {
        success: true,
        paymentLink,
        url: paymentLink.url,
      };
    } catch (error: any) {
      console.error('Error creating payment link:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create direct payment intent for provider (upfront payments)
   */
  static async createDirectPaymentIntent(data: PaymentLinkData & { customerId?: string }) {
    try {
      // Get provider's Stripe account
      const providerDoc = await getDoc(doc(db, COLLECTIONS.PROVIDERS, data.providerId));
      if (!providerDoc.exists()) {
        throw new Error('Provider not found');
      }

      const provider = providerDoc.data();
      if (!provider.stripeAccountId) {
        throw new Error('Provider has not completed Stripe onboarding');
      }

      const amountInCents = formatAmountForStripe(data.amount);
      const platformFee = calculatePlatformFee(amountInCents);

      const paymentIntent = await stripe.paymentIntents.create({
        amount: amountInCents,
        currency: STRIPE_CONFIG.DEFAULT_CURRENCY,
        customer: data.customerId,
        payment_method_types: ['card'],
        application_fee_amount: platformFee,
        transfer_data: {
          destination: provider.stripeAccountId,
        },
        metadata: {
          providerId: data.providerId,
          type: 'direct_payment',
          ...data.metadata,
        },
        description: data.description,
      });

      return {
        success: true,
        paymentIntent,
        clientSecret: paymentIntent.client_secret,
      };
    } catch (error: any) {
      console.error('Error creating direct payment intent:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get provider's payout history
   */
  static async getPayoutHistory(accountId: string, limit: number = 10) {
    try {
      const payouts = await stripe.payouts.list(
        {
          limit,
        },
        {
          stripeAccount: accountId,
        }
      );

      return {
        success: true,
        payouts: payouts.data,
      };
    } catch (error: any) {
      console.error('Error fetching payout history:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get provider's balance
   */
  static async getBalance(accountId: string) {
    try {
      const balance = await stripe.balance.retrieve({
        stripeAccount: accountId,
      });

      return {
        success: true,
        balance,
      };
    } catch (error: any) {
      console.error('Error fetching balance:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create instant payout (if enabled)
   */
  static async createInstantPayout(accountId: string, amount: number) {
    try {
      const amountInCents = formatAmountForStripe(amount);
      
      const payout = await stripe.payouts.create(
        {
          amount: amountInCents,
          currency: STRIPE_CONFIG.DEFAULT_CURRENCY,
          method: 'instant',
        },
        {
          stripeAccount: accountId,
        }
      );

      return {
        success: true,
        payout,
      };
    } catch (error: any) {
      console.error('Error creating instant payout:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}
