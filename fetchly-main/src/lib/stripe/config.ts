import Stripe from 'stripe';

// Initialize Stripe with your secret key
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
  typescript: true,
});

// Stripe configuration constants
export const STRIPE_CONFIG = {
  // Platform fee percentage (10% as mentioned in requirements)
  PLATFORM_FEE_PERCENTAGE: 0.10,
  
  // Minimum amounts (in cents)
  MIN_PAYMENT_AMOUNT: 100, // $1.00
  MIN_WALLET_TOPUP: 500,   // $5.00
  
  // Maximum amounts (in cents)
  MAX_PAYMENT_AMOUNT: 100000, // $1,000.00
  MAX_WALLET_TOPUP: 50000,     // $500.00
  
  // Currency
  DEFAULT_CURRENCY: 'usd',
  
  // Webhook endpoints
  WEBHOOK_ENDPOINTS: {
    PAYMENT_SUCCESS: '/api/webhooks/stripe/payment-success',
    PAYOUT_PAID: '/api/webhooks/stripe/payout-paid',
    ACCOUNT_UPDATED: '/api/webhooks/stripe/account-updated',
    TRANSFER_PAID: '/api/webhooks/stripe/transfer-paid',
  },
  
  // Connect account settings
  CONNECT: {
    REFRESH_URL: `${process.env.NEXT_PUBLIC_APP_URL}/provider/onboarding/refresh`,
    RETURN_URL: `${process.env.NEXT_PUBLIC_APP_URL}/provider/onboarding/complete`,
    DASHBOARD_URL: `${process.env.NEXT_PUBLIC_APP_URL}/provider/dashboard`,
  },
} as const;

// Helper function to calculate platform fee
export const calculatePlatformFee = (amount: number): number => {
  return Math.round(amount * STRIPE_CONFIG.PLATFORM_FEE_PERCENTAGE);
};

// Helper function to format amount for Stripe (convert dollars to cents)
export const formatAmountForStripe = (amount: number): number => {
  return Math.round(amount * 100);
};

// Helper function to format amount for display (convert cents to dollars)
export const formatAmountForDisplay = (amount: number): number => {
  return amount / 100;
};

// Stripe Connect account types
export const ACCOUNT_TYPES = {
  EXPRESS: 'express',
  STANDARD: 'standard',
  CUSTOM: 'custom',
} as const;

// Payment method types
export const PAYMENT_METHODS = {
  CARD: 'card',
  WALLET: 'fetchly_wallet',
  BANK_TRANSFER: 'us_bank_account',
} as const;

// Transaction types for our system
export const TRANSACTION_TYPES = {
  PAYMENT: 'payment',
  REFUND: 'refund',
  WALLET_TOPUP: 'wallet_topup',
  WALLET_PAYMENT: 'wallet_payment',
  PAYOUT: 'payout',
  PLATFORM_FEE: 'platform_fee',
} as const;

// Transaction statuses
export const TRANSACTION_STATUSES = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  SUCCEEDED: 'succeeded',
  FAILED: 'failed',
  CANCELED: 'canceled',
  REFUNDED: 'refunded',
} as const;

export type TransactionType = typeof TRANSACTION_TYPES[keyof typeof TRANSACTION_TYPES];
export type TransactionStatus = typeof TRANSACTION_STATUSES[keyof typeof TRANSACTION_STATUSES];
export type PaymentMethod = typeof PAYMENT_METHODS[keyof typeof PAYMENT_METHODS];
