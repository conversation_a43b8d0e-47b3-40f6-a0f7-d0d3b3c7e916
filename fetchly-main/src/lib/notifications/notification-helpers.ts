import { unifiedNotificationService } from '@/lib/services/unified-notification-service';

/**
 * Easy-to-use notification helpers for different platform activities
 * Call these from anywhere in your app to trigger notifications
 */

// ==========================================
// CHAT & MESSAGING NOTIFICATIONS
// ==========================================

export const notifyNewMessage = async (
  recipientId: string,
  senderId: string,
  senderName: string,
  messageText: string,
  chatId: string,
  messageId: string,
  senderAvatar?: string,
  isGroupChat: boolean = false
) => {
  await unifiedNotificationService.notifyNewMessage(
    recipientId,
    senderId,
    senderName,
    messageText,
    chatId,
    messageId,
    senderAvatar,
    isGroupChat
  );
};

// ==========================================
// BOOKING NOTIFICATIONS
// ==========================================

export const notifyBookingRequest = async (
  providerId: string,
  petOwnerId: string,
  petOwnerName: string,
  serviceType: string,
  bookingId: string,
  bookingDate: Date,
  amount: number
) => {
  await unifiedNotificationService.notifyBookingRequest(
    providerId,
    petOwnerId,
    petOwnerName,
    serviceType,
    bookingId,
    bookingDate,
    amount
  );
};

export const notifyBookingConfirmed = async (
  petOwnerId: string,
  providerId: string,
  providerName: string,
  serviceType: string,
  bookingId: string,
  bookingDate: Date
) => {
  await unifiedNotificationService.notifyBookingConfirmed(
    petOwnerId,
    providerId,
    providerName,
    serviceType,
    bookingId,
    bookingDate
  );
};

export const notifyBookingCancelled = async (
  userId: string,
  cancelledBy: string,
  cancellerName: string,
  serviceType: string,
  bookingId: string,
  reason?: string
) => {
  await unifiedNotificationService.createNotification({
    userId,
    type: 'booking_cancelled',
    title: 'Booking Cancelled',
    message: `${cancellerName} cancelled the ${serviceType} booking${reason ? `: ${reason}` : ''}`,
    data: {
      bookingId,
      serviceType,
      authorId: cancelledBy,
      authorName: cancellerName,
      actionUrl: `/bookings/${bookingId}`
    },
    priority: 'high',
    category: 'booking',
    isRead: false
  });
};

export const notifyBookingCompleted = async (
  petOwnerId: string,
  providerId: string,
  providerName: string,
  serviceType: string,
  bookingId: string
) => {
  await unifiedNotificationService.createNotification({
    userId: petOwnerId,
    type: 'booking_completed',
    title: 'Service Completed',
    message: `${providerName} completed your ${serviceType} service. Please leave a review!`,
    data: {
      bookingId,
      serviceType,
      providerId,
      providerName,
      actionUrl: `/bookings/${bookingId}/review`
    },
    priority: 'medium',
    category: 'booking',
    isRead: false,
    actions: {
      primary: {
        label: 'Leave Review',
        action: 'leave_review',
        url: `/bookings/${bookingId}/review`
      }
    }
  });
};

// ==========================================
// SOCIAL & COMMUNITY NOTIFICATIONS
// ==========================================

export const notifyPostLiked = async (
  postAuthorId: string,
  likerId: string,
  likerName: string,
  postId: string,
  postContent: string,
  likerAvatar?: string
) => {
  await unifiedNotificationService.notifyPostLiked(
    postAuthorId,
    likerId,
    likerName,
    postId,
    postContent,
    likerAvatar
  );
};

export const notifyPostCommented = async (
  postAuthorId: string,
  commenterId: string,
  commenterName: string,
  postId: string,
  commentId: string,
  commentText: string,
  postContent: string,
  commenterAvatar?: string
) => {
  await unifiedNotificationService.createNotification({
    userId: postAuthorId,
    type: 'post_commented',
    title: 'New Comment',
    message: `${commenterName} commented on your post: "${commentText.substring(0, 50)}..."`,
    data: {
      postId,
      commentId,
      authorId: commenterId,
      authorName: commenterName,
      authorAvatar: commenterAvatar,
      actionUrl: `/community/post/${postId}#comment-${commentId}`
    },
    priority: 'medium',
    category: 'social',
    isRead: false,
    actions: {
      primary: {
        label: 'View Comment',
        action: 'view_comment',
        url: `/community/post/${postId}#comment-${commentId}`
      }
    }
  });
};

export const notifyPostShared = async (
  postAuthorId: string,
  sharerId: string,
  sharerName: string,
  postId: string,
  postContent: string,
  sharerAvatar?: string
) => {
  await unifiedNotificationService.createNotification({
    userId: postAuthorId,
    type: 'post_shared',
    title: 'Post Shared',
    message: `${sharerName} shared your post: "${postContent.substring(0, 50)}..."`,
    data: {
      postId,
      authorId: sharerId,
      authorName: sharerName,
      authorAvatar: sharerAvatar,
      actionUrl: `/community/post/${postId}`
    },
    priority: 'low',
    category: 'social',
    isRead: false
  });
};

export const notifyNewFollower = async (
  userId: string,
  followerId: string,
  followerName: string,
  followerAvatar?: string
) => {
  await unifiedNotificationService.notifyNewFollower(
    userId,
    followerId,
    followerName,
    followerAvatar
  );
};

export const notifyFriendRequest = async (
  userId: string,
  requesterId: string,
  requesterName: string,
  requesterAvatar?: string
) => {
  await unifiedNotificationService.createNotification({
    userId,
    type: 'friend_request',
    title: 'Friend Request',
    message: `${requesterName} sent you a friend request`,
    data: {
      authorId: requesterId,
      authorName: requesterName,
      authorAvatar: requesterAvatar,
      actionUrl: `/friends/requests`
    },
    priority: 'medium',
    category: 'social',
    isRead: false,
    actions: {
      primary: {
        label: 'View Request',
        action: 'view_friend_request',
        url: `/friends/requests`
      }
    }
  });
};

// ==========================================
// REVIEW & RATING NOTIFICATIONS
// ==========================================

export const notifyNewReview = async (
  providerId: string,
  reviewerId: string,
  reviewerName: string,
  reviewId: string,
  rating: number,
  reviewText: string,
  serviceType: string,
  reviewerAvatar?: string
) => {
  await unifiedNotificationService.createNotification({
    userId: providerId,
    type: 'new_review',
    title: `New ${rating}-Star Review`,
    message: `${reviewerName} left a ${rating}-star review for your ${serviceType}: "${reviewText.substring(0, 50)}..."`,
    data: {
      reviewId,
      rating,
      reviewText,
      serviceType,
      authorId: reviewerId,
      authorName: reviewerName,
      authorAvatar: reviewerAvatar,
      actionUrl: `/provider/reviews/${reviewId}`
    },
    priority: 'medium',
    category: 'social',
    isRead: false,
    actions: {
      primary: {
        label: 'View Review',
        action: 'view_review',
        url: `/provider/reviews/${reviewId}`
      },
      secondary: {
        label: 'Respond',
        action: 'respond_review',
        url: `/provider/reviews/${reviewId}/respond`
      }
    }
  });
};

export const notifyReviewResponse = async (
  reviewerId: string,
  providerId: string,
  providerName: string,
  reviewId: string,
  responseText: string,
  serviceType: string
) => {
  await unifiedNotificationService.createNotification({
    userId: reviewerId,
    type: 'review_response',
    title: 'Provider Responded',
    message: `${providerName} responded to your ${serviceType} review: "${responseText.substring(0, 50)}..."`,
    data: {
      reviewId,
      serviceType,
      authorId: providerId,
      authorName: providerName,
      actionUrl: `/reviews/${reviewId}`
    },
    priority: 'low',
    category: 'social',
    isRead: false
  });
};

// ==========================================
// PAYMENT & EARNINGS NOTIFICATIONS
// ==========================================

export const notifyPaymentReceived = async (
  providerId: string,
  amount: number,
  payerName: string,
  serviceType: string,
  transactionId: string
) => {
  await unifiedNotificationService.notifyPaymentReceived(
    providerId,
    amount,
    payerName,
    serviceType,
    transactionId
  );
};

export const notifyPayoutProcessed = async (
  providerId: string,
  amount: number,
  payoutId: string,
  bankAccount: string
) => {
  await unifiedNotificationService.createNotification({
    userId: providerId,
    type: 'payout_processed',
    title: 'Payout Processed',
    message: `Your payout of $${amount} has been sent to ${bankAccount}`,
    data: {
      transactionId: payoutId,
      paymentAmount: amount,
      paymentMethod: bankAccount,
      actionUrl: `/provider/payouts/${payoutId}`
    },
    priority: 'high',
    category: 'payment',
    isRead: false
  });
};

// ==========================================
// SYSTEM NOTIFICATIONS
// ==========================================

export const notifyAccountVerified = async (userId: string) => {
  await unifiedNotificationService.createNotification({
    userId,
    type: 'account_verified',
    title: 'Account Verified!',
    message: 'Your account has been successfully verified. You can now access all features.',
    data: {
      actionUrl: '/profile'
    },
    priority: 'high',
    category: 'system',
    isRead: false
  });
};

export const notifyProfileApproved = async (providerId: string) => {
  await unifiedNotificationService.createNotification({
    userId: providerId,
    type: 'profile_approved',
    title: 'Profile Approved!',
    message: 'Your provider profile has been approved and is now live on Fetchly.',
    data: {
      actionUrl: '/provider/profile'
    },
    priority: 'high',
    category: 'system',
    isRead: false
  });
};

// ==========================================
// EMERGENCY NOTIFICATIONS
// ==========================================

export const notifyEmergencyAlert = async (
  userId: string,
  alertTitle: string,
  alertMessage: string,
  alertUrl?: string
) => {
  await unifiedNotificationService.createNotification({
    userId,
    type: 'emergency_alert',
    title: alertTitle,
    message: alertMessage,
    data: {
      actionUrl: alertUrl
    },
    priority: 'urgent',
    category: 'emergency',
    isRead: false
  });
};
