import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';

export interface AdminUser {
  email: string;
  role: 'admin' | 'superadmin' | 'support';
  loginTime: number;
}

export interface AdminCredentials {
  email: string;
  password: string;
}

// Get admin credentials from environment
export function getAdminCredentials(): { emails: string[], passwords: string[], roles: string[] } {
  const emails = process.env.ADMIN_EMAILS?.split(',') || [];
  const passwords = process.env.ADMIN_PASSWORDS?.split(',') || [];
  const roles = process.env.ADMIN_ROLES?.split(',') || [];

  if (emails.length !== passwords.length || emails.length !== roles.length) {
    throw new Error('Admin emails, passwords, and roles count mismatch');
  }

  return { emails, passwords, roles };
}

// Validate admin credentials and return role
export function validateAdminCredentials(email: string, password: string): { isValid: boolean, role?: string } {
  try {
    const { emails, passwords, roles } = getAdminCredentials();

    const adminIndex = emails.findIndex(adminEmail => adminEmail.trim() === email.trim());

    if (adminIndex === -1) {
      return { isValid: false };
    }

    const expectedPassword = passwords[adminIndex].trim();
    const isValid = password === expectedPassword;

    return {
      isValid,
      role: isValid ? roles[adminIndex].trim() : undefined
    };
  } catch (error) {
    console.error('Error validating admin credentials:', error);
    return { isValid: false };
  }
}

// Generate admin JWT token
export function generateAdminToken(email: string, role: string): string {
  const secret = process.env.ADMIN_JWT_SECRET;
  if (!secret) {
    throw new Error('Admin JWT secret not configured');
  }

  const payload: AdminUser = {
    email,
    role: role as 'admin' | 'superadmin' | 'support',
    loginTime: Date.now()
  };

  return jwt.sign(payload, secret, {
    expiresIn: '24h',
    issuer: 'fetchlypr-admin'
  });
}

// Verify admin JWT token
export function verifyAdminToken(token: string): AdminUser | null {
  try {
    const secret = process.env.ADMIN_JWT_SECRET;
    if (!secret) {
      throw new Error('Admin JWT secret not configured');
    }
    
    const decoded = jwt.verify(token, secret) as AdminUser;
    
    // Check if token is expired (24 hours)
    const tokenAge = Date.now() - decoded.loginTime;
    const maxAge = parseInt(process.env.ADMIN_SESSION_TIMEOUT || '86400000'); // 24 hours default
    
    if (tokenAge > maxAge) {
      return null;
    }
    
    return decoded;
  } catch (error) {
    console.error('Error verifying admin token:', error);
    return null;
  }
}

// Check if admin dashboard is enabled
export function isAdminDashboardEnabled(): boolean {
  return process.env.ADMIN_DASHBOARD_ENABLED === 'true';
}

// Get admin role from email
export function getAdminRole(email: string): 'admin' | 'superadmin' | 'support' {
  if (email.includes('superadmin')) return 'superadmin';
  if (email.includes('support')) return 'support';
  return 'admin';
}

// Admin permissions
export const ADMIN_PERMISSIONS = {
  support: [
    'view_bookings',
    'view_users',
    'view_providers',
    'manage_support_tickets',
    'send_messages'
  ],
  admin: [
    'view_bookings',
    'view_providers',
    'view_users',
    'view_analytics',
    'manage_bookings',
    'manage_providers',
    'manage_featured',
    'manage_reviews'
  ],
  superadmin: [
    'view_bookings',
    'view_providers',
    'view_users',
    'view_analytics',
    'manage_bookings',
    'manage_providers',
    'manage_users',
    'manage_settings',
    'view_payments',
    'manage_payments',
    'system_admin',
    'manage_featured',
    'manage_reviews',
    'manage_support_tickets',
    'send_messages'
  ]
};

export function hasAdminPermission(role: 'admin' | 'superadmin' | 'support', permission: string): boolean {
  return ADMIN_PERMISSIONS[role].includes(permission);
}
