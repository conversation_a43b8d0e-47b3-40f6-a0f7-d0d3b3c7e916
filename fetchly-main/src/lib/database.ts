// Firebase Database Service - Replaces PostgreSQL
import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  addDoc,
  query as firestoreQuery,
  where,
  orderBy,
  limit,
  Timestamp,
  writeBatch,
  increment,
  arrayUnion,
  arrayRemove,
  QuerySnapshot,
  DocumentData,
  DocumentReference,
  CollectionReference,
} from 'firebase/firestore';
import { db } from './firebase/config';

// Collection references for both Pet Owners and Providers
export const COLLECTIONS = {
  // Core user collections
  USERS: 'users',

  // Pet Owner specific collections
  PETS: 'pets',
  PET_OWNER_TRANSACTIONS: 'petOwnerTransactions',
  REWARD_TRANSACTIONS: 'rewardTransactions',

  // Provider specific collections
  PROVIDER_PROFILES: 'providerProfiles',
  SERVICES: 'services',
  PROVIDER_EARNINGS: 'providerEarnings',
  PROVIDER_CLIENTS: 'providerClients',
  PROVIDER_CALENDAR: 'providerCalendar',
  PROVIDER_SUBSCRIPTIONS: 'providerSubscriptions',
  PROVIDER_BANK_ACCOUNTS: 'providerBankAccounts',

  // Shared collections (both user types)
  BOOKINGS: 'bookings',
  REVIEWS: 'reviews',
  POSTS: 'posts',
  BLOG_POSTS: 'blogPosts',
  CHAT_ROOMS: 'chatRooms',
  MESSAGES: 'messages',

  // Payment collections
  WALLETS: 'wallets',
  INVOICES: 'invoices',
  PAYOUTS: 'payouts',
  DISPUTES: 'disputes',
  PAYMENT_METHODS: 'paymentMethods',

  // Monetization collections
  PROVIDER_SUBSCRIPTIONS: 'providerSubscriptions',
  PROVIDER_BOOSTS: 'providerBoosts',
  BOOKING_ADD_ONS: 'bookingAddOns',

  // System collections
  REWARD_ITEMS: 'rewardItems',
  SYSTEM_SETTINGS: 'systemSettings',
  ANALYTICS: 'analytics',

  // Legacy collections (deprecated)
  VACCINATIONS: 'vaccinations',
  TRANSACTIONS: 'transactions',
  PROVIDERS: 'providers',
  FILES: 'files',
} as const;

// User role types
export const USER_ROLES = {
  PET_OWNER: 'pet_owner',
  PROVIDER: 'provider',
  ADMIN: 'admin'
} as const;

// Provider subscription tiers
export const PROVIDER_TIERS = {
  FREE: 'free',
  PRO: 'pro'
} as const;

// Helper function to convert Firestore timestamp to Date
export function timestampToDate(timestamp: any): Date {
  if (timestamp && timestamp.toDate) {
    return timestamp.toDate();
  }
  return new Date(timestamp);
}

// Helper function to convert Date to Firestore timestamp
export function dateToTimestamp(date: Date): Timestamp {
  return Timestamp.fromDate(date);
}

// Generic database operations
export class DatabaseService {
  // Create a document
  static async create(collectionName: string, data: any, id?: string): Promise<string> {
    if (id) {
      const docRef = doc(db, collectionName, id);
      await setDoc(docRef, {
        ...data,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      });
      return id;
    } else {
      const collectionRef = collection(db, collectionName);
      const docRef = await addDoc(collectionRef, {
        ...data,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      });
      return docRef.id;
    }
  }

  // Get a document by ID
  static async getById(collectionName: string, id: string): Promise<any | null> {
    const docRef = doc(db, collectionName, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() };
    }
    return null;
  }

  // Update a document
  static async update(collectionName: string, id: string, data: any): Promise<void> {
    const docRef = doc(db, collectionName, id);
    await updateDoc(docRef, {
      ...data,
      updatedAt: Timestamp.now(),
    });
  }

  // Delete a document
  static async delete(collectionName: string, id: string): Promise<void> {
    const docRef = doc(db, collectionName, id);
    await deleteDoc(docRef);
  }

  // Get all documents in a collection
  static async getAll(collectionName: string): Promise<any[]> {
    const collectionRef = collection(db, collectionName);
    const querySnapshot = await getDocs(collectionRef);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  // Query documents with conditions
  static async query(
    collectionName: string,
    conditions: Array<{ field: string; operator: any; value: any }> = [],
    orderByField?: string,
    orderDirection: 'asc' | 'desc' = 'desc',
    limitCount?: number
  ): Promise<any[]> {
    let q = collection(db, collectionName) as any;

    // Add where conditions
    conditions.forEach(condition => {
      q = firestoreQuery(q, where(condition.field, condition.operator, condition.value));
    });

    // Add ordering
    if (orderByField) {
      q = firestoreQuery(q, orderBy(orderByField, orderDirection));
    }

    // Add limit
    if (limitCount) {
      q = firestoreQuery(q, limit(limitCount));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }
}

// Firebase-specific helper functions

// Get documents by user ID
export async function getUserDocuments(userId: string, collectionName: string): Promise<any[]> {
  return DatabaseService.query(collectionName, [
    { field: 'userId', operator: '==', value: userId }
  ]);
}

// Get documents with pagination
export async function getPaginatedDocuments(
  collectionName: string,
  pageSize: number = 10,
  orderByField: string = 'createdAt',
  conditions: Array<{ field: string; operator: any; value: any }> = []
): Promise<any[]> {
  return DatabaseService.query(collectionName, conditions, orderByField, 'desc', pageSize);
}
