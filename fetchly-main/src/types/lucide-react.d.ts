// Lucide React type declarations
declare module 'lucide-react' {
  import { ComponentType, SVGProps } from 'react';
  
  export interface LucideProps extends SVGProps<SVGSVGElement> {
    size?: string | number;
    color?: string;
    strokeWidth?: string | number;
    absoluteStrokeWidth?: boolean;
  }
  
  export type LucideIcon = ComponentType<LucideProps>;
  
  // Common icons used in the app
  export const Loader2: LucideIcon;
  export const Users: LucideIcon;
  export const Shield: LucideIcon;
  export const Award: LucideIcon;
  export const Target: LucideIcon;
  export const Zap: LucideIcon;
  export const Lock: LucideIcon;
  export const Eye: LucideIcon;
  export const EyeOff: LucideIcon;
  export const AlertCircle: LucideIcon;
  export const MessageCircle: LucideIcon;
  export const User: LucideIcon;
  export const Clock: LucideIcon;
  export const CheckCircle: LucideIcon;
  export const Filter: LucideIcon;
  export const Search: LucideIcon;
  export const Menu: LucideIcon;
  export const X: LucideIcon;
  export const Home: LucideIcon;
  export const Calendar: LucideIcon;
  export const Settings: LucideIcon;
  export const Bell: LucideIcon;
  export const Heart: LucideIcon;
  export const Star: LucideIcon;
  export const MapPin: LucideIcon;
  export const Phone: LucideIcon;
  export const Mail: LucideIcon;
  export const Camera: LucideIcon;
  export const Upload: LucideIcon;
  export const Download: LucideIcon;
  export const Edit: LucideIcon;
  export const Trash: LucideIcon;
  export const Plus: LucideIcon;
  export const Minus: LucideIcon;
  export const ChevronDown: LucideIcon;
  export const ChevronUp: LucideIcon;
  export const ChevronLeft: LucideIcon;
  export const ChevronRight: LucideIcon;
  export const ArrowLeft: LucideIcon;
  export const ArrowRight: LucideIcon;
  export const Check: LucideIcon;
  export const Copy: LucideIcon;
  export const Share: LucideIcon;
  export const ExternalLink: LucideIcon;
  export const Info: LucideIcon;
  export const HelpCircle: LucideIcon;
  export const MoreHorizontal: LucideIcon;
  export const MoreVertical: LucideIcon;
  
  // Export all icons as default
  const icons: Record<string, LucideIcon>;
  export default icons;
}
