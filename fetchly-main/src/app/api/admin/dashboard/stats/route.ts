import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminToken } from '@/lib/admin/auth';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.cookies.get('admin-token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const admin = verifyAdminToken(token);
    if (!admin) {
      return NextResponse.json({ error: 'Invalid admin token' }, { status: 401 });
    }

    console.log('🔒 Admin dashboard stats requested by:', admin.email);

    // Fetch all collections data
    const [usersSnapshot, providersSnapshot, bookingsSnapshot] = await Promise.all([
      getDocs(collection(db, 'users')),
      getDocs(collection(db, 'providers')),
      getDocs(collection(db, 'bookings'))
    ]);

    // Calculate stats
    const totalUsers = usersSnapshot.size;
    const totalProviders = providersSnapshot.size;
    const totalBookings = bookingsSnapshot.size;

    // Active providers (approved status)
    const activeProviders = providersSnapshot.docs.filter(doc => {
      const data = doc.data();
      return data.status === 'approved';
    }).length;

    // Pending bookings
    const pendingBookings = bookingsSnapshot.docs.filter(doc => {
      const data = doc.data();
      return data.status === 'pending_provider_approval' || data.status === 'pending';
    }).length;

    // Calculate revenue (sum of all completed bookings)
    const totalRevenue = bookingsSnapshot.docs.reduce((sum, doc) => {
      const data = doc.data();
      if (data.status === 'completed' && data.totalPrice) {
        return sum + (data.totalPrice * 0.05); // 5% platform fee
      }
      return sum;
    }, 0);

    // Recent activity (last 10 bookings)
    const recentBookings = bookingsSnapshot.docs
      .map(doc => ({ id: doc.id, ...doc.data() }))
      .sort((a, b) => {
        const aTime = a.createdAt?.toDate?.() || new Date(0);
        const bTime = b.createdAt?.toDate?.() || new Date(0);
        return bTime.getTime() - aTime.getTime();
      })
      .slice(0, 10);

    const stats = {
      totalUsers,
      totalProviders,
      activeProviders,
      totalBookings,
      pendingBookings,
      totalRevenue: Math.round(totalRevenue * 100) / 100,
      recentActivity: recentBookings.map(booking => ({
        id: booking.id,
        type: 'booking',
        customerName: booking.customerName || booking.userName,
        serviceName: booking.serviceName,
        status: booking.status,
        createdAt: booking.createdAt?.toDate?.()?.toISOString() || new Date().toISOString()
      })),
      lastUpdated: new Date().toISOString()
    };

    console.log('📊 Dashboard stats calculated:', {
      totalUsers,
      totalProviders,
      activeProviders,
      totalBookings,
      pendingBookings,
      totalRevenue: stats.totalRevenue
    });

    return NextResponse.json(stats);

  } catch (error) {
    console.error('❌ Error fetching dashboard stats:', error);
    return NextResponse.json({
      error: 'Failed to fetch dashboard stats',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
