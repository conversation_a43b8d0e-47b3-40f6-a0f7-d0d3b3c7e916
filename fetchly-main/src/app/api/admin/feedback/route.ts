import { NextRequest, NextResponse } from 'next/server';
import { collection, getDocs, query, orderBy, where, doc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

// Admin authentication check
function isAdminAuthenticated(request: NextRequest): boolean {
  const authHeader = request.headers.get('authorization');
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
  
  if (!authHeader || !authHeader.startsWith('Basic ')) {
    return false;
  }
  
  const credentials = Buffer.from(authHeader.slice(6), 'base64').toString();
  const [email, password] = credentials.split(':');
  
  return email === adminEmail && password === adminPassword;
}

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    if (!isAdminAuthenticated(request)) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized access'
      }, { status: 401 });
    }

    console.log('📋 Admin fetching feedback submissions...');

    // Get all feedback from Firebase
    const feedbackQuery = query(
      collection(db, 'feedback'),
      orderBy('createdAt', 'desc')
    );

    const feedbackSnapshot = await getDocs(feedbackQuery);
    const feedback = feedbackSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt
    }));

    console.log(`✅ Retrieved ${feedback.length} feedback submissions`);

    return NextResponse.json({
      success: true,
      feedback,
      total: feedback.length
    });

  } catch (error) {
    console.error('❌ Error fetching feedback:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch feedback'
    }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Check admin authentication
    if (!isAdminAuthenticated(request)) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized access'
      }, { status: 401 });
    }

    const { feedbackId, status, adminResponse } = await request.json();

    if (!feedbackId) {
      return NextResponse.json({
        success: false,
        error: 'Feedback ID is required'
      }, { status: 400 });
    }

    console.log('📝 Admin updating feedback:', feedbackId);

    // Update feedback in Firebase
    const feedbackRef = doc(db, 'feedback', feedbackId);
    const updateData: any = {
      updatedAt: new Date()
    };

    if (status) {
      updateData.status = status;
    }

    if (adminResponse) {
      updateData.adminResponse = adminResponse;
      updateData.adminResponseAt = new Date();
    }

    await updateDoc(feedbackRef, updateData);

    console.log('✅ Feedback updated successfully');

    return NextResponse.json({
      success: true,
      message: 'Feedback updated successfully'
    });

  } catch (error) {
    console.error('❌ Error updating feedback:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update feedback'
    }, { status: 500 });
  }
}
