import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminToken } from '@/lib/admin/auth';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.cookies.get('admin-token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const admin = verifyAdminToken(token);
    if (!admin) {
      return NextResponse.json({ error: 'Invalid admin token' }, { status: 401 });
    }

    console.log('🔍 Analyzing Provider ID Consistency...');

    // Get all users with provider role
    const usersRef = collection(db, 'users');
    const usersSnapshot = await getDocs(usersRef);
    
    const providerUsers = usersSnapshot.docs
      .filter(doc => doc.data().role === 'provider')
      .map(doc => ({
        userId: doc.id, // Firebase Auth ID
        email: doc.data().email,
        name: doc.data().name || doc.data().displayName,
        role: doc.data().role,
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || 'Unknown'
      }));

    // Get all provider documents
    const providersRef = collection(db, 'providers');
    const providersSnapshot = await getDocs(providersRef);
    
    const providerDocs = providersSnapshot.docs.map(doc => ({
      documentId: doc.id,
      userId: doc.data().userId || doc.data().uid || 'NO_USER_ID',
      email: doc.data().email,
      businessName: doc.data().businessName || doc.data().name,
      status: doc.data().status,
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || 'Unknown'
    }));

    // Get all bookings
    const bookingsRef = collection(db, 'bookings');
    const bookingsSnapshot = await getDocs(bookingsRef);
    
    const bookings = bookingsSnapshot.docs.map(doc => ({
      id: doc.id,
      providerId: doc.data().providerId,
      customerName: doc.data().customerName || doc.data().userName,
      serviceName: doc.data().serviceName,
      status: doc.data().status,
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || 'Unknown'
    }));

    // Analyze ID consistency
    const analysis = {
      providerUsers: {
        count: providerUsers.length,
        users: providerUsers
      },
      providerDocs: {
        count: providerDocs.length,
        docs: providerDocs
      },
      bookings: {
        count: bookings.length,
        uniqueProviderIds: [...new Set(bookings.map(b => b.providerId))],
        bookingsByProvider: bookings.reduce((acc, booking) => {
          const providerId = booking.providerId || 'NO_PROVIDER_ID';
          if (!acc[providerId]) {
            acc[providerId] = [];
          }
          acc[providerId].push({
            id: booking.id,
            customer: booking.customerName,
            service: booking.serviceName,
            status: booking.status
          });
          return acc;
        }, {} as Record<string, any[]>)
      },
      inconsistencies: {
        orphanedBookings: [],
        mismatchedProviders: [],
        missingUserIds: []
      }
    };

    // Find inconsistencies
    
    // 1. Orphaned bookings (bookings with provider IDs that don't exist in users or providers)
    for (const booking of bookings) {
      const hasMatchingUser = providerUsers.some(u => u.userId === booking.providerId);
      const hasMatchingDoc = providerDocs.some(d => d.documentId === booking.providerId || d.userId === booking.providerId);
      
      if (!hasMatchingUser && !hasMatchingDoc) {
        analysis.inconsistencies.orphanedBookings.push({
          bookingId: booking.id,
          providerId: booking.providerId,
          customer: booking.customerName,
          service: booking.serviceName
        });
      }
    }

    // 2. Provider docs with mismatched user IDs
    for (const providerDoc of providerDocs) {
      const matchingUser = providerUsers.find(u => u.email === providerDoc.email);
      if (matchingUser && providerDoc.userId !== matchingUser.userId) {
        analysis.inconsistencies.mismatchedProviders.push({
          documentId: providerDoc.documentId,
          currentUserId: providerDoc.userId,
          correctUserId: matchingUser.userId,
          email: providerDoc.email,
          businessName: providerDoc.businessName
        });
      }
    }

    // 3. Provider docs missing user IDs
    for (const providerDoc of providerDocs) {
      if (!providerDoc.userId || providerDoc.userId === 'NO_USER_ID') {
        analysis.inconsistencies.missingUserIds.push({
          documentId: providerDoc.documentId,
          email: providerDoc.email,
          businessName: providerDoc.businessName
        });
      }
    }

    // Generate recommendations
    const recommendations = [];
    
    if (analysis.inconsistencies.orphanedBookings.length > 0) {
      recommendations.push({
        type: 'orphaned_bookings',
        severity: 'high',
        message: `${analysis.inconsistencies.orphanedBookings.length} bookings have invalid provider IDs`,
        action: 'Update booking provider IDs to match correct user IDs'
      });
    }

    if (analysis.inconsistencies.mismatchedProviders.length > 0) {
      recommendations.push({
        type: 'mismatched_providers',
        severity: 'high',
        message: `${analysis.inconsistencies.mismatchedProviders.length} provider documents have incorrect user IDs`,
        action: 'Update provider documents with correct user IDs'
      });
    }

    if (analysis.inconsistencies.missingUserIds.length > 0) {
      recommendations.push({
        type: 'missing_user_ids',
        severity: 'medium',
        message: `${analysis.inconsistencies.missingUserIds.length} provider documents are missing user IDs`,
        action: 'Add user IDs to provider documents'
      });
    }

    const summary = {
      totalProviderUsers: providerUsers.length,
      totalProviderDocs: providerDocs.length,
      totalBookings: bookings.length,
      uniqueProviderIdsInBookings: analysis.bookings.uniqueProviderIds.length,
      totalInconsistencies: 
        analysis.inconsistencies.orphanedBookings.length +
        analysis.inconsistencies.mismatchedProviders.length +
        analysis.inconsistencies.missingUserIds.length,
      needsFix: recommendations.length > 0
    };

    return NextResponse.json({
      success: true,
      summary,
      analysis,
      recommendations,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error analyzing provider IDs:', error);
    return NextResponse.json({
      error: 'Failed to analyze provider IDs',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
