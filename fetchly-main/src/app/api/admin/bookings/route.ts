import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminToken } from '@/lib/admin/auth';
import { collection, getDocs, orderBy, query } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.cookies.get('admin-token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const admin = verifyAdminToken(token);
    if (!admin) {
      return NextResponse.json({ error: 'Invalid admin token' }, { status: 401 });
    }

    console.log('🔒 Admin bookings data requested by:', admin.email);

    // Fetch all bookings
    const bookingsQuery = query(
      collection(db, 'bookings'),
      orderBy('createdAt', 'desc')
    );
    
    const bookingsSnapshot = await getDocs(bookingsQuery);
    
    const bookings = bookingsSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        customerName: data.customerName || data.userName || 'Unknown',
        providerName: data.providerName || 'Unknown Provider',
        serviceName: data.serviceName || 'Unknown Service',
        scheduledDate: data.scheduledDate || '',
        scheduledTime: data.scheduledTime || '',
        status: data.status || 'pending',
        totalPrice: data.totalPrice || 0,
        petName: data.petName || '',
        notes: data.notes || '',
        createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString()
      };
    });

    console.log(`📊 Fetched ${bookings.length} bookings for admin`);

    return NextResponse.json({
      success: true,
      bookings,
      total: bookings.length
    });

  } catch (error) {
    console.error('❌ Error fetching admin bookings:', error);
    return NextResponse.json({
      error: 'Failed to fetch bookings',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
