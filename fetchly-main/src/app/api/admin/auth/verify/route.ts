import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminToken } from '@/lib/admin/auth';

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('admin-token')?.value;

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'No admin token found'
      }, { status: 401 });
    }

    const adminUser = verifyAdminToken(token);

    if (!adminUser) {
      return NextResponse.json({
        success: false,
        error: 'Invalid or expired admin token'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: true,
      admin: adminUser
    });

  } catch (error) {
    console.error('❌ Admin token verification error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
