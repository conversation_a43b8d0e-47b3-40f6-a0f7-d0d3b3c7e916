import { NextRequest, NextResponse } from 'next/server';
import { validateAdminCredentials, generateAdminToken, isAdminDashboardEnabled } from '@/lib/admin/auth';

export async function POST(request: NextRequest) {
  try {
    // Check if admin dashboard is enabled
    if (!isAdminDashboardEnabled()) {
      return NextResponse.json({
        success: false,
        error: 'Admin dashboard is disabled'
      }, { status: 403 });
    }

    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 });
    }

    // Validate admin credentials
    const { isValid, role } = validateAdminCredentials(email, password);

    if (!isValid || !role) {
      console.log(`❌ Failed admin login attempt for: ${email}`);
      return NextResponse.json({
        success: false,
        error: 'Invalid admin credentials'
      }, { status: 401 });
    }

    // Generate JWT token
    const token = generateAdminToken(email, role);

    console.log(`✅ Admin login successful for: ${email}`);

    // Create response with secure cookie
    const response = NextResponse.json({
      success: true,
      message: 'Admin login successful',
      admin: {
        email,
        role,
        loginTime: Date.now()
      }
    });

    // Set secure HTTP-only cookie
    response.cookies.set('admin-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      path: '/admin'
    });

    return response;

  } catch (error) {
    console.error('❌ Admin login error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
