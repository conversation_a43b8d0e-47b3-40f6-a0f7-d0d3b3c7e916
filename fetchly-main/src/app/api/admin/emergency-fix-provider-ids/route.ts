import { NextRequest, NextResponse } from 'next/server';
import { collection, getDocs, doc, updateDoc, query, where } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function POST(request: NextRequest) {
  try {
    console.log('🚨 EMERGENCY: Fixing Provider ID Mismatch...');

    // Step 1: Get all users with provider role
    const usersRef = collection(db, 'users');
    const usersSnapshot = await getDocs(usersRef);
    
    const providers = usersSnapshot.docs
      .filter(doc => doc.data().role === 'provider')
      .map(doc => ({
        userId: doc.id, // This is the correct Firebase Auth ID
        email: doc.data().email,
        name: doc.data().name || doc.data().displayName,
        ...doc.data()
      }));

    console.log(`📊 Found ${providers.length} provider users`);

    // Step 2: Get all bookings
    const bookingsRef = collection(db, 'bookings');
    const bookingsSnapshot = await getDocs(bookingsRef);
    
    const bookings = bookingsSnapshot.docs.map(doc => ({
      id: doc.id,
      providerId: doc.data().providerId,
      customerName: doc.data().customerName || doc.data().userName,
      serviceName: doc.data().serviceName,
      status: doc.data().status,
      ...doc.data()
    }));

    console.log(`📊 Found ${bookings.length} bookings`);

    // Step 3: Fix the specific booking with provider ID C3BTQw52xAYlSvtOKDfE
    const targetProviderId = 'C3BTQw52xAYlSvtOKDfE';
    const correctProviderId = 'KfUbx3q1QLXuItCVKHJ2liCJnrt2';

    console.log(`🎯 Fixing bookings with provider ID: ${targetProviderId}`);
    console.log(`🎯 Changing to correct provider ID: ${correctProviderId}`);

    // Find bookings with the wrong provider ID
    const bookingsToFix = bookings.filter(booking => booking.providerId === targetProviderId);
    console.log(`📋 Found ${bookingsToFix.length} bookings to fix`);

    let fixedBookings = 0;
    const fixResults = [];

    for (const booking of bookingsToFix) {
      try {
        const bookingRef = doc(db, 'bookings', booking.id);
        await updateDoc(bookingRef, {
          providerId: correctProviderId,
          updatedAt: new Date(),
          fixedProviderId: true,
          originalProviderId: targetProviderId
        });
        
        fixedBookings++;
        fixResults.push({
          bookingId: booking.id,
          customerName: booking.customerName,
          serviceName: booking.serviceName,
          status: booking.status,
          oldProviderId: targetProviderId,
          newProviderId: correctProviderId
        });
        
        console.log(`✅ Fixed booking ${booking.id}: ${booking.customerName} - ${booking.serviceName}`);
      } catch (error) {
        console.error(`❌ Failed to fix booking ${booking.id}:`, error);
      }
    }

    // Step 4: Also fix any provider documents with wrong user ID
    const providersRef = collection(db, 'providers');
    const providersSnapshot = await getDocs(providersRef);
    
    let fixedProviders = 0;
    const providerFixResults = [];

    for (const providerDoc of providersSnapshot.docs) {
      const data = providerDoc.data();
      
      // If this provider document has the wrong ID, fix it
      if (providerDoc.id === targetProviderId) {
        try {
          const providerRef = doc(db, 'providers', providerDoc.id);
          await updateDoc(providerRef, {
            userId: correctProviderId,
            uid: correctProviderId,
            updatedAt: new Date(),
            fixedUserId: true
          });
          
          fixedProviders++;
          providerFixResults.push({
            documentId: providerDoc.id,
            businessName: data.businessName || data.name,
            email: data.email,
            oldUserId: data.userId || 'none',
            newUserId: correctProviderId
          });
          
          console.log(`✅ Fixed provider document ${providerDoc.id}`);
        } catch (error) {
          console.error(`❌ Failed to fix provider ${providerDoc.id}:`, error);
        }
      }
    }

    const summary = {
      targetProviderId,
      correctProviderId,
      totalBookings: bookings.length,
      bookingsFixed: fixedBookings,
      providersFixed: fixedProviders,
      fixResults,
      providerFixResults,
      timestamp: new Date().toISOString()
    };

    console.log('🎉 Emergency Fix Complete!');
    console.log(`📊 Fixed ${fixedBookings} bookings and ${fixedProviders} providers`);

    return NextResponse.json({
      success: true,
      message: 'Emergency provider ID fix completed',
      summary
    });

  } catch (error) {
    console.error('❌ Emergency fix failed:', error);
    return NextResponse.json({
      error: 'Emergency fix failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
