import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminToken } from '@/lib/admin/auth';
import { collection, getDocs, orderBy, query } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.cookies.get('admin-token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const admin = verifyAdminToken(token);
    if (!admin) {
      return NextResponse.json({ error: 'Invalid admin token' }, { status: 401 });
    }

    console.log('🔒 Admin providers data requested by:', admin.email);

    // Fetch all providers
    const providersQuery = query(
      collection(db, 'providers'),
      orderBy('createdAt', 'desc')
    );
    
    const providersSnapshot = await getDocs(providersQuery);
    
    const providers = providersSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        businessName: data.businessName || data.name || 'Unknown Business',
        email: data.email || 'No email',
        status: data.status || 'pending',
        services: data.services || [],
        rating: data.rating || 0,
        totalBookings: data.totalBookings || 0,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
        phone: data.phone || '',
        location: data.location || ''
      };
    });

    console.log(`📊 Fetched ${providers.length} providers for admin`);

    return NextResponse.json({
      success: true,
      providers,
      total: providers.length
    });

  } catch (error) {
    console.error('❌ Error fetching admin providers:', error);
    return NextResponse.json({
      error: 'Failed to fetch providers',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
