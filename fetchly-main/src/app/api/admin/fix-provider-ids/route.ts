import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminToken } from '@/lib/admin/auth';
import { collection, getDocs, doc, updateDoc, query, where } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.cookies.get('admin-token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const admin = verifyAdminToken(token);
    if (!admin || admin.role !== 'superadmin') {
      return NextResponse.json({ error: 'SuperAdmin access required' }, { status: 403 });
    }

    console.log('🔧 Starting Provider ID Fix Process...');

    // Step 1: Get all users with provider role
    const usersRef = collection(db, 'users');
    const usersSnapshot = await getDocs(usersRef);
    
    const providers = usersSnapshot.docs
      .filter(doc => doc.data().role === 'provider')
      .map(doc => ({
        userId: doc.id, // This is the Firebase Auth ID (correct one)
        email: doc.data().email,
        name: doc.data().name || doc.data().displayName,
        ...doc.data()
      }));

    console.log(`📊 Found ${providers.length} provider users`);

    // Step 2: Get all provider documents
    const providersRef = collection(db, 'providers');
    const providersSnapshot = await getDocs(providersRef);
    
    const providerDocs = providersSnapshot.docs.map(doc => ({
      documentId: doc.id, // This might be the wrong ID
      userId: doc.data().userId || doc.data().uid,
      email: doc.data().email,
      businessName: doc.data().businessName,
      ...doc.data()
    }));

    console.log(`📊 Found ${providerDocs.length} provider documents`);

    // Step 3: Get all bookings
    const bookingsRef = collection(db, 'bookings');
    const bookingsSnapshot = await getDocs(bookingsRef);
    
    const bookings = bookingsSnapshot.docs.map(doc => ({
      id: doc.id,
      providerId: doc.data().providerId,
      customerName: doc.data().customerName || doc.data().userName,
      serviceName: doc.data().serviceName,
      status: doc.data().status,
      ...doc.data()
    }));

    console.log(`📊 Found ${bookings.length} bookings`);

    // Step 4: Create mapping between old and new IDs
    const idMappings: { oldId: string, newId: string, email: string, name: string }[] = [];
    
    for (const provider of providers) {
      // Find matching provider document by email
      const matchingDoc = providerDocs.find(doc => 
        doc.email === provider.email || 
        doc.userId === provider.userId
      );

      if (matchingDoc && matchingDoc.documentId !== provider.userId) {
        idMappings.push({
          oldId: matchingDoc.documentId,
          newId: provider.userId,
          email: provider.email,
          name: provider.name || matchingDoc.businessName || 'Unknown'
        });
      }
    }

    console.log(`🔄 Found ${idMappings.length} ID mappings to fix`);

    // Step 5: Update bookings with correct provider IDs
    let updatedBookings = 0;
    const bookingUpdates: any[] = [];

    for (const booking of bookings) {
      const mapping = idMappings.find(m => m.oldId === booking.providerId);
      if (mapping) {
        try {
          const bookingRef = doc(db, 'bookings', booking.id);
          await updateDoc(bookingRef, {
            providerId: mapping.newId,
            updatedAt: new Date(),
            fixedProviderId: true // Flag to track fixed bookings
          });
          
          updatedBookings++;
          bookingUpdates.push({
            bookingId: booking.id,
            oldProviderId: mapping.oldId,
            newProviderId: mapping.newId,
            customerName: booking.customerName,
            serviceName: booking.serviceName
          });
          
          console.log(`✅ Updated booking ${booking.id}: ${mapping.oldId} → ${mapping.newId}`);
        } catch (error) {
          console.error(`❌ Failed to update booking ${booking.id}:`, error);
        }
      }
    }

    // Step 6: Update provider documents with correct user IDs
    let updatedProviders = 0;
    const providerUpdates: any[] = [];

    for (const mapping of idMappings) {
      try {
        const providerRef = doc(db, 'providers', mapping.oldId);
        await updateDoc(providerRef, {
          userId: mapping.newId,
          uid: mapping.newId,
          updatedAt: new Date(),
          fixedUserId: true // Flag to track fixed providers
        });
        
        updatedProviders++;
        providerUpdates.push({
          documentId: mapping.oldId,
          oldUserId: mapping.oldId,
          newUserId: mapping.newId,
          email: mapping.email,
          name: mapping.name
        });
        
        console.log(`✅ Updated provider ${mapping.oldId}: userId set to ${mapping.newId}`);
      } catch (error) {
        console.error(`❌ Failed to update provider ${mapping.oldId}:`, error);
      }
    }

    // Step 7: Generate summary report
    const summary = {
      totalProviders: providers.length,
      totalProviderDocs: providerDocs.length,
      totalBookings: bookings.length,
      idMappingsFound: idMappings.length,
      bookingsUpdated: updatedBookings,
      providersUpdated: updatedProviders,
      mappings: idMappings,
      bookingUpdates,
      providerUpdates,
      timestamp: new Date().toISOString()
    };

    console.log('🎉 Provider ID Fix Complete!');
    console.log(`📊 Summary: ${updatedBookings} bookings and ${updatedProviders} providers updated`);

    return NextResponse.json({
      success: true,
      message: 'Provider ID fix completed successfully',
      summary
    });

  } catch (error) {
    console.error('❌ Error fixing provider IDs:', error);
    return NextResponse.json({
      error: 'Failed to fix provider IDs',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
