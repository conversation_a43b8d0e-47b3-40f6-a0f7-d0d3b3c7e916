import { NextRequest, NextResponse } from 'next/server';
import { withMiddleware } from '@/lib/middleware';
import { PetService } from '@/lib/services';

// Get user's pets
async function getPetsHandler(request: NextRequest) {
  try {
    const user = (request as any).user;
    
    const pets = await PetService.getUserPets(user.id);
    
    return NextResponse.json({
      success: true,
      pets
    });
  } catch (error) {
    console.error('Get pets error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get pets'
    }, { status: 500 });
  }
}

// Create new pet
async function createPetHandler(request: NextRequest) {
  try {
    const user = (request as any).user;
    const petData = (request as any).validatedData;
    
    const pet = await PetService.createPet({
      ...petData,
      ownerId: user.id
    });
    
    return NextResponse.json({
      success: true,
      pet,
      message: 'Pet created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Create pet error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create pet'
    }, { status: 500 });
  }
}

// Validation schema for creating pets
const createPetSchema = {
  name: { required: true, type: 'string' as const, minLength: 1, maxLength: 100 },
  species: { required: true, type: 'string' as const },
  breed: { required: false, type: 'string' as const, maxLength: 100 },
  age: { required: false, type: 'number' as const, min: 0, max: 50 },
  weight: { required: false, type: 'number' as const, min: 0, max: 1000 },
  color: { required: false, type: 'string' as const, maxLength: 50 },
  gender: { required: false, type: 'string' as const },
  microchipId: { required: false, type: 'string' as const, maxLength: 50 },
  specialNeeds: { required: false, type: 'string' as const, maxLength: 500 },
  allergies: { required: false, type: 'string' as const, maxLength: 500 },
  medications: { required: false, type: 'string' as const, maxLength: 500 },
  vetInfo: { required: false, type: 'string' as const, maxLength: 500 },
  emergencyContact: { required: false, type: 'string' as const, maxLength: 200 },
  notes: { required: false, type: 'string' as const, maxLength: 1000 }
};

export const GET = withMiddleware(getPetsHandler, {
  auth: true,
  requireVerified: true
});

export const POST = withMiddleware(createPetHandler, {
  auth: true,
  requireVerified: true,
  validation: createPetSchema
});
