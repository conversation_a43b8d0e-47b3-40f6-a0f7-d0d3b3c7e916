import { NextRequest, NextResponse } from 'next/server';
import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

/**
 * Simple invoice payment endpoint
 * POST /api/invoices/pay
 */
export async function POST(request: NextRequest) {
  try {
    const { invoiceId, paymentMethod } = await request.json();

    if (!invoiceId) {
      return NextResponse.json({
        success: false,
        error: 'Invoice ID is required'
      }, { status: 400 });
    }

    // Get invoice
    const invoiceRef = doc(db, 'invoices', invoiceId);
    const invoiceDoc = await getDoc(invoiceRef);

    if (!invoiceDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Invoice not found'
      }, { status: 404 });
    }

    const invoice = invoiceDoc.data();

    // Update invoice status to paid
    await updateDoc(invoiceRef, {
      status: 'paid',
      paidAt: new Date(),
      paymentMethod: paymentMethod || 'manual'
    });

    // In a real app, you would integrate with Stripe, PayPal, etc.
    // For now, we'll just mark it as paid

    return NextResponse.json({
      success: true,
      message: 'Invoice marked as paid',
      invoice: {
        id: invoiceId,
        status: 'paid',
        paidAt: new Date()
      }
    });

  } catch (error: any) {
    console.error('Error processing payment:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to process payment'
    }, { status: 500 });
  }
}

/**
 * Get invoice details for payment
 * GET /api/invoices/pay?id=invoiceId
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const invoiceId = searchParams.get('id');

    if (!invoiceId) {
      return NextResponse.json({
        success: false,
        error: 'Invoice ID is required'
      }, { status: 400 });
    }

    // Get invoice
    const invoiceRef = doc(db, 'invoices', invoiceId);
    const invoiceDoc = await getDoc(invoiceRef);

    if (!invoiceDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Invoice not found'
      }, { status: 404 });
    }

    const invoice = invoiceDoc.data();

    return NextResponse.json({
      success: true,
      invoice: {
        id: invoiceId,
        ...invoice,
        createdAt: invoice.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
        dueDate: invoice.dueDate?.toDate?.()?.toISOString() || new Date().toISOString(),
        paidAt: invoice.paidAt?.toDate?.()?.toISOString()
      }
    });

  } catch (error: any) {
    console.error('Error getting invoice:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get invoice'
    }, { status: 500 });
  }
}
