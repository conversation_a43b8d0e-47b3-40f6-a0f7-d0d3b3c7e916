import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { verifyIdToken } from '@/lib/auth/server';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export async function GET(
  request: NextRequest,
  { params }: { params: { providerId: string } }
) {
  try {
    const { providerId } = params;
    
    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyIdToken(token);
    
    if (!decodedToken) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    // Get provider data
    const providerRef = doc(db, 'providers', providerId);
    const providerDoc = await getDoc(providerRef);
    
    if (!providerDoc.exists()) {
      return NextResponse.json(
        { error: 'Provider not found' },
        { status: 404 }
      );
    }

    const providerData = providerDoc.data();
    const stripeAccountId = providerData.stripeAccountId;
    
    if (!stripeAccountId) {
      return NextResponse.json({
        success: true,
        account: {
          id: null,
          status: 'not_connected',
          payouts_enabled: false,
          charges_enabled: false,
          details_submitted: false,
          requirements: {
            currently_due: [],
            eventually_due: [],
            past_due: [],
          },
        },
      });
    }

    // Get account details from Stripe
    const account = await stripe.accounts.retrieve(stripeAccountId);
    
    // Update local status if it has changed
    const currentStatus = account.details_submitted 
      ? (account.charges_enabled ? 'active' : 'restricted')
      : 'pending';
      
    if (providerData.stripeAccountStatus !== currentStatus) {
      await updateDoc(providerRef, {
        stripeAccountStatus: currentStatus,
        stripePayoutsEnabled: account.payouts_enabled,
        stripeChargesEnabled: account.charges_enabled,
        updatedAt: new Date(),
      });
    }

    // Calculate balance
    let balance = null;
    if (account.charges_enabled) {
      try {
        const stripeBalance = await stripe.balance.retrieve({
          stripeAccount: stripeAccountId,
        });
        balance = {
          available: stripeBalance.available,
          pending: stripeBalance.pending,
        };
      } catch (balanceError) {
        console.error('Error fetching balance:', balanceError);
      }
    }

    return NextResponse.json({
      success: true,
      account: {
        id: account.id,
        status: currentStatus,
        payouts_enabled: account.payouts_enabled,
        charges_enabled: account.charges_enabled,
        details_submitted: account.details_submitted,
        email: account.email,
        business_profile: account.business_profile,
        requirements: {
          currently_due: account.requirements?.currently_due || [],
          eventually_due: account.requirements?.eventually_due || [],
          past_due: account.requirements?.past_due || [],
          disabled_reason: account.requirements?.disabled_reason,
        },
        balance,
        created: account.created,
      },
    });

  } catch (error: any) {
    console.error('Stripe Connect account fetch error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch account details' },
      { status: 500 }
    );
  }
}
