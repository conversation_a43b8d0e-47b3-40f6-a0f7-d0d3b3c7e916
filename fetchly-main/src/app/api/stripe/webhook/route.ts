import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import <PERSON><PERSON> from 'stripe';
import { db } from '@/lib/firebase';
import { doc, setDoc } from 'firebase/firestore';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
});

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET || '';

export async function POST(req: Request) {
  const body = await req.text();
  const signature = headers().get('stripe-signature') || '';

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (err) {
    console.error(`Webhook signature verification failed: ${err}`);
    return new NextResponse('Webhook Error', { status: 400 });
  }

  // Handle the event
  switch (event.type) {
    case 'account.updated':
      const account = event.data.object as Stripe.Account;
      await handleAccountUpdated(account);
      break;
    // Add more event types as needed
    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  return NextResponse.json({ received: true });
}

async function handleAccountUpdated(account: Stripe.Account) {
  try {
    // Find the user with this Stripe account ID
    // Note: In a production app, you'd want to query Firestore for the user
    // with this stripeAccountId. This is a simplified example.
    const userId = account.metadata.userId;
    if (!userId) return;

    const userRef = doc(db, 'users', userId);
    
    await setDoc(
      userRef,
      {
        isStripeConnected: account.details_submitted,
        stripeDashboardLink: `https://dashboard.stripe.com/connect/accounts/${account.id}`,
        updatedAt: new Date().toISOString(),
      },
      { merge: true }
    );
  } catch (error) {
    console.error('Error handling account.updated event:', error);
  }
}
