import { NextResponse } from 'next/server';
import { auth, adminDb } from '@/lib/firebase/admin-config';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
});

export async function POST(req: Request) {
  try {
    // Get the Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];

    // Verify the Firebase token
    if (!auth || !adminDb) {
      console.error('Firebase Admin not initialized');
      return new NextResponse('Server configuration error', { status: 500 });
    }

    let decodedToken;
    try {
      decodedToken = await auth.verifyIdToken(token);
    } catch (error) {
      console.error('Error verifying token:', error);
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const { userId, email, returnUrl } = await req.json();
    
    if (!userId || !email) {
      return new NextResponse('Missing required fields', { status: 400 });
    }

    // Verify the user ID matches the token
    if (userId !== decodedToken.uid) {
      return new NextResponse('Forbidden', { status: 403 });
    }

    // Find provider by userId using Admin SDK
    const providersSnapshot = await adminDb.collection('providers')
      .where('userId', '==', userId)
      .limit(1)
      .get();

    if (providersSnapshot.empty) {
      return NextResponse.json({
        success: false,
        error: 'Provider not found'
      }, { status: 404 });
    }

    const providerDoc = providersSnapshot.docs[0];
    const providerData = providerDoc.data();
    const providerId = providerDoc.id;

    let accountId = providerData?.stripeAccountId;

    if (!accountId) {
      // Create a new Stripe account
      const account = await stripe.accounts.create({
        type: 'express',
        country: 'US',
        email: email,
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
        },
        business_type: 'individual',
        business_profile: {
          product_description: 'Pet services on Fetchly',
        },
      });

      accountId = account.id;

      // Save the Stripe account ID to Firestore using Admin SDK
      await adminDb.collection('providers').doc(providerId).update({
        stripeAccountId: accountId,
        isStripeConnected: false,
        updatedAt: new Date().toISOString(),
      });
    }

    // Create account link for onboarding
    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: returnUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    });

    return NextResponse.json({
      success: true,
      url: accountLink.url,
      accountId: accountId
    });
  } catch (error) {
    console.error('Error creating Stripe account link:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create Stripe onboarding link'
    }, { status: 500 });
  }
}
