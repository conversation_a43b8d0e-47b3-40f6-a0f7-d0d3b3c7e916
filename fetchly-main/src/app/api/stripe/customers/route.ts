import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { verifyIdToken } from '@/lib/auth/server';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export async function POST(request: NextRequest) {
  try {
    const { userId, email, name } = await request.json();
    
    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyIdToken(token);
    
    if (!decodedToken || decodedToken.uid !== userId) {
      return NextResponse.json(
        { error: 'Invalid token or user mismatch' },
        { status: 401 }
      );
    }

    // Check if user already has a Stripe customer ID
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const userData = userDoc.data();
    
    // If user already has a Stripe customer ID, return it
    if (userData.stripeCustomerId) {
      try {
        // Verify the customer still exists in Stripe
        const customer = await stripe.customers.retrieve(userData.stripeCustomerId);
        if (!customer.deleted) {
          return NextResponse.json({
            success: true,
            customerId: userData.stripeCustomerId,
            existing: true,
          });
        }
      } catch (error) {
        console.log('Existing Stripe customer not found, creating new one');
      }
    }

    // Create new Stripe customer
    const customer = await stripe.customers.create({
      email: email || userData.email,
      name: name || userData.name,
      metadata: {
        firebaseUid: userId,
        userType: userData.role || 'pet_owner',
      },
    });

    // Save Stripe customer ID to user record
    await updateDoc(userRef, {
      stripeCustomerId: customer.id,
      updatedAt: new Date(),
    });

    console.log(`✅ Created Stripe customer ${customer.id} for user ${userId}`);

    return NextResponse.json({
      success: true,
      customerId: customer.id,
      existing: false,
    });

  } catch (error: any) {
    console.error('Stripe customer creation error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create customer' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyIdToken(token);
    
    if (!decodedToken || decodedToken.uid !== userId) {
      return NextResponse.json(
        { error: 'Invalid token or user mismatch' },
        { status: 401 }
      );
    }

    // Get user's Stripe customer ID
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const userData = userDoc.data();
    const stripeCustomerId = userData.stripeCustomerId;

    if (!stripeCustomerId) {
      return NextResponse.json({
        success: true,
        customerId: null,
        exists: false,
      });
    }

    // Get customer details from Stripe
    try {
      const customer = await stripe.customers.retrieve(stripeCustomerId);
      
      if (customer.deleted) {
        // Customer was deleted, remove from user record
        await updateDoc(userRef, {
          stripeCustomerId: null,
          updatedAt: new Date(),
        });
        
        return NextResponse.json({
          success: true,
          customerId: null,
          exists: false,
        });
      }

      return NextResponse.json({
        success: true,
        customerId: customer.id,
        exists: true,
        customer: {
          id: customer.id,
          email: customer.email,
          name: customer.name,
          created: customer.created,
        },
      });

    } catch (error: any) {
      if (error.code === 'resource_missing') {
        // Customer doesn't exist in Stripe, remove from user record
        await updateDoc(userRef, {
          stripeCustomerId: null,
          updatedAt: new Date(),
        });
        
        return NextResponse.json({
          success: true,
          customerId: null,
          exists: false,
        });
      }
      
      throw error;
    }

  } catch (error: any) {
    console.error('Stripe customer fetch error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch customer' },
      { status: 500 }
    );
  }
}
