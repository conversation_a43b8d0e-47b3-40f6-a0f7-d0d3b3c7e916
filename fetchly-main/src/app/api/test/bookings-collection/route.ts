import { NextRequest, NextResponse } from 'next/server';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing Bookings Collection Access...');

    // Get ALL bookings from the collection
    const bookingsRef = collection(db, 'bookings');
    const bookingsQuery = query(bookingsRef, orderBy('createdAt', 'desc'));
    const snapshot = await getDocs(bookingsQuery);

    console.log(`📊 Total bookings in collection: ${snapshot.size}`);

    const bookings = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        providerId: data.providerId,
        customerName: data.customerName || data.userName,
        serviceName: data.serviceName,
        status: data.status,
        scheduledDate: data.scheduledDate,
        scheduledTime: data.scheduledTime,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || 'No date',
        petName: data.petName,
        totalPrice: data.totalPrice
      };
    });

    // Group by provider ID
    const byProvider = bookings.reduce((acc, booking) => {
      const providerId = booking.providerId || 'NO_PROVIDER_ID';
      if (!acc[providerId]) {
        acc[providerId] = [];
      }
      acc[providerId].push(booking);
      return acc;
    }, {} as Record<string, any[]>);

    // Group by status
    const byStatus = bookings.reduce((acc, booking) => {
      const status = booking.status || 'NO_STATUS';
      if (!acc[status]) {
        acc[status] = [];
      }
      acc[status].push(booking);
      return acc;
    }, {} as Record<string, any[]>);

    return NextResponse.json({
      success: true,
      message: 'Bookings collection test complete',
      summary: {
        totalBookings: bookings.length,
        uniqueProviders: Object.keys(byProvider).length,
        statusBreakdown: Object.keys(byStatus).map(status => ({
          status,
          count: byStatus[status].length
        })),
        providerBreakdown: Object.keys(byProvider).map(providerId => ({
          providerId,
          count: byProvider[providerId].length,
          bookings: byProvider[providerId].map(b => ({
            id: b.id,
            status: b.status,
            customer: b.customerName,
            service: b.serviceName
          }))
        }))
      },
      allBookings: bookings,
      byProvider,
      byStatus
    });

  } catch (error) {
    console.error('❌ Error testing bookings collection:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to test bookings collection',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
