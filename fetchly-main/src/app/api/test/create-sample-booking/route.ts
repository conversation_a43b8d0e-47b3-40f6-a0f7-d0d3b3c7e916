import { NextRequest, NextResponse } from 'next/server';
import { collection, addDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function POST(request: NextRequest) {
  try {
    const { providerId } = await request.json();
    
    if (!providerId) {
      return NextResponse.json({
        success: false,
        error: 'Provider ID is required'
      }, { status: 400 });
    }

    console.log('🧪 Creating sample booking for provider:', providerId);

    console.log('🧪 Creating sample booking for provider ID:', providerId);

    // IMPORTANT: Ensure we're using the correct Firebase Auth user ID
    // The providerId should be the Firebase Auth user ID (KfUbx3q1QLXuItCVKHJ2liCJnrt2)

    // Create a sample booking
    const sampleBooking = {
      // Customer info
      userId: 'test_customer_123',
      userName: '<PERSON>',
      customerName: '<PERSON>',
      userEmail: '<EMAIL>',
      userPhone: '+**********',

      // Provider info - CRITICAL: Use the correct Firebase Auth user ID
      providerId: providerId, // This should be KfUbx3q1QLXuItCVKHJ2liCJnrt2
      providerName: 'Test Provider',
      
      // Service info
      serviceName: 'Dog Walking',
      petName: 'Buddy',
      petType: 'Dog',
      petAge: '3 years',
      
      // Booking details
      scheduledDate: '2024-08-08',
      scheduledTime: '10:00 AM',
      duration: 60,
      totalPrice: 50,
      
      // Status and metadata
      status: 'pending_provider_approval',
      notes: 'Please be gentle with Buddy, he is a bit shy.',
      location: 'Central Park, New York',
      
      // Timestamps
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Add to Firestore
    const bookingsRef = collection(db, 'bookings');
    const docRef = await addDoc(bookingsRef, sampleBooking);

    console.log('✅ Sample booking created with ID:', docRef.id);

    return NextResponse.json({
      success: true,
      message: 'Sample booking created successfully',
      bookingId: docRef.id,
      booking: {
        id: docRef.id,
        ...sampleBooking,
        createdAt: sampleBooking.createdAt.toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Error creating sample booking:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create sample booking',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
