import { NextRequest, NextResponse } from 'next/server';
import { collection, addDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Firebase client connection...');
    
    // Test basic Firestore write
    const testData = {
      test: true,
      timestamp: new Date(),
      message: 'Firebase client test'
    };

    const testRef = collection(db, 'test');
    const docRef = await addDoc(testRef, testData);
    
    console.log('✅ Firebase client test successful, doc ID:', docRef.id);
    
    return NextResponse.json({
      success: true,
      message: 'Firebase client connection working',
      testDocId: docRef.id
    });

  } catch (error: any) {
    console.error('❌ Firebase client test failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Firebase client connection failed',
      details: error.message
    }, { status: 500 });
  }
}
