import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '@/lib/firebase/admin-config';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing booking creation...');
    
    // Test data
    const testBooking = {
      providerId: 'test-provider',
      userId: 'test-user',
      serviceName: 'Test Service',
      petName: 'Test Pet',
      petType: 'dog',
      scheduledDate: '2024-01-15',
      scheduledTime: '10:00',
      totalPrice: 50,
      status: 'pending_provider_approval',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Test Firebase Admin
    if (adminDb) {
      console.log('✅ Firebase Admin available');
      const docRef = await adminDb.collection('bookings').add(testBooking);
      console.log('✅ Test booking created with Admin SDK:', docRef.id);
      
      // Clean up test data
      await adminDb.collection('bookings').doc(docRef.id).delete();
      console.log('✅ Test booking cleaned up');
      
      return NextResponse.json({
        success: true,
        message: 'Firebase Admin SDK working correctly',
        testDocId: docRef.id
      });
    } else {
      console.log('❌ Firebase Admin not available');
      return NextResponse.json({
        success: false,
        error: 'Firebase Admin SDK not initialized'
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('❌ Booking test failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Booking test failed',
      details: error.message,
      code: error.code
    }, { status: 500 });
  }
}
