import { NextRequest, NextResponse } from 'next/server';
import { addDoc, collection } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { EmailService } from '@/lib/services/email-service';

export async function POST(request: NextRequest) {
  try {
    const { 
      userId, 
      userEmail, 
      userName, 
      userType, 
      subject, 
      message, 
      category,
      priority 
    } = await request.json();

    console.log('📝 Received feedback submission:', { 
      userId, 
      userEmail, 
      userName, 
      userType, 
      subject, 
      category 
    });

    if (!userEmail || !message || !subject) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: userEmail, subject, message'
      }, { status: 400 });
    }

    // Save feedback to Firebase
    const feedbackData = {
      userId: userId || null,
      userEmail,
      userName: userName || 'Anonymous User',
      userType: userType || 'unknown',
      subject,
      message,
      category: category || 'general',
      priority: priority || 'medium',
      status: 'new',
      createdAt: new Date(),
      updatedAt: new Date(),
      adminResponse: null,
      adminResponseAt: null,
    };

    const docRef = await addDoc(collection(db, 'feedback'), feedbackData);
    console.log('✅ Feedback saved to Firebase:', docRef.id);

    // Send email notification to admin
    try {
      const emailService = new EmailService();
      await emailService.sendFeedbackNotification({
        feedbackId: docRef.id,
        userEmail,
        userName: userName || 'Anonymous User',
        userType: userType || 'unknown',
        subject,
        message,
        category: category || 'general',
        priority: priority || 'medium',
        createdAt: new Date().toISOString()
      });
      console.log('📧 Email notification sent to admin');
    } catch (emailError) {
      console.error('❌ Failed to send email notification:', emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      feedbackId: docRef.id,
      message: 'Feedback submitted successfully. Thank you for helping us improve!'
    });

  } catch (error: any) {
    console.error('❌ Error submitting feedback:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to submit feedback',
      details: error.message
    }, { status: 500 });
  }
}
