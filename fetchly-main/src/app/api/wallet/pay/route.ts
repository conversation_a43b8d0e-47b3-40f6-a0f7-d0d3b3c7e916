import { NextRequest, NextResponse } from 'next/server';
import { FetchlyPaymentService } from '@/lib/stripe/production-service';
import { ProductionStripeService } from '@/lib/stripe/production-config';
import { logProductionEvent } from '@/lib/stripe/server-config';
import { auth as adminAuth } from '@/lib/firebase/admin-config';

export async function POST(request: NextRequest) {
  try {
    // Check if Firebase Admin SDK is initialized
    if (!adminAuth) {
      return NextResponse.json({
        error: 'Payment system not configured. Please contact support.'
      }, { status: 503 });
    }

    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];

    // Verify the Firebase token
    let decodedToken;
    try {
      if (!adminAuth) {
        throw new Error('Firebase Admin not initialized');
      }
      decodedToken = await adminAuth.verifyIdToken(token);
    } catch (error) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await request.json();
    const { amount, providerId, serviceId, description } = body;

    // Validate required fields
    if (!amount || !providerId || !serviceId || !description) {
      return NextResponse.json(
        { error: 'Missing required fields: amount, providerId, serviceId, description' },
        { status: 400 }
      );
    }

    // Validate amount
    if (amount < 1 || amount > 10000) {
      return NextResponse.json(
        { error: 'Amount must be between $1 and $10,000' },
        { status: 400 }
      );
    }

    // Process wallet payment
    const result = await PaymentService.payWithWallet({
      amount,
      userId: decodedToken.uid,
      providerId,
      serviceId,
      description,
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      transferId: result.transfer?.id,
      newBalance: result.newBalance,
      message: 'Payment successful',
    });

  } catch (error: any) {
    console.error('Error processing wallet payment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
