import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/server-config';
import { adminDb } from '@/lib/firebase/admin-config';
import { COLLECTIONS } from '@/lib/database';
import { requireAuth } from '@/lib/auth/server';
import { collection, doc, getDoc, getDocs, query, where, orderBy, limit, updateDoc, addDoc, serverTimestamp, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

interface Transaction {
  id: string;
  userId: string;
  amount: number;
  type: string;
  status: string;
  description?: string;
  createdAt: any;
  updatedAt: any;
}

interface WalletData {
  balance: number;
  currency: string;
  userId: string;
  stripeAccountId?: string;
  userType?: string;
  createdAt: any;
  updatedAt: any;
}

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const decodedToken = await requireAuth(request);
    const userId = decodedToken.uid;

    // Get wallet data
    const walletRef = doc(db, COLLECTIONS.WALLETS, userId);
    const walletDoc = await getDoc(walletRef);
    
    if (!walletDoc.exists()) {
      // Create a new wallet if it doesn't exist
      const newWallet = {
        userId,
        balance: 0,
        currency: 'usd',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };
      
      await setDoc(walletRef, newWallet);
      
      return NextResponse.json({
        success: true,
        balance: 0,
        recentTransactions: []
      });
    }

    const walletData = walletDoc.data();

    // Get recent transactions
    const recentTransactions: Transaction[] = [];
    try {
      const transactionsRef = collection(db, COLLECTIONS.TRANSACTIONS);
      const transactionsQuery = query(
        transactionsRef,
        where('userId', '==', userId),
        orderBy('createdAt', 'desc'),
        limit(10)
      );
      const transactionsSnapshot = await getDocs(transactionsQuery);
      
      transactionsSnapshot.forEach(doc => {
        const data = doc.data();
        recentTransactions.push({
          id: doc.id,
          userId: data.userId || userId,
          amount: data.amount || 0,
          type: data.type || 'unknown',
          status: data.status || 'completed',
          description: data.description,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date()
        });
      });
    } catch (error) {
      console.error('Error fetching transactions:', error);
      // Continue with empty transactions array if there's an error
    }

    // Get Stripe balance for providers
    let stripeBalance = null;
    if (walletData.stripeAccountId && walletData.userType === 'provider') {
      try {
        const balance = await stripe.balance.retrieve({
          stripeAccount: walletData.stripeAccountId,
        });
        
        stripeBalance = {
          available: balance.available.map(b => ({
            amount: b.amount / 100, // Convert from cents
            currency: b.currency
          })),
          pending: balance.pending.map(b => ({
            amount: b.amount / 100, // Convert from cents
            currency: b.currency
          }))
        };
      } catch (error) {
        console.error('Error fetching Stripe balance:', error);
      }
    }

    // Calculate statistics
    const stats = {
      // Fetchly wallet balance (stored in our DB)
      fetchlyBalance: walletData.balance || 0,
      pendingBalance: walletData.pendingBalance || 0,
      totalEarned: walletData.totalEarned || 0,
      totalSpent: walletData.totalSpent || 0,
      
      // Stripe balance (for providers)
      stripeBalance,
      
      // Transaction counts
      totalTransactions: recentTransactions.length,
      pendingTransactions: recentTransactions.filter(t => t.status === 'pending').length,
      completedTransactions: recentTransactions.filter(t => t.status === 'completed').length,
      
      // This month's activity
      thisMonthEarned: 0, // Will calculate from transactions
      thisMonthSpent: 0,   // Will calculate from transactions
    };

    // Calculate this month's activity
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    recentTransactions.forEach(transaction => {
      const transactionDate = new Date(transaction.createdAt);
      if (transactionDate.getMonth() === currentMonth && transactionDate.getFullYear() === currentYear) {
        if (transaction.type === 'earning' || transaction.type === 'payment_received') {
          stats.thisMonthEarned += transaction.amount || 0;
        } else if (transaction.type === 'payment' || transaction.type === 'wallet_payment') {
          stats.thisMonthSpent += transaction.amount || 0;
        }
      }
    });

    // Get account status for providers
    let accountStatus = null;
    if (walletData.stripeAccountId) {
      try {
        const account = await stripe.accounts.retrieve(walletData.stripeAccountId);
        accountStatus = {
          id: account.id,
          status: account.details_submitted 
            ? (account.charges_enabled ? 'active' : 'restricted')
            : 'pending',
          chargesEnabled: account.charges_enabled,
          payoutsEnabled: account.payouts_enabled,
          detailsSubmitted: account.details_submitted,
          requirements: {
            currentlyDue: account.requirements?.currently_due || [],
            eventuallyDue: account.requirements?.eventually_due || [],
            pastDue: account.requirements?.past_due || [],
            disabledReason: account.requirements?.disabled_reason,
          }
        };
      } catch (error) {
        console.error('Error fetching account status:', error);
      }
    }

    return NextResponse.json({
      success: true,
      wallet: {
        id: userId,
        userType: walletData.userType,
        isActive: walletData.isActive,
        isVerified: walletData.isVerified,
        stripeCustomerId: walletData.stripeCustomerId,
        stripeAccountId: walletData.stripeAccountId,
        createdAt: walletData.createdAt,
        updatedAt: walletData.updatedAt,
      },
      stats,
      accountStatus,
      recentTransactions: recentTransactions.slice(0, 5), // Only return 5 most recent
    });

  } catch (error: any) {
    console.error('Wallet balance fetch error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch wallet balance' },
      { status: 500 }
    );
  }
}

// Update wallet balance (internal use only)
export async function PATCH(request: NextRequest) {
  try {
    // Verify authentication
    const decodedToken = await requireAuth(request);
    const userId = decodedToken.uid;
    const { amount, type, description } = await request.json();

    if (!amount || !type) {
      return NextResponse.json(
        { error: 'Amount and type are required' },
        { status: 400 }
      );
    }

    // Get current wallet
    const walletRef = doc(db, COLLECTIONS.WALLETS, userId);
    const walletDoc = await getDoc(walletRef);
    
    if (!walletDoc.exists()) {
      return NextResponse.json(
        { error: 'Wallet not found' },
        { status: 404 }
      );
    }

    const walletData = walletDoc.data();
    const currentBalance = walletData.balance || 0;

    // Calculate new balance
    let newBalance = currentBalance;
    if (type === 'add') {
      newBalance += amount;
    } else if (type === 'subtract') {
      if (currentBalance < amount) {
        return NextResponse.json(
          { error: 'Insufficient balance' },
          { status: 400 }
        );
      }
      newBalance -= amount;
    }

    // Update wallet balance
    await updateDoc(walletRef, {
      balance: newBalance,
      updatedAt: new Date().toISOString(),
    });

    // Record transaction
    const transactionData = {
      userId,
      type: type === 'add' ? 'wallet_topup' : 'wallet_payment',
      amount,
      description: description || `Wallet ${type}`,
      status: 'completed',
      balanceBefore: currentBalance,
      balanceAfter: newBalance,
      createdAt: new Date().toISOString(),
    };

    await addDoc(collection(db, COLLECTIONS.TRANSACTIONS), transactionData);

    return NextResponse.json({
      success: true,
      newBalance,
      transaction: transactionData
    });

  } catch (error: any) {
    console.error('Wallet balance update error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update wallet balance' },
      { status: 500 }
    );
  }
}
