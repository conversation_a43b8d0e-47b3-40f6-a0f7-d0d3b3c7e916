import { NextRequest, NextResponse } from 'next/server';
import { PaymentService } from '@/lib/stripe/payment-service';
import { requireAuth } from '@/lib/auth/server';

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const decodedToken = await requireAuth(request);
    const userId = decodedToken.uid;

    const body = await request.json();
    const { amount, paymentMethodId } = body;

    // Validate required fields
    if (!amount) {
      return NextResponse.json(
        { error: 'Amount is required' },
        { status: 400 }
      );
    }

    // Validate amount
    if (amount < 1 || amount > 500) {
      return NextResponse.json(
        { error: 'Amount must be between $1 and $500' },
        { status: 400 }
      );
    }

    // Create wallet topup
    const result = await PaymentService.topupWallet({
      amount,
      customerId: userId,
      paymentMethodId,
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      clientSecret: result.clientSecret,
      paymentIntentId: result.paymentIntent?.id,
    });

  } catch (error: any) {
    console.error('Error topping up wallet:', error);

    // Always return JSON, never HTML
    const errorMessage = error.message || 'Internal server error';

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
