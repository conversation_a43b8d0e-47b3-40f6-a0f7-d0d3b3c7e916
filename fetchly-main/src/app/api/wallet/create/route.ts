import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/server-config';
import { adminDb } from '@/lib/firebase/admin-config';
import { COLLECTIONS } from '@/lib/database';
import { verifyIdToken } from '@/lib/auth/server';

export async function POST(request: NextRequest) {
  // Global error wrapper to ensure we always return JSON
  try {
    console.log('🔧 Wallet creation API called');

    // Check if firebase-admin is available
    if (!process.env.FIREBASE_ADMIN_PROJECT_ID) {
      console.error('❌ Firebase Admin not configured');
      return NextResponse.json(
        { success: false, error: 'Server configuration error - Firebase Admin not available' },
        { status: 500 }
      );
    }

    // Check if Stripe is available
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('❌ Stripe not configured');
      return NextResponse.json(
        { success: false, error: 'Server configuration error - Stripe not available' },
        { status: 500 }
      );
    }
    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Invalid token' },
        { status: 401 }
      );
    }

    const userId = decodedToken.uid;
    const { userType = 'provider' } = await request.json(); // Default to provider for provider dashboard

    console.log(`🔍 Creating wallet for user: ${userId}, type: ${userType}`);

    // Check if adminDb is available
    if (!adminDb) {
      console.error('❌ Firebase Admin DB not initialized');
      return NextResponse.json(
        { success: false, error: 'Firebase Admin not initialized - check environment variables' },
        { status: 500 }
      );
    }

    console.log('✅ Firebase Admin DB initialized successfully');

    // Get user data - All users (pet owners and providers) are in the 'users' collection
    const userRef = adminDb.collection(COLLECTIONS.USERS).doc(userId);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      console.error(`❌ User not found in users collection: ${userId}`);
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    const userData = userDoc.data();
    console.log(`✅ User found: ${userData?.email || 'No email'}, role: ${userData?.role || 'No role'}`);

    // Check if wallet already exists
    const walletRef = adminDb.collection(COLLECTIONS.WALLETS).doc(userId);
    const walletDoc = await walletRef.get();

    if (walletDoc.exists) {
      return NextResponse.json({
        success: true,
        wallet: walletDoc.data(),
        message: 'Wallet already exists'
      });
    }

    // 1. Create Stripe Customer (for all users)
    let stripeCustomerId = userData.stripeCustomerId;
    
    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        name: userData.name || userData.businessName,
        email: userData.email,
        metadata: {
          firebaseUid: userId,
          userType,
          platform: 'fetchly',
        },
      });

      stripeCustomerId = customer.id;

      // Save customer ID to user record
      await userRef.update({
        stripeCustomerId,
        updatedAt: new Date().toISOString(),
      });

      console.log(`✅ Created Stripe customer ${stripeCustomerId} for ${userType} ${userId}`);
    }

    // 2. Create Stripe Connected Account (for providers only)
    let stripeAccountId = userData.stripeAccountId;
    
    if (userType === 'provider' && !stripeAccountId) {
      try {
        console.log('🔄 Creating Stripe Connect account...');
        const account = await stripe.accounts.create({
          type: 'express',
          country: 'US',
          email: userData.email,
          business_profile: {
            name: userData.businessName || userData.name,
            product_description: `Pet care services provided by ${userData.businessName || userData.name}`,
            support_email: userData.email,
            url: `${process.env.NEXTAUTH_URL}/provider/${userId}`,
          },
          capabilities: {
            card_payments: { requested: true },
            transfers: { requested: true },
          },
          settings: {
            payouts: {
              schedule: {
                interval: 'daily',
              },
            },
          },
          metadata: {
            firebaseUid: userId,
            userType: 'provider',
            platform: 'fetchly',
          },
        });

        stripeAccountId = account.id;
      } catch (stripeError: any) {
        console.error('❌ Stripe Connect account creation failed:', stripeError);

        if (stripeError.message?.includes('signed up for Connect')) {
          return NextResponse.json({
            success: false,
            error: 'Stripe Connect not enabled',
            message: 'Please enable Stripe Connect in your Stripe dashboard first.',
            instructions: [
              '1. Go to https://dashboard.stripe.com/connect/overview',
              '2. Click "Get started with Connect"',
              '3. Complete the Connect setup process',
              '4. Enable Express accounts for your platform'
            ],
            action: 'setup_connect'
          }, { status: 400 });
        }

        return NextResponse.json({
          success: false,
          error: 'Failed to create Stripe account',
          message: stripeError.message || 'Unknown Stripe error',
          details: process.env.NODE_ENV === 'development' ? stripeError.stack : undefined
        }, { status: 500 });
      }

      // Save account ID to provider record
      await userRef.update({
        stripeAccountId,
        stripeAccountStatus: 'pending',
        updatedAt: new Date().toISOString(),
      });

      console.log(`✅ Created Stripe Connect account ${stripeAccountId} for provider ${userId}`);

      // Create onboarding link for the provider
      try {
        const accountLink = await stripe.accountLinks.create({
          account: stripeAccountId,
          refresh_url: `${process.env.NEXTAUTH_URL}/provider/dashboard?tab=wallet&refresh=true`,
          return_url: `${process.env.NEXTAUTH_URL}/provider/dashboard?tab=wallet&success=true`,
          type: 'account_onboarding',
        });

        console.log(`✅ Created onboarding link for provider ${userId}`);

        // Store the onboarding link temporarily
        await userRef.update({
          stripeOnboardingUrl: accountLink.url,
          stripeOnboardingExpires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
        });
      } catch (onboardingError: any) {
        console.error('❌ Failed to create onboarding link:', onboardingError);
        // Don't fail the wallet creation if onboarding link fails
      }
    }

    // 3. Initialize Wallet in Database
    const walletData = {
      userId,
      userType,
      stripeCustomerId,
      ...(userType === 'provider' && { stripeAccountId }),
      
      // Balance tracking
      balance: 0,
      pendingBalance: 0,
      totalEarned: 0,
      totalSpent: 0,
      
      // Payment methods
      defaultPaymentMethod: null,
      paymentMethods: [],
      
      // Settings
      autoTopup: false,
      autoTopupAmount: 25,
      autoTopupThreshold: 10,
      
      // Status
      isActive: true,
      isVerified: false,
      
      // Timestamps
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Save wallet to database
    await walletRef.set(walletData);

    // Send admin notification
    try {
      await fetch(`${process.env.NEXTAUTH_URL}/api/send-email`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'admin_notification',
          to: '<EMAIL>',
          subject: `💳 New Wallet Created - ${userData.name || userData.businessName} (${userType})`,
          data: {
            type: 'wallet_created',
            userId,
            userType,
            userName: userData.name || userData.businessName,
            userEmail: userData.email,
            stripeCustomerId,
            ...(stripeAccountId && { stripeAccountId }),
            timestamp: new Date()
          }
        })
      });
    } catch (emailError) {
      console.error('Failed to send admin notification:', emailError);
    }

    // Get the updated user data with onboarding URL
    const updatedUserDoc = await userRef.get();
    const updatedUserData = updatedUserDoc.data();

    const response = {
      success: true,
      wallet: walletData,
      message: userType === 'provider' ? 'Wallet created! Complete your Stripe onboarding to start receiving payments.' : 'Wallet created successfully'
    };

    // Add onboarding URL for providers
    if (userType === 'provider' && updatedUserData?.stripeOnboardingUrl) {
      response.onboarding = {
        url: updatedUserData.stripeOnboardingUrl,
        expires: updatedUserData.stripeOnboardingExpires,
        required: true
      };
    }

    return NextResponse.json(response);

  } catch (error: any) {
    console.error('Wallet creation error:', error);

    // Always return JSON, never HTML
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to create wallet',
        details: process.env.NODE_ENV === 'development' ? {
          stack: error.stack,
          name: error.name,
          cause: error.cause
        } : undefined
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Invalid token' },
        { status: 401 }
      );
    }

    const userId = decodedToken.uid;

    // Check if adminDb is available
    if (!adminDb) {
      return NextResponse.json(
        { success: false, error: 'Firebase Admin not initialized' },
        { status: 500 }
      );
    }

    // Get wallet data
    const walletRef = adminDb.collection(COLLECTIONS.WALLETS).doc(userId);
    const walletDoc = await walletRef.get();

    if (!walletDoc.exists) {
      return NextResponse.json({
        success: false,
        exists: false,
        message: 'Wallet not found'
      });
    }

    const walletData = walletDoc.data();

    // Get real-time Stripe data if available
    if (walletData.stripeCustomerId) {
      try {
        const customer = await stripe.customers.retrieve(walletData.stripeCustomerId);
        walletData.stripeCustomerStatus = customer.deleted ? 'deleted' : 'active';
      } catch (error) {
        console.error('Error fetching Stripe customer:', error);
      }
    }

    if (walletData.stripeAccountId) {
      try {
        const account = await stripe.accounts.retrieve(walletData.stripeAccountId);
        walletData.stripeAccountStatus = account.details_submitted 
          ? (account.charges_enabled ? 'active' : 'restricted')
          : 'pending';
        walletData.stripeChargesEnabled = account.charges_enabled;
        walletData.stripePayoutsEnabled = account.payouts_enabled;
      } catch (error) {
        console.error('Error fetching Stripe account:', error);
      }
    }

    return NextResponse.json({
      success: true,
      exists: true,
      wallet: walletData
    });

  } catch (error: any) {
    console.error('Wallet fetch error:', error);

    // Always return JSON, never HTML
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to fetch wallet',
        details: process.env.NODE_ENV === 'development' ? {
          stack: error.stack,
          name: error.name,
          cause: error.cause
        } : undefined
      },
      { status: 500 }
    );
  }
}
