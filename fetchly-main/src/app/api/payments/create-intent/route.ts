import { NextRequest, NextResponse } from 'next/server';
import { FetchlyPaymentService } from '@/lib/stripe/production-service';
import { ProductionStripeService } from '@/lib/stripe/production-config';
import { logProductionEvent } from '@/lib/stripe/server-config';
import { auth as adminAuth } from '@/lib/firebase/admin-config';

export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];

    // Verify the Firebase token
    let decodedToken;
    try {
      if (!adminAuth) {
        throw new Error('Firebase Admin not initialized');
      }
      decodedToken = await adminAuth.verifyIdToken(token);
    } catch (error: any) {
      logProductionEvent('auth_failed', { error: error.message }, 'error');
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await request.json();
    const { amount, type, paymentMethodId, providerId, serviceId, serviceName } = body;

    // Validate required fields
    if (!amount || !type) {
      return NextResponse.json(
        { error: 'Missing required fields: amount, type' },
        { status: 400 }
      );
    }

    // Validate payment type
    if (!['wallet_topup', 'service_payment'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid payment type. Must be wallet_topup or service_payment' },
        { status: 400 }
      );
    }

    let result;

    if (type === 'wallet_topup') {
      // Validate wallet top-up amount
      if (!ProductionStripeService.validatePaymentAmount(amount, 'wallet')) {
        return NextResponse.json(
          { error: `Wallet top-up amount must be between $5 and $500` },
          { status: 400 }
        );
      }

      result = await FetchlyPaymentService.createWalletTopUpIntent(
        decodedToken.uid,
        amount,
        paymentMethodId
      );
    } else if (type === 'service_payment') {
      // Validate service payment fields
      if (!providerId || !serviceId || !serviceName) {
        return NextResponse.json(
          { error: 'Missing required fields for service payment: providerId, serviceId, serviceName' },
          { status: 400 }
        );
      }

      // For service payments, we'll create a checkout session instead
      const successUrl = `${request.headers.get('origin')}/dashboard?payment=success`;
      const cancelUrl = `${request.headers.get('origin')}/dashboard?payment=canceled`;

      result = await FetchlyPaymentService.createServiceCheckoutSession(
        serviceId,
        providerId,
        decodedToken.uid,
        amount,
        serviceName,
        successUrl,
        cancelUrl
      );

      if (result.success) {
        return NextResponse.json({
          success: true,
          sessionUrl: result.sessionUrl,
          type: 'checkout_session',
        });
      }
    }

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      clientSecret: result.clientSecret,
      type: 'payment_intent',
    });

  } catch (error: any) {
    logProductionEvent('payment_intent_creation_failed', { error: error.message }, 'error');
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
