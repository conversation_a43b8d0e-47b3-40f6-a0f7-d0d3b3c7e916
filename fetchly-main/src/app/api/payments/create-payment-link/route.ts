import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export async function POST(request: NextRequest) {
  try {
    const { bookingId, providerId, amount, description } = await request.json();

    if (!bookingId || !providerId || !amount) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: bookingId, providerId, amount'
      }, { status: 400 });
    }

    // Get provider data to check Stripe account - try both 'providers' and 'users' collections
    let providerRef = doc(db, 'providers', providerId);
    let providerDoc = await getDoc(providerRef);

    // If not found in providers, try users collection
    if (!providerDoc.exists()) {
      console.log('🔍 Provider not found in providers collection, trying users...');
      providerRef = doc(db, 'users', providerId);
      providerDoc = await getDoc(providerRef);
    }

    if (!providerDoc.exists()) {
      console.error('❌ Provider not found in either collection:', providerId);
      return NextResponse.json({
        success: false,
        error: 'Provider not found'
      }, { status: 404 });
    }

    const providerData = providerDoc.data();
    const stripeAccountId = providerData.stripeAccountId;

    if (!stripeAccountId) {
      return NextResponse.json({
        success: false,
        error: 'Provider has not connected their Stripe account'
      }, { status: 400 });
    }

    // Check if Stripe account is active
    const account = await stripe.accounts.retrieve(stripeAccountId);
    if (!account.charges_enabled) {
      return NextResponse.json({
        success: false,
        error: 'Provider Stripe account is not fully set up'
      }, { status: 400 });
    }

    // Get booking data
    const bookingRef = doc(db, 'bookings', bookingId);
    const bookingDoc = await getDoc(bookingRef);
    
    if (!bookingDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Booking not found'
      }, { status: 404 });
    }

    const booking = bookingDoc.data();

    // Calculate platform fee (5% of total amount)
    const amountInCents = Math.round(amount * 100);
    const platformFee = Math.round(amountInCents * 0.05);

    // Create payment link using provider's connected account
    const paymentLink = await stripe.paymentLinks.create({
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: description || `${booking.serviceName} for ${booking.petName}`,
              description: `Pet service provided by ${providerData.businessName || providerData.name}`,
            },
            unit_amount: amountInCents,
          },
          quantity: 1,
        },
      ],
      application_fee_amount: platformFee,
      transfer_data: {
        destination: stripeAccountId,
      },
      after_completion: {
        type: 'redirect',
        redirect: {
          url: `${process.env.NEXT_PUBLIC_APP_URL}/booking-success/${bookingId}?payment=success`,
        },
      },
      metadata: {
        bookingId,
        providerId,
        customerId: booking.userId,
        type: 'booking_payment',
        platformFee: (platformFee / 100).toString(),
      },
    });

    // Update booking with payment link
    await updateDoc(bookingRef, {
      paymentLink: paymentLink.url,
      paymentLinkId: paymentLink.id,
      paymentRequired: {
        amount,
        currency: 'USD',
        status: 'pending',
        requestedAt: new Date(),
        stripePaymentLinkId: paymentLink.id,
      },
      updatedAt: new Date(),
    });

    return NextResponse.json({
      success: true,
      paymentLink: paymentLink.url,
      paymentLinkId: paymentLink.id,
      message: 'Payment link created successfully'
    });

  } catch (error: any) {
    console.error('Error creating payment link:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create payment link',
      details: error.message
    }, { status: 500 });
  }
}
