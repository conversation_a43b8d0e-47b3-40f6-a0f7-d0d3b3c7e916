import { NextResponse } from 'next/server';
import { auth, adminDb } from '@/lib/firebase/admin-config';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
});

export async function GET(req: Request) {
  try {
    // Get the Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ 
        success: false,
        error: 'Unauthorized' 
      }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    
    // Verify the Firebase token
    if (!auth || !adminDb) {
      console.error('Firebase Admin not initialized');
      return NextResponse.json({ 
        success: false,
        error: 'Server configuration error' 
      }, { status: 500 });
    }

    let decodedToken;
    try {
      decodedToken = await auth.verifyIdToken(token);
    } catch (error) {
      console.error('Error verifying token:', error);
      return NextResponse.json({ 
        success: false,
        error: 'Unauthorized' 
      }, { status: 401 });
    }

    // Get user data from users collection (all users are stored there)
    const userDoc = await adminDb.collection('users').doc(decodedToken.uid).get();

    if (!userDoc.exists) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    const userData = userDoc.data();

    // Check if user is a provider
    if (userData?.role !== 'provider') {
      return NextResponse.json({
        success: false,
        error: 'Access denied - not a provider'
      }, { status: 403 });
    }

    const stripeAccountId = userData.stripeAccountId;

    // If no Stripe account, return onboarding needed
    if (!stripeAccountId) {
      return NextResponse.json({
        success: true,
        isOnboarded: false,
        balance: { available: 0, pending: 0 },
        payouts: [],
        error: 'Stripe account not connected. Please complete onboarding.',
      });
    }

    try {
      // Get account details
      const account = await stripe.accounts.retrieve(stripeAccountId);
      
      // Get balance
      const balance = await stripe.balance.retrieve({
        stripeAccount: stripeAccountId,
      });

      // Get recent payouts
      const payouts = await stripe.payouts.list({
        limit: 10,
        stripeAccount: stripeAccountId,
      });

      // Check if account is fully onboarded
      const isOnboarded = account.details_submitted && 
                         account.charges_enabled && 
                         account.payouts_enabled;

      return NextResponse.json({
        success: true,
        isOnboarded,
        accountId: stripeAccountId,
        status: {
          detailsSubmitted: account.details_submitted,
          chargesEnabled: account.charges_enabled,
          payoutsEnabled: account.payouts_enabled,
        },
        balance: {
          available: balance.available.reduce((sum, bal) => sum + bal.amount, 0) / 100,
          pending: balance.pending.reduce((sum, bal) => sum + bal.amount, 0) / 100,
        },
        payouts: payouts.data.map(payout => ({
          id: payout.id,
          amount: payout.amount / 100,
          currency: payout.currency,
          status: payout.status,
          arrival_date: payout.arrival_date,
          created: payout.created,
        })),
      });

    } catch (stripeError: any) {
      console.error('Stripe API error:', stripeError);
      
      // If account doesn't exist or has issues, return onboarding needed
      return NextResponse.json({
        success: true,
        isOnboarded: false,
        balance: { available: 0, pending: 0 },
        payouts: [],
        error: 'Unable to retrieve wallet data. Please complete Stripe onboarding.',
      });
    }

  } catch (error: any) {
    console.error('Wallet API error:', error);
    return NextResponse.json({ 
      success: false,
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
