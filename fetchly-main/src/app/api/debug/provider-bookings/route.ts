import { NextRequest, NextResponse } from 'next/server';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const providerId = searchParams.get('providerId');

    if (!providerId) {
      return NextResponse.json({ error: 'Provider ID is required' }, { status: 400 });
    }

    console.log('🔍 DEBUG: Checking bookings for provider:', providerId);

    // Get all bookings
    const allBookingsQuery = query(collection(db, 'bookings'));
    const allBookingsSnapshot = await getDocs(allBookingsQuery);
    
    console.log('📊 Total bookings in database:', allBookingsSnapshot.size);

    const allBookings = allBookingsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Get bookings for this specific provider
    const providerBookingsQuery = query(
      collection(db, 'bookings'),
      where('providerId', '==', providerId)
    );
    const providerBookingsSnapshot = await getDocs(providerBookingsQuery);
    
    console.log('📊 Bookings for provider:', providerBookingsSnapshot.size);

    const providerBookings = providerBookingsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Get provider data
    const providersQuery = query(collection(db, 'providers'));
    const providersSnapshot = await getDocs(providersQuery);
    
    const providers = providersSnapshot.docs.map(doc => ({
      documentId: doc.id,
      ...doc.data()
    }));

    return NextResponse.json({
      success: true,
      providerId,
      totalBookings: allBookings.length,
      providerBookings: providerBookings.length,
      allBookings: allBookings.map(b => ({
        id: b.id,
        providerId: b.providerId,
        status: b.status,
        customerName: b.customerName || b.userName,
        serviceName: b.serviceName,
        scheduledDate: b.scheduledDate,
        scheduledTime: b.scheduledTime
      })),
      providerBookings: providerBookings.map(b => ({
        id: b.id,
        providerId: b.providerId,
        status: b.status,
        customerName: b.customerName || b.userName,
        serviceName: b.serviceName,
        scheduledDate: b.scheduledDate,
        scheduledTime: b.scheduledTime
      })),
      providers: providers.map(p => ({
        documentId: p.documentId,
        userId: p.userId || p.uid || 'NO_USER_ID',
        businessName: p.businessName || p.name,
        email: p.email
      }))
    });

  } catch (error) {
    console.error('❌ Error in debug endpoint:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch debug data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
