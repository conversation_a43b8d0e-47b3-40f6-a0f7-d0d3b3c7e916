import { NextRequest, NextResponse } from 'next/server';
import { professionalEmailService } from '@/lib/services/professional-email-service';

export async function POST(request: NextRequest) {
  try {
    const { email, displayName } = await request.json();

    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      );
    }

    // Send professional email verification
    const result = await professionalEmailService.sendEmailVerification(email, displayName);
    
    if (result.success) {
      // Send admin notification
      await professionalEmailService.notifyAdminEmailVerification(email, displayName);
      
      return NextResponse.json({
        success: true,
        message: 'Email verification sent successfully'
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Email verification API error:', error);
    
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to send email verification' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Email verification endpoint. Use POST to send verification email.'
  });
}
