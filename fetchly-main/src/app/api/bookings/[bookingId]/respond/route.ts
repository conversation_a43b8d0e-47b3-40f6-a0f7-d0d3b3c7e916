import { NextRequest, NextResponse } from 'next/server';
import { doc, updateDoc, getDoc, addDoc, collection } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { CalendarService } from '@/lib/services/calendar-service';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

/**
 * Provider responds to booking request (accept/reject)
 * POST /api/bookings/[bookingId]/respond
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { bookingId: string } }
) {
  try {
    console.log('📋 Provider responding to booking request...');
    
    const { bookingId } = params;
    const { action, providerId, message, paymentAmount } = await request.json();
    
    console.log('📝 Response data:', { bookingId, action, providerId });
    
    // Validate required fields
    if (!bookingId || !action || !providerId) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: bookingId, action, providerId'
      }, { status: 400 });
    }
    
    if (!['accept', 'reject'].includes(action)) {
      return NextResponse.json({
        success: false,
        error: 'Action must be either "accept" or "reject"'
      }, { status: 400 });
    }
    
    // Get the booking
    const bookingRef = doc(db, 'bookings', bookingId);
    const bookingDoc = await getDoc(bookingRef);
    
    if (!bookingDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Booking not found'
      }, { status: 404 });
    }
    
    const booking = bookingDoc.data();
    
    // Verify the provider owns this booking
    if (booking.providerId !== providerId) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized to modify this booking'
      }, { status: 403 });
    }
    
    // Update booking status
    const newStatus = action === 'accept' ? 'accepted' : 'rejected';
    const updateData: any = {
      status: newStatus,
      providerResponse: {
        action,
        message: message || '',
        respondedAt: new Date(),
        respondedBy: providerId
      },
      updatedAt: new Date()
    };

    // Add payment information for accepted bookings
    if (action === 'accept' && paymentAmount) {
      updateData.paymentRequired = {
        amount: paymentAmount,
        currency: 'USD',
        status: 'pending',
        requestedAt: new Date()
      };

      // Add calendar entry for accepted bookings
      updateData.calendarEntry = {
        addedToCalendar: true,
        calendarEventId: `booking_${bookingId}_${Date.now()}`,
        addedAt: new Date()
      };
    }
    
    await updateDoc(bookingRef, updateData);
    console.log(`✅ Booking ${action}ed successfully`);

    // Add to calendar if booking is accepted
    if (action === 'accept') {
      try {
        await CalendarService.addBookingToCalendar(providerId, bookingId, {
          customerName: booking.userName || 'Customer',
          customerEmail: booking.userEmail || '',
          customerPhone: booking.userPhone,
          serviceName: booking.serviceName || 'Service',
          petName: booking.petName || 'Pet',
          petType: booking.petType || 'pet',
          scheduledDate: booking.scheduledDate || '',
          scheduledTime: booking.scheduledTime || '',
          duration: booking.duration || 60,
          notes: booking.notes
        });
        console.log('✅ Booking added to calendar successfully');
      } catch (calendarError) {
        console.error('❌ Failed to add booking to calendar:', calendarError);
        // Don't fail the booking response if calendar fails
      }
    }

    // Send notification to customer
    try {
      const notificationsRef = collection(db, 'notifications');
      const notificationTitle = action === 'accept'
        ? '🎉 Booking Accepted!'
        : '😔 Booking Declined';

      let notificationMessage;
      if (action === 'accept') {
        if (paymentAmount) {
          notificationMessage = `${booking.providerName} accepted your booking for ${booking.serviceName}. Payment required: $${paymentAmount}. You'll receive a payment link shortly.`;
        } else {
          notificationMessage = `${booking.providerName} accepted your booking for ${booking.serviceName}. You'll receive payment instructions soon.`;
        }
      } else {
        notificationMessage = `${booking.providerName} declined your booking for ${booking.serviceName}. ${message || 'Please try booking with another provider.'}`;
      }
      
      await addDoc(notificationsRef, {
        userId: booking.userId,
        title: notificationTitle,
        message: notificationMessage,
        type: `booking_${action}ed`,
        data: {
          bookingId,
          providerName: booking.providerName,
          serviceName: booking.serviceName,
          petName: booking.petName,
          scheduledDate: booking.scheduledDate,
          scheduledTime: booking.scheduledTime,
          totalPrice: booking.totalPrice,
          providerMessage: message,
          paymentAmount: paymentAmount || null,
          paymentRequired: action === 'accept' && paymentAmount ? true : false
        },
        read: false,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log('✅ Customer notification sent');
    } catch (error) {
      console.error('❌ Error sending customer notification:', error);
      // Don't fail the response if notification fails
    }
    
    const response = NextResponse.json({
      success: true,
      message: `Booking ${action}ed successfully`,
      booking: {
        id: bookingId,
        status: newStatus,
        customerName: booking.userName,
        serviceName: booking.serviceName,
        petName: booking.petName,
        scheduledDate: booking.scheduledDate,
        scheduledTime: booking.scheduledTime,
        totalPrice: booking.totalPrice
      }
    });
    
    // Add CORS headers
    Object.entries(corsHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    return response;
    
  } catch (error: any) {
    console.error('❌ Error responding to booking:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to respond to booking',
      details: error.message
    }, { status: 500 });
  }
}

export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: corsHeaders,
  });
}
