import { NextRequest, NextResponse } from 'next/server';
import { auth, adminDb } from '@/lib/firebase/admin-config';
import { sendEmail } from '@/lib/services/email-service';

export async function POST(
  req: NextRequest,
  { params }: { params: { bookingId: string } }
) {
  try {
    // Get the Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ 
        success: false,
        error: 'Unauthorized' 
      }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    
    // Verify the Firebase token
    if (!auth || !adminDb) {
      console.error('Firebase Admin not initialized');
      return NextResponse.json({ 
        success: false,
        error: 'Server configuration error' 
      }, { status: 500 });
    }

    let decodedToken;
    try {
      decodedToken = await auth.verifyIdToken(token);
    } catch (error) {
      console.error('Error verifying token:', error);
      return NextResponse.json({ 
        success: false,
        error: 'Unauthorized' 
      }, { status: 401 });
    }

    const { bookingId } = params;
    
    // Get the booking
    const bookingRef = adminDb.collection('bookings').doc(bookingId);
    const bookingDoc = await bookingRef.get();
    
    if (!bookingDoc.exists) {
      return NextResponse.json({
        success: false,
        error: 'Booking not found'
      }, { status: 404 });
    }

    const booking = bookingDoc.data();
    
    // Verify the provider owns this booking
    if (booking?.providerId !== decodedToken.uid) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized to modify this booking'
      }, { status: 403 });
    }

    // Get rejection reason from request body
    const body = await req.json();
    const rejectionReason = body.reason || 'No reason provided';

    // Update booking status
    await bookingRef.update({
      status: 'rejected',
      updatedAt: new Date().toISOString(),
      rejectedAt: new Date().toISOString(),
      rejectionReason
    });

    // Send notifications
    try {
      // Email to pet owner
      await sendEmail({
        to: booking.bookingDetails.contactEmail,
        subject: `Booking Update - ${booking.serviceDetails.name}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 30px; text-align: center;">
              <h1 style="margin: 0; font-size: 28px;">Booking Update</h1>
              <p style="margin: 10px 0 0 0; opacity: 0.9;">Regarding your service request</p>
            </div>
            <div style="padding: 30px; background: #f8f9fa;">
              <h2 style="color: #333; margin-bottom: 20px;">Booking Status Update</h2>
              <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <p>Unfortunately, <strong>${booking.providerDetails.businessName}</strong> is unable to accommodate your booking request for <strong>${booking.serviceDetails.name}</strong> at this time.</p>
                ${rejectionReason !== 'No reason provided' ? `
                  <div style="background: #fef3c7; padding: 15px; border-radius: 6px; margin: 15px 0;">
                    <p style="margin: 0;"><strong>Reason:</strong> ${rejectionReason}</p>
                  </div>
                ` : ''}
                <p><strong>What you can do:</strong></p>
                <ul style="color: #333; padding-left: 20px;">
                  <li>Try booking for a different date or time</li>
                  <li>Contact the provider directly to discuss alternatives</li>
                  <li>Browse other providers offering similar services</li>
                </ul>
              </div>
              <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h3 style="color: #333; margin-top: 0;">Original Request Details</h3>
                <p><strong>Service:</strong> ${booking.serviceDetails.name}</p>
                <p><strong>Pet:</strong> ${booking.bookingDetails.petName} (${booking.bookingDetails.petType})</p>
                <p><strong>Requested Date:</strong> ${booking.bookingDetails.preferredDate}</p>
                <p><strong>Requested Time:</strong> ${booking.bookingDetails.preferredTime}</p>
              </div>
              <div style="text-align: center; margin-top: 30px;">
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/community" 
                   style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold; margin-right: 10px;">
                  Find Other Providers
                </a>
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard" 
                   style="background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
                  View Your Bookings
                </a>
              </div>
            </div>
          </div>
        `
      });

      // In-app notification to pet owner
      await adminDb.collection('notifications').add({
        userId: booking.petOwnerId,
        title: 'Booking Update',
        message: `${booking.providerDetails.businessName} is unable to accommodate your ${booking.serviceDetails.name} request`,
        type: 'booking_rejected',
        data: { bookingId, rejectionReason },
        read: false,
        createdAt: new Date().toISOString()
      });

    } catch (notificationError) {
      console.error('Error sending notifications:', notificationError);
      // Don't fail the booking if notifications fail
    }

    return NextResponse.json({
      success: true,
      message: 'Booking rejected successfully'
    });

  } catch (error: any) {
    console.error('Reject booking error:', error);
    return NextResponse.json({ 
      success: false,
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
