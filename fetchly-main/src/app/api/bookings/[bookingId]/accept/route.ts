import { NextRequest, NextResponse } from 'next/server';
import { auth, adminDb } from '@/lib/firebase/admin-config';
import { sendEmail } from '@/lib/services/email-service';

export async function POST(
  req: NextRequest,
  { params }: { params: { bookingId: string } }
) {
  try {
    // Get the Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ 
        success: false,
        error: 'Unauthorized' 
      }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    
    // Verify the Firebase token
    if (!auth || !adminDb) {
      console.error('Firebase Admin not initialized');
      return NextResponse.json({ 
        success: false,
        error: 'Server configuration error' 
      }, { status: 500 });
    }

    let decodedToken;
    try {
      decodedToken = await auth.verifyIdToken(token);
    } catch (error) {
      console.error('Error verifying token:', error);
      return NextResponse.json({ 
        success: false,
        error: 'Unauthorized' 
      }, { status: 401 });
    }

    const { bookingId } = params;
    
    // Get the booking
    const bookingRef = adminDb.collection('bookings').doc(bookingId);
    const bookingDoc = await bookingRef.get();
    
    if (!bookingDoc.exists) {
      return NextResponse.json({
        success: false,
        error: 'Booking not found'
      }, { status: 404 });
    }

    const booking = bookingDoc.data();
    
    // Verify the provider owns this booking
    if (booking?.providerId !== decodedToken.uid) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized to modify this booking'
      }, { status: 403 });
    }

    // Update booking status
    await bookingRef.update({
      status: 'accepted',
      updatedAt: new Date().toISOString(),
      acceptedAt: new Date().toISOString()
    });

    // Send notifications
    try {
      // Email to pet owner
      await sendEmail({
        to: booking.bookingDetails.contactEmail,
        subject: `Booking Accepted - ${booking.serviceDetails.name}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 30px; text-align: center;">
              <h1 style="margin: 0; font-size: 28px;">🎉 Booking Accepted!</h1>
              <p style="margin: 10px 0 0 0; opacity: 0.9;">Your service request has been approved</p>
            </div>
            <div style="padding: 30px; background: #f8f9fa;">
              <h2 style="color: #333; margin-bottom: 20px;">Great News!</h2>
              <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <p><strong>${booking.providerDetails.businessName}</strong> has accepted your booking request for <strong>${booking.serviceDetails.name}</strong>.</p>
                <p><strong>Next Steps:</strong></p>
                <ol style="color: #333; padding-left: 20px;">
                  <li>You'll receive an invoice shortly with payment details</li>
                  <li>Once payment is completed, your appointment will be confirmed</li>
                  <li>You'll receive calendar invites and reminders</li>
                  <li>The provider may contact you to confirm details</li>
                </ol>
              </div>
              <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h3 style="color: #333; margin-top: 0;">Booking Details</h3>
                <p><strong>Service:</strong> ${booking.serviceDetails.name}</p>
                <p><strong>Pet:</strong> ${booking.bookingDetails.petName} (${booking.bookingDetails.petType})</p>
                <p><strong>Requested Date:</strong> ${booking.bookingDetails.preferredDate}</p>
                <p><strong>Requested Time:</strong> ${booking.bookingDetails.preferredTime}</p>
                <p><strong>Price:</strong> $${booking.serviceDetails.price}</p>
              </div>
              <div style="text-align: center; margin-top: 30px;">
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard" 
                   style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
                  View Your Bookings
                </a>
              </div>
            </div>
          </div>
        `
      });

      // In-app notification to pet owner
      await adminDb.collection('notifications').add({
        userId: booking.petOwnerId,
        title: 'Booking Accepted! 🎉',
        message: `${booking.providerDetails.businessName} accepted your ${booking.serviceDetails.name} request`,
        type: 'booking_accepted',
        data: { bookingId },
        read: false,
        createdAt: new Date().toISOString()
      });

    } catch (notificationError) {
      console.error('Error sending notifications:', notificationError);
      // Don't fail the booking if notifications fail
    }

    return NextResponse.json({
      success: true,
      message: 'Booking accepted successfully'
    });

  } catch (error: any) {
    console.error('Accept booking error:', error);
    return NextResponse.json({ 
      success: false,
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
