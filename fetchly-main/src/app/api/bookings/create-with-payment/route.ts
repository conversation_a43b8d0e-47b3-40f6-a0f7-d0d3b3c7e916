import { NextRequest, NextResponse } from 'next/server';
import { auth as adminAuth } from '@/lib/firebase/admin-config';
import { collection, addDoc, doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function OPTIONS(request: NextRequest) {
  return new Response(null, { status: 200, headers: corsHeaders });
}

/**
 * Create booking with payment flow
 * POST /api/bookings/create-with-payment
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🎯 Creating booking with payment flow...');

    // Check if Firebase Admin is properly initialized
    if (!adminAuth) {
      console.error('❌ Firebase Admin Auth not initialized');
      return NextResponse.json({
        success: false,
        error: 'Server configuration error - please contact support'
      }, { status: 500 });
    }

    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];

    // Verify the token
    let decodedToken;
    try {
      decodedToken = await adminAuth.verifyIdToken(token);
      console.log('✅ Token verified for user:', decodedToken.uid);
    } catch (error) {
      console.error('❌ Token verification failed:', error);
      return NextResponse.json({
        success: false,
        error: 'Invalid authentication token'
      }, { status: 401 });
    }

    const bookingData = await request.json();
    console.log('📝 Booking data received:', {
      providerId: bookingData.providerId,
      scheduledDate: bookingData.scheduledDate,
      scheduledTime: bookingData.scheduledTime,
      serviceName: bookingData.serviceName
    });

    // Validate required fields
    if (!bookingData.providerId || !bookingData.scheduledDate || !bookingData.scheduledTime) {
      console.error('❌ Missing required booking fields');
      return NextResponse.json({
        success: false,
        error: 'Missing required booking information'
      }, { status: 400 });
    }

    // Get provider information
    let providerDoc;
    try {
      const providerRef = doc(db, 'providers', bookingData.providerId);
      providerDoc = await getDoc(providerRef);
      console.log('📋 Provider lookup result:', providerDoc.exists());
    } catch (error) {
      console.error('❌ Error fetching provider:', error);
      return NextResponse.json({
        success: false,
        error: 'Database error - please try again'
      }, { status: 500 });
    }

    if (!providerDoc.exists()) {
      console.error('❌ Provider not found:', bookingData.providerId);
      return NextResponse.json({
        success: false,
        error: 'Provider not found'
      }, { status: 404 });
    }

    const providerData = providerDoc.data();

    // Create comprehensive booking record
    const booking = {
      // Basic booking info
      providerId: bookingData.providerId,
      providerName: bookingData.providerName || providerData.businessName,
      providerEmail: providerData.email,
      userId: decodedToken.uid,
      userName: bookingData.userName || decodedToken.name,
      userEmail: bookingData.userEmail || decodedToken.email,
      
      // Service details
      serviceId: bookingData.serviceId,
      serviceName: bookingData.serviceName,
      
      // Pet information
      petId: bookingData.petId,
      petName: bookingData.petName,
      
      // Scheduling
      scheduledDate: bookingData.scheduledDate,
      scheduledTime: bookingData.scheduledTime,
      duration: bookingData.duration || 60,
      
      // Payment information
      totalPrice: bookingData.totalPrice || 50,
      paymentRequired: true,
      paymentStatus: 'pending',
      
      // Status tracking
      status: 'pending_provider_approval',
      
      // Additional info
      notes: bookingData.notes || '',
      
      // Timestamps
      createdAt: new Date(),
      updatedAt: new Date(),
      
      // Workflow tracking
      workflow: {
        step: 1,
        description: 'Waiting for provider approval',
        nextAction: 'Provider will review and send invoice'
      }
    };

    // Save booking to Firestore
    let docRef;
    try {
      const bookingsRef = collection(db, 'bookings');
      docRef = await addDoc(bookingsRef, booking);
      console.log('✅ Booking created with ID:', docRef.id);
    } catch (error) {
      console.error('❌ Error saving booking to Firestore:', error);
      return NextResponse.json({
        success: false,
        error: 'Failed to save booking - please try again'
      }, { status: 500 });
    }

    // Check if provider has Stripe Connect set up (reuse existing providerDoc)
    let requiresStripeSetup = false;
    if (providerDoc.exists()) {
      const providerData = providerDoc.data();
      requiresStripeSetup = !providerData.stripeAccountId || !providerData.stripeChargesEnabled;

      if (requiresStripeSetup) {
        console.log('⚠️ Provider needs to complete Stripe Connect setup');
      }
    }

    // Create notification for provider
    const providerNotification = {
      type: 'new_booking',
      title: 'New Booking Request',
      message: `${booking.userName} wants to book ${booking.serviceName} for ${booking.petName}`,
      bookingId: docRef.id,
      userId: booking.providerId,
      read: false,
      createdAt: new Date(),
      data: {
        bookingId: docRef.id,
        customerName: booking.userName,
        serviceName: booking.serviceName,
        petName: booking.petName,
        scheduledDate: booking.scheduledDate,
        scheduledTime: booking.scheduledTime,
        totalPrice: booking.totalPrice
      }
    };

    // Save notification
    try {
      const notificationsRef = collection(db, 'notifications');
      await addDoc(notificationsRef, providerNotification);
      console.log('✅ Provider notification created');
    } catch (notificationError) {
      console.error('⚠️ Failed to create notification:', notificationError);
      // Don't fail the booking if notification fails
    }

    // Create customer notification
    const customerNotification = {
      type: 'booking_created',
      title: 'Booking Request Sent',
      message: `Your booking request has been sent to ${booking.providerName}. They will send you an invoice to complete payment.`,
      bookingId: docRef.id,
      userId: booking.userId,
      read: false,
      createdAt: new Date(),
      data: {
        bookingId: docRef.id,
        providerName: booking.providerName,
        serviceName: booking.serviceName,
        scheduledDate: booking.scheduledDate,
        scheduledTime: booking.scheduledTime
      }
    };

    try {
      const notificationsRef = collection(db, 'notifications');
      await addDoc(notificationsRef, customerNotification);
      console.log('✅ Customer notification created');
    } catch (notificationError) {
      console.error('⚠️ Failed to create customer notification:', notificationError);
    }

    // Send email notifications to admin
    try {
      await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/send-email`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'admin_notification',
          to: '<EMAIL>',
          subject: `📅 New Booking Request - ${booking.serviceName} for ${booking.petName}`,
          data: {
            type: 'booking',
            bookingId: docRef.id,
            customerName: booking.userName,
            customerEmail: booking.userEmail,
            providerName: booking.providerName,
            serviceName: booking.serviceName,
            petName: booking.petName,
            petType: booking.petType,
            scheduledDate: booking.scheduledDate,
            scheduledTime: booking.scheduledTime,
            totalPrice: booking.totalPrice,
            notes: booking.notes,
            requiresStripeSetup,
            timestamp: new Date()
          }
        })
      });
      console.log('✅ Admin notification email sent for booking');
    } catch (emailError) {
      console.error('❌ Failed to send admin notification email:', emailError);
      // Don't fail the booking if email fails
    }

    const response = NextResponse.json({
      success: true,
      bookingId: docRef.id,
      message: 'Booking created successfully. Provider will send invoice for payment.',
      booking: {
        id: docRef.id,
        status: booking.status,
        totalPrice: booking.totalPrice,
        scheduledDate: booking.scheduledDate,
        scheduledTime: booking.scheduledTime,
        providerName: booking.providerName,
        serviceName: booking.serviceName
      },
      nextSteps: [
        'Provider will review your booking request',
        'You will receive an invoice via email',
        'Complete payment to confirm your booking',
        'Enjoy your pet service!'
      ]
    });

    // Add CORS headers
    Object.entries(corsHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;

  } catch (error: any) {
    console.error('❌ Error creating booking with payment:', error);
    const errorResponse = NextResponse.json({
      success: false,
      error: 'Failed to create booking. Please try again.',
      details: error.message
    }, { status: 500 });

    // Add CORS headers
    Object.entries(corsHeaders).forEach(([key, value]) => {
      errorResponse.headers.set(key, value);
    });

    return errorResponse;
  }
}
