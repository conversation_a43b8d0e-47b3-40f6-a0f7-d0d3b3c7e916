import { NextRequest, NextResponse } from 'next/server';
import { auth, adminDb } from '@/lib/firebase/admin-config';
import { sendEmail } from '@/lib/services/email-service';

export async function POST(req: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];

    // Verify the Firebase token
    if (!auth || !adminDb) {
      console.error('Firebase Admin not initialized');
      return NextResponse.json({
        success: false,
        error: 'Server configuration error'
      }, { status: 500 });
    }

    let decodedToken;
    try {
      decodedToken = await auth.verifyIdToken(token);
    } catch (error) {
      console.error('Error verifying token:', error);
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const bookingData = await req.json();

    // Validate required fields
    const requiredFields = ['serviceId', 'providerId', 'petOwnerId', 'serviceDetails', 'providerDetails', 'bookingDetails'];
    for (const field of requiredFields) {
      if (!bookingData[field]) {
        return NextResponse.json({
          success: false,
          error: `Missing required field: ${field}`
        }, { status: 400 });
      }
    }

    // Create booking document
    const bookingId = adminDb.collection('bookings').doc().id;
    const booking = {
      id: bookingId,
      ...bookingData,
      status: 'pending_provider_approval',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    console.log('📝 Creating booking with data:', JSON.stringify(booking, null, 2));

    // Save to Firestore
    await adminDb.collection('bookings').doc(bookingId).set(booking);

    // Send notifications
    try {
      // Email to provider
      await sendEmail({
        to: bookingData.providerDetails.email,
        subject: `New Booking Request - ${bookingData.serviceDetails.name}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
              <h1 style="margin: 0; font-size: 28px;">New Booking Request</h1>
              <p style="margin: 10px 0 0 0; opacity: 0.9;">You have a new service request!</p>
            </div>
            <div style="padding: 30px; background: #f8f9fa;">
              <h2 style="color: #333; margin-bottom: 20px;">Booking Details</h2>
              <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <p><strong>Service:</strong> ${bookingData.serviceDetails.name}</p>
                <p><strong>Price:</strong> $${bookingData.serviceDetails.price}</p>
                <p><strong>Duration:</strong> ${bookingData.serviceDetails.duration} minutes</p>
                <p><strong>Pet Name:</strong> ${bookingData.bookingDetails.petName}</p>
                <p><strong>Pet Type:</strong> ${bookingData.bookingDetails.petType}</p>
                <p><strong>Preferred Date:</strong> ${bookingData.bookingDetails.preferredDate}</p>
                <p><strong>Preferred Time:</strong> ${bookingData.bookingDetails.preferredTime}</p>
                <p><strong>Address:</strong> ${bookingData.bookingDetails.address}</p>
              </div>
              <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h3 style="color: #333; margin-top: 0;">Pet Owner Contact</h3>
                <p><strong>Email:</strong> ${bookingData.bookingDetails.contactEmail}</p>
                <p><strong>Phone:</strong> ${bookingData.bookingDetails.contactPhone}</p>
                ${bookingData.bookingDetails.emergencyContact ? `<p><strong>Emergency Contact:</strong> ${bookingData.bookingDetails.emergencyContact} (${bookingData.bookingDetails.emergencyPhone})</p>` : ''}
              </div>
              ${bookingData.bookingDetails.specialRequests ? `
                <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                  <h3 style="color: #333; margin-top: 0;">Special Requests</h3>
                  <p>${bookingData.bookingDetails.specialRequests}</p>
                </div>
              ` : ''}
              <div style="text-align: center; margin-top: 30px;">
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/provider/dashboard?tab=bookings"
                   style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
                  View & Respond to Booking
                </a>
              </div>
            </div>
          </div>
        `
      });

      // Email to pet owner
      await sendEmail({
        to: bookingData.bookingDetails.contactEmail,
        subject: `Booking Request Sent - ${bookingData.serviceDetails.name}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
              <h1 style="margin: 0; font-size: 28px;">Booking Request Sent!</h1>
              <p style="margin: 10px 0 0 0; opacity: 0.9;">We've sent your request to the provider</p>
            </div>
            <div style="padding: 30px; background: #f8f9fa;">
              <h2 style="color: #333; margin-bottom: 20px;">Your Booking Request</h2>
              <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <p><strong>Provider:</strong> ${bookingData.providerDetails.businessName}</p>
                <p><strong>Service:</strong> ${bookingData.serviceDetails.name}</p>
                <p><strong>Pet:</strong> ${bookingData.bookingDetails.petName} (${bookingData.bookingDetails.petType})</p>
                <p><strong>Requested Date:</strong> ${bookingData.bookingDetails.preferredDate}</p>
                <p><strong>Requested Time:</strong> ${bookingData.bookingDetails.preferredTime}</p>
                <p><strong>Booking ID:</strong> ${bookingId}</p>
              </div>
              <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h3 style="color: #1976d2; margin-top: 0;">What happens next?</h3>
                <ol style="color: #333; padding-left: 20px;">
                  <li>The provider will review your request</li>
                  <li>They'll send you an invoice with final details</li>
                  <li>Once you pay, your appointment is confirmed</li>
                  <li>You'll receive calendar invites and reminders</li>
                </ol>
              </div>
              <div style="text-align: center; margin-top: 30px;">
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard"
                   style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
                  View Your Bookings
                </a>
              </div>
            </div>
          </div>
        `
      });

      // In-app notification to provider
      await adminDb.collection('notifications').add({
        userId: bookingData.providerId,
        title: 'New Booking Request',
        message: `${bookingData.bookingDetails.petName} needs ${bookingData.serviceDetails.name}`,
        type: 'booking_request',
        data: { bookingId },
        read: false,
        createdAt: new Date().toISOString()
      });

      // In-app notification to pet owner
      await adminDb.collection('notifications').add({
        userId: bookingData.petOwnerId,
        title: 'Booking Request Sent',
        message: `Your request for ${bookingData.serviceDetails.name} has been sent to ${bookingData.providerDetails.businessName}`,
        type: 'booking_confirmation',
        data: { bookingId },
        read: false,
        createdAt: new Date().toISOString()
      });

    } catch (notificationError) {
      console.error('Error sending notifications:', notificationError);
      // Don't fail the booking if notifications fail
    }

    return NextResponse.json({
      success: true,
      bookingId: bookingId,
      message: 'Booking request sent successfully'
    });

  } catch (error: any) {
    console.error('Booking creation error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// GET endpoint to fetch bookings
export async function GET(req: NextRequest) {
  try {
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];

    if (!auth || !adminDb) {
      return NextResponse.json({
        success: false,
        error: 'Server configuration error'
      }, { status: 500 });
    }

    let decodedToken;
    try {
      decodedToken = await auth.verifyIdToken(token);
    } catch (error) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const userType = searchParams.get('userType'); // 'provider' or 'petOwner'
    const userId = decodedToken.uid;

    let bookingsQuery;
    if (userType === 'provider') {
      bookingsQuery = adminDb.collection('bookings').where('providerId', '==', userId);
    } else {
      bookingsQuery = adminDb.collection('bookings').where('petOwnerId', '==', userId);
    }

    const bookingsSnapshot = await bookingsQuery.orderBy('createdAt', 'desc').get();
    const bookings = bookingsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    return NextResponse.json({
      success: true,
      bookings
    });

  } catch (error: any) {
    console.error('Get bookings error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
