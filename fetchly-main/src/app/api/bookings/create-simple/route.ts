import { NextRequest, NextResponse } from 'next/server';
import { collection, addDoc, doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { adminDb } from '@/lib/firebase/admin-config';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function OPTIONS(request: NextRequest) {
  return new Response(null, { status: 200, headers: corsHeaders });
}

/**
 * Simple booking creation without Firebase Admin
 * POST /api/bookings/create-simple
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🎯 Creating booking with simple flow...');
    
    const bookingData = await request.json();
    console.log('📝 Booking data received:', {
      providerId: bookingData.providerId,
      scheduledDate: bookingData.scheduledDate,
      scheduledTime: bookingData.scheduledTime,
      serviceName: bookingData.serviceName,
      userId: bookingData.userId
    });
    
    // Validate required fields
    if (!bookingData.providerId || !bookingData.scheduledDate || !bookingData.scheduledTime) {
      console.error('❌ Missing required booking fields');
      return NextResponse.json({
        success: false,
        error: 'Missing required booking information (providerId, scheduledDate, scheduledTime)'
      }, { status: 400 });
    }

    // If no userId provided, generate a temporary one
    if (!bookingData.userId) {
      bookingData.userId = 'temp-user-' + Date.now();
      console.log('⚠️ No userId provided, using temporary ID:', bookingData.userId);
    }

    // CRITICAL: Resolve correct provider ID from users collection
    let resolvedProviderId = bookingData.providerId;
    let providerData = null;

    try {
      console.log('🔍 Resolving provider ID:', bookingData.providerId);

      // First, check if this is a valid user ID in the users collection
      const userRef = doc(db, 'users', bookingData.providerId);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists() && userDoc.data().role === 'provider') {
        // This is already the correct Firebase Auth user ID
        resolvedProviderId = bookingData.providerId;
        console.log('✅ Provider ID is correct Firebase Auth ID:', resolvedProviderId);
      } else {
        // This might be a provider document ID, try to find the correct user ID
        console.log('🔄 Provider ID not found in users, searching providers collection...');

        const providersRef = collection(db, 'providers');
        const providersSnapshot = await getDocs(providersRef);

        let foundCorrectId = false;
        for (const providerDoc of providersSnapshot.docs) {
          const data = providerDoc.data();

          // Check if this provider document matches the requested ID
          if (providerDoc.id === bookingData.providerId) {
            // Found the provider document, now get the correct user ID
            if (data.userId) {
              resolvedProviderId = data.userId;
              console.log('✅ Resolved provider ID from document:', bookingData.providerId, '→', resolvedProviderId);
              foundCorrectId = true;
              break;
            }
          }
        }

        if (!foundCorrectId) {
          console.warn('⚠️ Could not resolve provider ID, using original:', bookingData.providerId);
        }
      }

      // Now get provider information using the resolved ID
      const providerRef = doc(db, 'providers', resolvedProviderId);
      const providerDoc = await getDoc(providerRef);
      if (providerDoc.exists()) {
        providerData = providerDoc.data();
        console.log('📋 Provider found:', providerData.businessName);
      } else {
        // Try to get provider info from the original ID if resolved ID doesn't work
        const fallbackRef = doc(db, 'providers', bookingData.providerId);
        const fallbackDoc = await getDoc(fallbackRef);
        if (fallbackDoc.exists()) {
          providerData = fallbackDoc.data();
          console.log('📋 Provider found via fallback:', providerData.businessName);
        } else {
          console.warn('⚠️ Provider not found in either location');
        }
      }
    } catch (error) {
      console.warn('⚠️ Error resolving provider ID, using original:', error);
      resolvedProviderId = bookingData.providerId;
    }

    // Create comprehensive booking record
    const booking = {
      // Basic booking info - USE RESOLVED PROVIDER ID
      providerId: resolvedProviderId,
      originalProviderId: bookingData.providerId, // Keep track of original for debugging
      providerName: bookingData.providerName || providerData?.businessName || 'Unknown Provider',
      providerEmail: providerData?.email || '<EMAIL>',
      userId: bookingData.userId,
      userName: bookingData.userName || 'Pet Owner',
      userEmail: bookingData.userEmail || '<EMAIL>',
      
      // Service details
      serviceId: bookingData.serviceId || 'default-service',
      serviceName: bookingData.serviceName || 'Pet Service',
      
      // Pet information
      petId: bookingData.petId || 'default-pet',
      petName: bookingData.petName || 'Pet',
      
      // Scheduling
      scheduledDate: bookingData.scheduledDate,
      scheduledTime: bookingData.scheduledTime,
      duration: bookingData.duration || 60,
      
      // Payment information
      totalPrice: bookingData.totalPrice || 50,
      paymentRequired: true,
      paymentStatus: 'pending',
      
      // Status tracking
      status: 'pending_provider_approval',
      
      // Additional info
      notes: bookingData.notes || '',
      
      // Timestamps
      createdAt: new Date(),
      updatedAt: new Date(),
      
      // Workflow tracking
      workflow: {
        step: 1,
        description: 'Waiting for provider approval',
        nextAction: 'Provider will review and send invoice'
      }
    };

    // Save booking to Firestore using Admin SDK (server-side)
    let docRef;
    let bookingId;
    try {
      // Use Admin SDK for server-side operations (bypasses security rules)
      if (adminDb) {
        console.log('🔧 Using Firebase Admin SDK...');
        docRef = await adminDb.collection('bookings').add(booking);
        bookingId = docRef.id;
        console.log('✅ Booking created with Admin SDK, ID:', bookingId);
      } else {
        console.log('❌ Firebase Admin not available - this is required for server-side booking creation');
        throw new Error('Firebase Admin SDK not initialized. Please check environment variables.');
      }
    } catch (error: any) {
      console.error('❌ Error saving booking to Firestore:', error);
      console.error('❌ Error details:', {
        code: error.code,
        message: error.message,
        stack: error.stack
      });
      return NextResponse.json({
        success: false,
        error: 'Failed to save booking - please try again',
        details: error.message,
        errorCode: error.code
      }, { status: 500 });
    }

    // Send notification to provider
    try {
      const notificationData = {
        userId: bookingData.providerId,
        title: '🎉 New Booking Request!',
        message: `${bookingData.userName || 'A customer'} wants to book ${bookingData.serviceName} for ${bookingData.petName}`,
        type: 'booking_request',
        data: {
          bookingId: bookingId,
          customerName: bookingData.userName,
          serviceName: bookingData.serviceName,
          petName: bookingData.petName,
          scheduledDate: bookingData.scheduledDate,
          scheduledTime: bookingData.scheduledTime,
          totalPrice: bookingData.totalPrice
        },
        read: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      if (adminDb) {
        await adminDb.collection('notifications').add(notificationData);
      } else {
        const notificationsRef = collection(db, 'notifications');
        await addDoc(notificationsRef, notificationData);
      }
      console.log('✅ Provider notification sent');
    } catch (error) {
      console.error('❌ Error sending provider notification:', error);
      // Don't fail the booking if notification fails
    }

    const response = NextResponse.json({
      success: true,
      bookingId: bookingId,
      message: 'Booking created successfully. Provider will send invoice for payment.',
      booking: {
        id: bookingId,
        status: booking.status,
        totalPrice: booking.totalPrice,
        scheduledDate: booking.scheduledDate,
        scheduledTime: booking.scheduledTime,
        providerName: booking.providerName,
        serviceName: booking.serviceName
      },
      nextSteps: [
        'Provider will review your booking request',
        'You will receive an invoice via email',
        'Complete payment to confirm your booking',
        'Enjoy your pet service!'
      ]
    });

    // Add CORS headers
    Object.entries(corsHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;

  } catch (error: any) {
    console.error('❌ Error creating booking:', error);
    const errorResponse = NextResponse.json({
      success: false,
      error: 'Failed to create booking. Please try again.',
      details: error.message
    }, { status: 500 });

    // Add CORS headers
    Object.entries(corsHeaders).forEach(([key, value]) => {
      errorResponse.headers.set(key, value);
    });

    return errorResponse;
  }
}
