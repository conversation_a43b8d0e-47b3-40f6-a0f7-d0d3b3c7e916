import { NextRequest, NextResponse } from 'next/server';
import { withMiddleware } from '@/lib/middleware';
import { BookingService } from '@/lib/services';
import { notifyPaymentReceived } from '@/lib/notifications/notification-helpers';

// Customer pays the invoice
async function payInvoiceHandler(request: NextRequest) {
  try {
    const user = (request as any).user;
    const { bookingId, paymentMethodId } = await request.json();

    // Validate input
    if (!bookingId || !paymentMethodId) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: bookingId, paymentMethodId'
      }, { status: 400 });
    }

    // Process payment for the invoice
    const booking = await BookingService.payInvoice(
      bookingId,
      user.id, // userId
      paymentMethodId
    );

    // Send notification to provider about payment received
    await notifyPaymentReceived(
      booking.providerId,
      booking.finalAmount || booking.totalPrice,
      user.name || 'Customer',
      booking.serviceName,
      `booking_${bookingId}`
    );

    return NextResponse.json({
      success: true,
      booking,
      message: 'Payment processed successfully'
    });
  } catch (error: any) {
    console.error('Pay invoice error:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to process payment'
    }, { status: 500 });
  }
}

export const POST = withMiddleware(payInvoiceHandler);
