'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import {
  FileText,
  Calendar,
  DollarSign,
  User,
  Mail,
  CheckCircle,
  AlertCircle,
  CreditCard
} from 'lucide-react';
import toast from 'react-hot-toast';

interface Invoice {
  id: string;
  providerId: string;
  providerName: string;
  clientName: string;
  clientEmail: string;
  amount: number;
  description: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  createdAt: string;
  dueDate: string;
  paidAt?: string;
  invoiceNumber: string;
}

export default function InvoicePage() {
  const params = useParams();
  const invoiceId = params.id as string;
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPaymentLoading, setIsPaymentLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (invoiceId) {
      loadInvoice();
    }
  }, [invoiceId]);

  const loadInvoice = async () => {
    try {
      const response = await fetch(`/api/invoices/pay?id=${invoiceId}`);
      const data = await response.json();

      if (data.success) {
        setInvoice(data.invoice);
      } else {
        setError(data.error || 'Invoice not found');
      }
    } catch (error) {
      console.error('Error loading invoice:', error);
      setError('Failed to load invoice');
    } finally {
      setIsLoading(false);
    }
  };

  const payInvoice = async () => {
    try {
      setIsPaymentLoading(true);
      
      const response = await fetch('/api/invoices/pay', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invoiceId,
          paymentMethod: 'manual'
        }),
      });

      const data = await response.json();

      if (data.success) {
        setInvoice(prev => prev ? { ...prev, status: 'paid', paidAt: new Date().toISOString() } : null);
        toast.success('Payment processed successfully!');
      } else {
        toast.error(data.error || 'Payment failed');
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      toast.error('Payment failed');
    } finally {
      setIsPaymentLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !invoice) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl p-8 shadow-lg max-w-md w-full mx-4">
          <div className="text-center">
            <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-800 mb-2">Invoice Not Found</h1>
            <p className="text-gray-600">{error || 'The invoice you are looking for does not exist.'}</p>
          </div>
        </div>
      </div>
    );
  }

  const isOverdue = new Date(invoice.dueDate) < new Date() && invoice.status !== 'paid';

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Invoice</h1>
          <p className="text-gray-600">#{invoice.invoiceNumber}</p>
        </div>

        {/* Invoice Card */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          {/* Status Banner */}
          <div className={`p-4 text-center text-white font-semibold ${
            invoice.status === 'paid' 
              ? 'bg-green-500'
              : isOverdue
              ? 'bg-red-500'
              : invoice.status === 'sent'
              ? 'bg-blue-500'
              : 'bg-gray-500'
          }`}>
            {invoice.status === 'paid' && (
              <div className="flex items-center justify-center gap-2">
                <CheckCircle className="w-5 h-5" />
                <span>PAID</span>
              </div>
            )}
            {invoice.status === 'sent' && !isOverdue && <span>PENDING PAYMENT</span>}
            {isOverdue && <span>OVERDUE</span>}
            {invoice.status === 'draft' && <span>DRAFT</span>}
          </div>

          {/* Invoice Details */}
          <div className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              {/* From */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">From</h3>
                <div className="space-y-1">
                  <p className="font-semibold text-gray-800">{invoice.providerName}</p>
                  <p className="text-gray-600">Pet Service Provider</p>
                </div>
              </div>

              {/* To */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">To</h3>
                <div className="space-y-1">
                  <p className="font-semibold text-gray-800">{invoice.clientName}</p>
                  <p className="text-gray-600 flex items-center gap-2">
                    <Mail className="w-4 h-4" />
                    {invoice.clientEmail}
                  </p>
                </div>
              </div>
            </div>

            {/* Invoice Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 p-6 bg-gray-50 rounded-xl">
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Invoice Date</p>
                <p className="text-gray-800 flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  {new Date(invoice.createdAt).toLocaleDateString()}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Due Date</p>
                <p className={`flex items-center gap-2 ${isOverdue ? 'text-red-600' : 'text-gray-800'}`}>
                  <Calendar className="w-4 h-4" />
                  {new Date(invoice.dueDate).toLocaleDateString()}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Amount</p>
                <p className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  {invoice.amount.toFixed(2)}
                </p>
              </div>
            </div>

            {/* Description */}
            <div className="mb-8">
              <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Description</h3>
              <p className="text-gray-800 bg-gray-50 p-4 rounded-lg">{invoice.description}</p>
            </div>

            {/* Payment Section */}
            {invoice.status === 'paid' ? (
              <div className="bg-green-50 border border-green-200 rounded-xl p-6 text-center">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-green-800 mb-1">Payment Received</h3>
                <p className="text-green-600">
                  Paid on {new Date(invoice.paidAt!).toLocaleDateString()}
                </p>
              </div>
            ) : (
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">Pay This Invoice</h3>
                <div className="space-y-4">
                  <div className="text-center">
                    <p className="text-3xl font-bold text-gray-800 mb-2">${invoice.amount.toFixed(2)}</p>
                    <p className="text-gray-600">Amount Due</p>
                  </div>
                  
                  <button
                    onClick={payInvoice}
                    disabled={isPaymentLoading}
                    className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-4 rounded-xl font-semibold hover:from-green-700 hover:to-blue-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  >
                    <CreditCard className="w-5 h-5" />
                    <span>{isPaymentLoading ? 'Processing...' : 'Pay Now'}</span>
                  </button>
                  
                  <p className="text-xs text-gray-500 text-center">
                    This is a demo payment. In production, this would integrate with Stripe, PayPal, etc.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-gray-500 text-sm">
            Powered by Fetchly • Secure Pet Service Payments
          </p>
        </div>
      </div>
    </div>
  );
}
