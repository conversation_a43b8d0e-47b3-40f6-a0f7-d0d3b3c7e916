'use client';

import { useState } from 'react';
import Link from 'next/link';
import { 
  Search, 
  MapPin, 
  Star, 
  Filter, 
  Heart, 
  Phone, 
  Globe, 
  Calendar,
  DollarSign,
  Clock,
  Award,
  Verified,
  ChevronDown,
  SlidersHorizontal,
  Grid3X3,
  List
} from 'lucide-react';

interface Provider {
  id: string;
  name: string;
  type: string;
  rating: number;
  reviewCount: number;
  location: string;
  distance: string;
  price: string;
  availability: string;
  services: string[];
  verified: boolean;
  featured: boolean;
  image: string;
  description: string;
  phone: string;
  website: string;
  responseTime: string;
}

const mockProviders: Provider[] = [
  {
    id: '1',
    name: 'Happy Paws Grooming',
    type: 'Grooming',
    rating: 4.9,
    reviewCount: 127,
    location: 'San Francisco, CA',
    distance: '0.8 miles',
    price: '$50-80',
    availability: 'Available today',
    services: ['Full Grooming', 'Nail Trimming', 'Teeth Cleaning', 'Flea Treatment'],
    verified: true,
    featured: true,
    image: '/providers/happy-paws.jpg',
    description: 'Professional pet grooming with 10+ years of experience. We specialize in all breeds and provide a stress-free environment.',
    phone: '+****************',
    website: 'https://happypaws.com',
    responseTime: '< 1 hour'
  },
  {
    id: '2',
    name: 'Dr. Michael Chen - Veterinary Care',
    type: 'Veterinary',
    rating: 4.8,
    reviewCount: 89,
    location: 'Los Angeles, CA',
    distance: '1.2 miles',
    price: '$80-150',
    availability: 'Next available: Tomorrow',
    services: ['Health Checkups', 'Vaccinations', 'Surgery', 'Emergency Care'],
    verified: true,
    featured: false,
    image: '/providers/dr-chen.jpg',
    description: 'Board-certified veterinarian with expertise in small animal medicine and surgery.',
    phone: '+****************',
    website: 'https://drchen-vet.com',
    responseTime: '< 2 hours'
  },
  {
    id: '3',
    name: 'Cozy Pet Lodge',
    type: 'Pet Hotel',
    rating: 4.7,
    reviewCount: 156,
    location: 'New York, NY',
    distance: '2.1 miles',
    price: '$40-60/night',
    availability: 'Available this weekend',
    services: ['Overnight Boarding', 'Daycare', 'Play Time', 'Feeding'],
    verified: true,
    featured: true,
    image: '/providers/cozy-lodge.jpg',
    description: 'Luxury pet boarding facility with spacious rooms and 24/7 supervision.',
    phone: '+****************',
    website: 'https://cozypetlodge.com',
    responseTime: '< 30 minutes'
  },
  {
    id: '4',
    name: 'Pawsome Dog Walking',
    type: 'Dog Walking',
    rating: 4.6,
    reviewCount: 203,
    location: 'Chicago, IL',
    distance: '0.5 miles',
    price: '$25-35',
    availability: 'Available now',
    services: ['Daily Walks', 'Pet Sitting', 'Feeding', 'Playtime'],
    verified: false,
    featured: false,
    image: '/providers/pawsome-walking.jpg',
    description: 'Reliable dog walking service with GPS tracking and photo updates.',
    phone: '+****************',
    website: 'https://pawsomewalking.com',
    responseTime: '< 15 minutes'
  },
  {
    id: '5',
    name: 'Elite Pet Training Academy',
    type: 'Training',
    rating: 4.9,
    reviewCount: 78,
    location: 'Miami, FL',
    distance: '1.8 miles',
    price: '$60-120',
    availability: 'Available next week',
    services: ['Basic Training', 'Advanced Training', 'Behavior Modification', 'Puppy Classes'],
    verified: true,
    featured: true,
    image: '/providers/elite-training.jpg',
    description: 'Professional dog training with certified trainers and proven methods.',
    phone: '+****************',
    website: 'https://elitepettraining.com',
    responseTime: '< 1 hour'
  },
  {
    id: '6',
    name: 'Furry Friends Daycare',
    type: 'Pet Sitting',
    rating: 4.5,
    reviewCount: 134,
    location: 'Seattle, WA',
    distance: '3.2 miles',
    price: '$30-50/day',
    availability: 'Available today',
    services: ['Pet Sitting', 'Daycare', 'Overnight Care', 'Exercise'],
    verified: true,
    featured: false,
    image: '/providers/furry-friends.jpg',
    description: 'Loving pet care in a home environment with experienced caregivers.',
    phone: '+****************',
    website: 'https://furryfriendsdaycare.com',
    responseTime: '< 45 minutes'
  }
];

const serviceTypes = ['All Services', 'Grooming', 'Veterinary', 'Pet Hotel', 'Dog Walking', 'Training', 'Pet Sitting'];
const sortOptions = ['Recommended', 'Highest Rated', 'Nearest', 'Lowest Price', 'Most Reviews'];

export default function ProvidersPage() {
  const [providers, setProviders] = useState<Provider[]>(mockProviders);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedService, setSelectedService] = useState('All Services');
  const [sortBy, setSortBy] = useState('Recommended');
  const [showFilters, setShowFilters] = useState(false);
  const [favorites, setFavorites] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filters, setFilters] = useState({
    minRating: 0,
    maxDistance: 50,
    priceRange: 'any',
    availability: 'any',
    verified: false
  });

  const toggleFavorite = (providerId: string) => {
    setFavorites(prev => 
      prev.includes(providerId) 
        ? prev.filter(id => id !== providerId)
        : [...prev, providerId]
    );
  };

  const filteredProviders = providers.filter(provider => {
    const matchesSearch = provider.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         provider.services.some(service => service.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesService = selectedService === 'All Services' || provider.type === selectedService;
    const matchesRating = provider.rating >= filters.minRating;
    const matchesVerified = !filters.verified || provider.verified;
    
    return matchesSearch && matchesService && matchesRating && matchesVerified;
  });

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-gray-50 via-primary-50 to-accent-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 rounded-full bg-primary-400 animate-float"></div>
        <div className="absolute top-32 right-20 w-16 h-16 rounded-full bg-accent-400 animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 rounded-full bg-secondary-400 animate-float" style={{animationDelay: '4s'}}></div>
      </div>

      <div className="container mx-auto px-4 py-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Service Providers <span className="bg-gradient-to-r from-primary-500 to-accent-500 bg-clip-text text-transparent">Directory</span>
          </h1>
          <p className="text-gray-700 text-lg">Browse all verified pet care professionals with active services in your area</p>
        </div>

        {/* Search and Filters */}
        <div className="glass-card rounded-2xl p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
              <input
                type="text"
                placeholder="Search for services, providers, or locations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
              />
            </div>
            
            <div className="flex gap-4">
              <select
                value={selectedService}
                onChange={(e) => setSelectedService(e.target.value)}
                className="px-4 py-4 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
              >
                {serviceTypes.map(service => (
                  <option key={service} value={service}>{service}</option>
                ))}
              </select>
              
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="btn-outline flex items-center gap-2"
              >
                <SlidersHorizontal className="w-5 h-5" />
                Filters
              </button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="pt-6 border-t border-white/30">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Min Rating</label>
                  <select
                    value={filters.minRating}
                    onChange={(e) => setFilters({...filters, minRating: Number(e.target.value)})}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value={0}>Any Rating</option>
                    <option value={4}>4+ Stars</option>
                    <option value={4.5}>4.5+ Stars</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Max Distance</label>
                  <select
                    value={filters.maxDistance}
                    onChange={(e) => setFilters({...filters, maxDistance: Number(e.target.value)})}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value={50}>Any Distance</option>
                    <option value={1}>Within 1 mile</option>
                    <option value={5}>Within 5 miles</option>
                    <option value={10}>Within 10 miles</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Availability</label>
                  <select
                    value={filters.availability}
                    onChange={(e) => setFilters({...filters, availability: e.target.value})}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="any">Any Time</option>
                    <option value="today">Available Today</option>
                    <option value="week">This Week</option>
                  </select>
                </div>
                
                <div className="flex items-end">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={filters.verified}
                      onChange={(e) => setFilters({...filters, verified: e.target.checked})}
                      className="w-4 h-4 text-primary-500 border-2 border-white/30 rounded focus:ring-primary-500"
                    />
                    <span className="text-sm text-cool-700">Verified Only</span>
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Results Header */}
        <div className="flex items-center justify-between mb-6">
          <p className="text-cool-700">
            <span className="font-bold">{filteredProviders.length}</span> providers found
          </p>
          
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-cool-700">Sort by:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                {sortOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center gap-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  viewMode === 'grid' ? 'bg-primary-100 text-primary-600' : 'bg-white/50 text-cool-600 hover:bg-white/80'
                }`}
              >
                <Grid3X3 className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  viewMode === 'list' ? 'bg-primary-100 text-primary-600' : 'bg-white/50 text-cool-600 hover:bg-white/80'
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Provider Cards */}
        <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 lg:grid-cols-2' : 'grid-cols-1'}`}>
          {filteredProviders.map((provider) => (
            <div key={provider.id} className="glass-card rounded-2xl p-6 hover:shadow-xl transition-all duration-300">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center">
                    <span className="text-white font-bold text-lg">
                      {provider.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <Link href={`/provider/${provider.id}`}>
                        <h3 className="text-xl font-bold text-cool-800 hover:text-primary-600 transition-colors duration-200 cursor-pointer">
                          {provider.name}
                        </h3>
                      </Link>
                      {provider.verified && (
                        <Verified className="w-5 h-5 text-blue-500" />
                      )}
                      {provider.featured && (
                        <Award className="w-5 h-5 text-yellow-500" />
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-cool-600">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-yellow-500" />
                        <span className="font-medium">{provider.rating}</span>
                        <span>({provider.reviewCount} reviews)</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        <span>{provider.distance}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={() => toggleFavorite(provider.id)}
                  className={`p-2 rounded-full transition-colors duration-200 ${
                    favorites.includes(provider.id)
                      ? 'bg-red-100 text-red-500'
                      : 'bg-gray-100 text-gray-500 hover:bg-red-100 hover:text-red-500'
                  }`}
                >
                  <Heart className={`w-5 h-5 ${favorites.includes(provider.id) ? 'fill-current' : ''}`} />
                </button>
              </div>

              {/* Description */}
              <p className="text-cool-700 mb-4">{provider.description}</p>

              {/* Services */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {provider.services.slice(0, 3).map((service, index) => (
                    <span key={index} className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm">
                      {service}
                    </span>
                  ))}
                  {provider.services.length > 3 && (
                    <span className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                      +{provider.services.length - 3} more
                    </span>
                  )}
                </div>
              </div>

              {/* Details */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-warm-500" />
                  <span className="text-cool-700">{provider.price}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-secondary-500" />
                  <span className="text-cool-700">{provider.availability}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-accent-500" />
                  <span className="text-cool-700">Responds in {provider.responseTime}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-primary-500" />
                  <span className="text-cool-700">{provider.location}</span>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-3">
                <Link href={`/provider/${provider.id}`} className="flex-1">
                  <button className="w-full btn-primary">
                    View Profile
                  </button>
                </Link>
                <button className="btn-outline">
                  <Calendar className="w-4 h-4 mr-2" />
                  Book
                </button>
                <button className="btn-outline">
                  <Phone className="w-4 h-4 mr-2" />
                  Call
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredProviders.length === 0 && (
          <div className="glass-card rounded-2xl p-12 text-center">
            <Search className="w-16 h-16 text-cool-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-cool-800 mb-2">No providers found</h3>
            <p className="text-cool-600 mb-6">Try adjusting your search criteria or filters.</p>
            <button 
              onClick={() => {
                setSearchQuery('');
                setSelectedService('All Services');
                setFilters({
                  minRating: 0,
                  maxDistance: 50,
                  priceRange: 'any',
                  availability: 'any',
                  verified: false
                });
              }}
              className="btn-primary"
            >
              Clear All Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
