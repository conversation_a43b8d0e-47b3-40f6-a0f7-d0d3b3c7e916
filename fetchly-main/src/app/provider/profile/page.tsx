'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useProvider } from '@/contexts/ProviderContext';
import { Provider, Service } from '@/lib/firebase/providers';
import { ShoppingCart } from 'lucide-react';
import {
  MapPin, Phone, Mail, Globe, Award, Building, Calendar, Users, Star,
  Edit, Camera, Save, X, MessageCircle, UserPlus, UserCheck, Briefcase,
  Clock, CheckCircle, PawPrint, Heart, Share2, MoreHorizontal, Package,
  Upload, RotateCcw, Maximize2, Plus, Home, CreditCard, Shield, Search, Move, ZoomIn
} from 'lucide-react';
import { uploadImage, generateStoragePath, validateImageFile, compressImage } from '@/lib/storage';
import { collection, query, where, getDocs, addDoc, doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { getProviderByUserId, getProviderServices } from '@/lib/firebase/providers';
import toast from 'react-hot-toast';
import PostCard from '@/components/PostCard';
import BookingRequestModal from '@/components/booking/BookingRequestModal';

export default function ProviderProfilePage() {
  const { user } = useAuth();
  const { provider, services, bookings, isLoading, error, updateProviderProfile, refreshProviderData } = useProvider();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const providerId = searchParams.get('id');
  const isOwnProfile = !providerId || (provider && provider.id === providerId);
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState({ profile: false, banner: false });
  // We'll determine this based on the URL and user state
  const [viewingOwnProfile, setViewingOwnProfile] = useState(true);
  const [viewedProvider, setViewedProvider] = useState<Provider | null>(null);
  const [viewedProviderServices, setViewedProviderServices] = useState<Service[]>([]);
  const [providerPosts, setProviderPosts] = useState<any[]>([]);
  const [isFollowing, setIsFollowing] = useState(false);
  const [isFriend, setIsFriend] = useState(false);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [selectedService, setSelectedService] = useState<Service | null>(null);

  // Image upload states
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showBannerModal, setShowBannerModal] = useState(false);
  const [bannerFit, setBannerFit] = useState<'cover' | 'contain' | 'fill'>('cover');
  const [bannerPosition, setBannerPosition] = useState({ x: 50, y: 50 });
  const [isDragging, setIsDragging] = useState(false);

  // Profile data state with Fetchly defaults
  const [profileData, setProfileData] = useState<{
    businessName: string;
    ownerName: string;
    description: string;
    profilePhoto: string;
    bannerPhoto: string;
    email: string;
    phone: string;
    website: string;
    serviceType: string;
    experience: string;
    specialties: string[];
    address: string;
    city: string;
    state: string;
    zipCode: string;
    hasStore?: boolean;
    businessHours: {
      monday: { open: string; close: string; closed: boolean };
      tuesday: { open: string; close: string; closed: boolean };
      wednesday: { open: string; close: string; closed: boolean };
      thursday: { open: string; close: string; closed: boolean };
      friday: { open: string; close: string; closed: boolean };
      saturday: { open: string; close: string; closed: boolean };
      sunday: { open: string; close: string; closed: boolean };
    };
    status: 'pending' | 'approved' | 'rejected' | 'suspended';
  }>({
    businessName: '',
    ownerName: '',
    description: '',
    profilePhoto: '/favicon.png', // Fetchly favicon as default
    bannerPhoto: '/fetchlylogo.png', // Fetchly logo as default banner
    email: '',
    phone: '',
    website: '',
    serviceType: '',
    experience: '',
    specialties: [] as string[],
    address: '',
    city: '',
    state: '',
    zipCode: '',
    hasStore: false,
    businessHours: {
      monday: { open: '09:00', close: '17:00', closed: false },
      tuesday: { open: '09:00', close: '17:00', closed: false },
      wednesday: { open: '09:00', close: '17:00', closed: false },
      thursday: { open: '09:00', close: '17:00', closed: false },
      friday: { open: '09:00', close: '17:00', closed: false },
      saturday: { open: '10:00', close: '16:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: true }
    },
    status: 'pending' as const
  });

  // Determine which provider to display
  const currentProvider = providerId && providerId !== user?.id ? viewedProvider : provider;
  // Determine which services to display
  const currentServices = viewingOwnProfile ? services : viewedProviderServices;

  // Load provider data and posts
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        if (providerId && providerId !== user?.id) {
          // Viewing someone else's profile - providerId is actually the userId
          setViewingOwnProfile(false);
          console.log('🔍 Loading provider profile for userId:', providerId);

          try {
            // Get provider by userId (since the URL contains the user ID, not provider document ID)
            console.log('🔍 Attempting to load provider with userId:', providerId);
            const providerData = await getProviderByUserId(providerId);
            console.log('📋 Provider data loaded:', providerData);

            if (providerData) {
              setViewedProvider(providerData);
              console.log('✅ Successfully set viewed provider:', providerData.businessName);

              // Also load their services
              try {
                console.log('🛠️ Loading services for provider ID:', providerData.id);
                const providerServices = await getProviderServices(providerData.id!);
                console.log('🛠️ Provider services loaded:', providerServices);
                setViewedProviderServices(providerServices);
              } catch (servicesError) {
                console.error('⚠️ Error loading provider services:', servicesError);
                // Continue without services - not critical
                setViewedProviderServices([]);
                toast.error('Could not load provider services');
              }

            setProfileData({
              businessName: providerData.businessName || '',
              ownerName: providerData.ownerName || '',
              description: providerData.description || '',
              profilePhoto: providerData.profilePhoto || '/favicon.png',
              bannerPhoto: providerData.bannerPhoto || '/fetchlylogo.png',
              email: providerData.email || '',
              phone: providerData.phone || '',
              website: providerData.website || '',
              serviceType: providerData.serviceType || '',
              experience: providerData.experience || '',
              specialties: providerData.specialties || [],
              address: providerData.address || '',
              city: providerData.city || '',
              state: providerData.state || '',
              zipCode: providerData.zipCode || '',
              businessHours: (providerData.businessHours as any) || profileData.businessHours,
              status: providerData.status || 'pending'
            });
            } else {
              console.log('❌ No provider data found for userId:', providerId);
              console.log('🔍 This could mean:');
              console.log('  1. The user is not a provider');
              console.log('  2. The provider profile hasn\'t been created yet');
              console.log('  3. The userId is incorrect');
              toast.error('Provider profile not found. This user may not be a provider or hasn\'t set up their profile yet.');
            }
          } catch (providerError) {
            console.error('❌ Error loading provider data:', providerError);
            console.error('❌ Full error details:', providerError);
            toast.error(`Failed to load provider profile: ${(providerError as any)?.message || 'Unknown error'}`);
          }
        } else {
          // Own profile - load data or show defaults for setup
          setViewingOwnProfile(true);

          console.log('Loading own profile. Provider data:', provider);
          console.log('User data:', user);

          // Always try to fetch fresh data from Firebase first
          let currentProviderData = provider;
          if (user?.id) {
            try {
              console.log('🔄 Fetching fresh provider data from Firebase...');
              const freshProviderData = await getProviderByUserId(user.id);
              if (freshProviderData) {
                currentProviderData = freshProviderData;
                console.log('✅ Fresh provider data loaded:', {
                  id: freshProviderData.id,
                  businessName: freshProviderData.businessName,
                  profilePhoto: freshProviderData.profilePhoto,
                  bannerPhoto: freshProviderData.bannerPhoto
                });
              } else {
                console.log('⚠️ No provider data found in Firebase');
              }
            } catch (error) {
              console.error('❌ Error fetching fresh provider data:', error);
              // Continue with context data if available
            }
          }

          if (currentProviderData) {
            console.log('Setting profile data from provider:', {
              businessName: currentProviderData.businessName,
              description: currentProviderData.description,
              profilePhoto: currentProviderData.profilePhoto,
              bannerPhoto: currentProviderData.bannerPhoto,
              phone: currentProviderData.phone,
              website: currentProviderData.website,
              experience: currentProviderData.experience,
              address: currentProviderData.address,
              city: currentProviderData.city,
              state: currentProviderData.state,
              zipCode: currentProviderData.zipCode
            });

            setProfileData({
              businessName: currentProviderData.businessName || '',
              ownerName: currentProviderData.ownerName || user?.name || '',
              description: currentProviderData.description || '',
              profilePhoto: currentProviderData.profilePhoto || '/favicon.png',
              bannerPhoto: currentProviderData.bannerPhoto || '/fetchlylogo.png',
              email: currentProviderData.email || user?.email || '',
              phone: currentProviderData.phone || '',
              website: currentProviderData.website || '',
              serviceType: currentProviderData.serviceType || '',
              experience: currentProviderData.experience || '',
              specialties: currentProviderData.specialties || [],
              address: currentProviderData.address || '',
              city: currentProviderData.city || user?.city || '',
              state: currentProviderData.state || '',
              zipCode: currentProviderData.zipCode || '',
              businessHours: (currentProviderData.businessHours as any) || profileData.businessHours,
              status: currentProviderData.status || 'pending'
            });
          } else {
            console.log('No provider data found, using defaults');
            // No provider data exists - show defaults with user info
            setProfileData(prev => ({
              ...prev,
              ownerName: user?.name || '',
              email: user?.email || '',
              city: user?.city || ''
            }));
          }
        }

        // Load posts
        await loadProviderPosts();

      } catch (error) {
        console.error('Error loading provider data:', error);
        toast.error('Failed to load provider profile');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadData();
    }
  }, [user, provider, providerId]);

  const loadProviderPosts = async () => {
    try {
      // For posts, we need to use the user ID, not the provider document ID
      let targetUserId;

      if (providerId && providerId !== user?.id) {
        // Viewing someone else's profile - providerId is the user ID
        targetUserId = providerId;
      } else {
        // Own profile - use current user ID
        targetUserId = user?.id;
      }

      if (!targetUserId) {
        console.log('No target user ID for loading posts');
        return;
      }

      console.log('Loading posts for user ID:', targetUserId);

      const postsQuery = query(
        collection(db, 'posts'),
        where('userId', '==', targetUserId),
        where('isPublic', '==', true)
      );

      const postsSnapshot = await getDocs(postsQuery);
      const posts = postsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      console.log('Loaded posts:', posts.length);
      setProviderPosts(posts);
    } catch (error) {
      console.error('Error loading provider posts:', error);
      // Don't show error toast for posts as it's not critical
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({ ...prev, [name]: value }));
  };

  // Handle avatar upload
  const handleAvatarUpload = async (file: File) => {
    if (!user) return;

    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast.error(validation.error || 'Invalid image file');
      return;
    }

    try {
      setSaving(true);

      // Compress image
      const compressedFile = await compressImage(file, 400, 400, 0.8);

      // Generate storage path
      const path = generateStoragePath(user.id, 'provider-avatar', file.name);

      // Upload image
      const result = await uploadImage(compressedFile, path, (progress) => {
        setUploadProgress(progress.progress);
      });

      if (result.success && result.url) {
        // Create updated profile data with the new photo URL
        const updatedProfileData = { ...profileData, profilePhoto: result.url };
        
        // Update local state
        setProfileData(updatedProfileData);
        setUploadProgress(0);
        
        // Auto-save the profile with the new photo
        const providerData: Partial<Provider> = {
          ...updatedProfileData,
          userId: user.id,
          businessPhotos: provider?.businessPhotos || [],
          rating: provider?.rating || 0,
          reviewCount: provider?.reviewCount || 0,
          totalBookings: provider?.totalBookings || 0,
          totalRevenue: provider?.totalRevenue || 0,
          completionRate: provider?.completionRate || 0,
          responseTime: provider?.responseTime || '< 1 hour',
          responseRate: provider?.responseRate || 100,
          membershipTier: provider?.membershipTier || 'free',
          fetchPoints: provider?.fetchPoints || 0,
          commissionsaved: provider?.commissionsaved || 0,
          socialMedia: provider?.socialMedia || {},
          settings: provider?.settings || {
            emailNotifications: true,
            smsNotifications: true,
            bookingNotifications: true,
            marketingEmails: false,
            autoAcceptBookings: false,
            requireDeposit: false,
            cancellationPolicy: 'Standard'
          },
          verified: provider?.verified || false,
          featured: provider?.featured || false
        };

        // Save to database
        await updateProviderProfile(providerData);
        await refreshProviderData();
        
        toast.success('Profile picture updated and saved!');
      } else {
        toast.error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error('Upload failed. Please try again.');
    } finally {
      setSaving(false);
      setUploadProgress(0);
    }
  };

  // Handle banner upload
  const handleBannerUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast.error(validation.error || 'Invalid image file');
      return;
    }

    try {
      setSaving(true);

      // Compress image for banner (larger dimensions)
      const compressedFile = await compressImage(file, 1200, 400, 0.8);

      // Generate storage path
      const path = generateStoragePath(user.id, 'provider-banner', file.name);

      // Upload image
      const result = await uploadImage(compressedFile, path, (progress) => {
        setUploadProgress(progress.progress);
      });

      if (result.success && result.url) {
        // Create updated profile data with the new banner URL
        const updatedProfileData = { ...profileData, bannerPhoto: result.url };
        
        // Update local state
        setProfileData(updatedProfileData);
        setUploadProgress(0);
        
        // Auto-save the profile with the new banner
        const providerData: Partial<Provider> = {
          ...updatedProfileData,
          userId: user.id,
          businessPhotos: provider?.businessPhotos || [],
          rating: provider?.rating || 0,
          reviewCount: provider?.reviewCount || 0,
          totalBookings: provider?.totalBookings || 0,
          totalRevenue: provider?.totalRevenue || 0,
          completionRate: provider?.completionRate || 0,
          responseTime: provider?.responseTime || '< 1 hour',
          responseRate: provider?.responseRate || 100,
          membershipTier: provider?.membershipTier || 'free',
          fetchPoints: provider?.fetchPoints || 0,
          commissionsaved: provider?.commissionsaved || 0,
          socialMedia: provider?.socialMedia || {},
          settings: provider?.settings || {
            emailNotifications: true,
            smsNotifications: true,
            bookingNotifications: true,
            marketingEmails: false,
            autoAcceptBookings: false,
            requireDeposit: false,
            cancellationPolicy: 'Standard'
          },
          verified: provider?.verified || false,
          featured: provider?.featured || false
        };

        // Save to database
        await updateProviderProfile(providerData);
        await refreshProviderData();
        
        toast.success('Banner updated and saved!');
      } else {
        toast.error(result.error || 'Banner upload failed');
      }
    } catch (error) {
      console.error('Error uploading banner:', error);
      toast.error('Banner upload failed. Please try again.');
    } finally {
      setSaving(false);
      setUploadProgress(0);
    }
  };

  const handleSaveProfile = async () => {
    if (!user?.id) return;

    try {
      setSaving(true);

      // Validate required fields
      if (!profileData.businessName || !profileData.email) {
        const errorMessage = 'Please fill in Business Name and Email';
        toast.error(errorMessage);
        return;
      }

      console.log('Saving profile data:', profileData);

      // Prepare data for Provider interface
      const providerData: Partial<Provider> = {
        ...profileData,
        userId: user.id,
        // Ensure required fields have defaults
        businessPhotos: [],
        rating: provider?.rating || 0,
        reviewCount: provider?.reviewCount || 0,
        totalBookings: provider?.totalBookings || 0,
        totalRevenue: provider?.totalRevenue || 0,
        completionRate: provider?.completionRate || 0,
        responseTime: provider?.responseTime || '< 1 hour',
        responseRate: provider?.responseRate || 100,
        membershipTier: provider?.membershipTier || 'free',
        fetchPoints: provider?.fetchPoints || 0,
        commissionsaved: provider?.commissionsaved || 0,
        socialMedia: provider?.socialMedia || {},
        settings: provider?.settings || {
          emailNotifications: true,
          smsNotifications: true,
          bookingNotifications: true,
          marketingEmails: false,
          autoAcceptBookings: false,
          requireDeposit: false,
          cancellationPolicy: 'Standard'
        },
        verified: provider?.verified || false,
        featured: provider?.featured || false
      };

      console.log('Updating provider with data:', providerData);
      await updateProviderProfile(providerData);

      // Force refresh the provider data to ensure it's loaded
      console.log('Refreshing provider data after save...');
      await refreshProviderData();

      const successMessage = 'Profile updated successfully!';
      toast.success(successMessage);
      setEditing({ profile: false, banner: false });
    } catch (error) {
      console.error('Error saving profile:', error);
      const errorMessage = 'Failed to update profile';
      toast.error(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleFollowProvider = async () => {
    try {
      if (!user || !currentProvider) return;
      
      await addDoc(collection(db, 'follows'), {
        followerId: user.id,
        followingId: currentProvider.id,
        createdAt: new Date()
      });
      
      setIsFollowing(true);
      toast.success('Following provider!');
    } catch (error) {
      console.error('Error following provider:', error);
      toast.error('Failed to follow provider');
    }
  };

  const handleAddFriend = async () => {
    try {
      if (!user || !currentProvider) return;

      await addDoc(collection(db, 'friendRequests'), {
        fromUserId: user.id,
        toUserId: currentProvider.userId,
        status: 'pending',
        createdAt: new Date()
      });

      toast.success('Friend request sent!');
    } catch (error) {
      console.error('Error sending friend request:', error);
      toast.error('Failed to send friend request');
    }
  };

  const handleBookService = (service: Service) => {
    if (!user) {
      toast.error('Please sign in to book a service');
      return;
    }

    if (user.role !== 'pet_owner') {
      toast.error('Only pet owners can book services');
      return;
    }

    setSelectedService(service);
    setShowBookingModal(true);
  };

  // Handle authentication
  useEffect(() => {
    if (!user) {
      router.push('/auth/signin');
      return;
    }
  }, [user, router]);

  // Check if we're viewing own profile or someone else's
  // isOwnProfile is already declared above

  if (isLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading provider profile...</p>
        </div>
      </div>
    );
  }

  // If viewing someone else's profile and no data, show not found
  if (!currentProvider && !viewingOwnProfile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-6 max-w-md">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-4">
            <h2 className="text-lg font-semibold text-yellow-800 mb-2">Provider Profile Not Found</h2>
            <p className="text-yellow-600 mb-4">
              This provider profile doesn't exist or hasn't been set up yet.
            </p>
            <button
              onClick={() => router.push('/community')}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Community
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50 pt-20">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header - Enhanced */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 gap-4">
          <div>
            <h1 className="text-4xl font-black bg-gradient-to-r from-green-500 to-blue-500 bg-clip-text text-transparent mb-2">
              Professional Provider
            </h1>
            <p className="text-gray-600 font-medium text-lg">Manage your business profile and services</p>
          </div>
          {isOwnProfile && (
            <button
              onClick={() => router.push('/provider/dashboard')}
              className="px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-white/20 backdrop-blur-sm font-semibold flex items-center space-x-2"
            >
              <Home className="w-5 h-5" />
              <span>Dashboard</span>
            </button>
          )}
        </div>

        {/* Profile Header - Hot Provider Design */}
        <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30 overflow-hidden mb-8 hover:shadow-3xl transition-all duration-500">
          <div className="relative w-full bg-gradient-to-r from-green-400 via-blue-500 to-cyan-500 overflow-hidden" style={{ height: '512px' }}>
            {/* Animated business background pattern */}
            <div className="absolute inset-0 opacity-20">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-transparent via-white/10 to-transparent animate-pulse"></div>
              <div className="absolute top-10 left-10 w-32 h-32 bg-white/5 rounded-full blur-xl animate-bounce"></div>
              <div className="absolute bottom-10 right-10 w-24 h-24 bg-cyan-200/20 rounded-full blur-lg animate-pulse"></div>
              <div className="absolute top-1/2 left-1/2 w-40 h-40 bg-green-200/10 rounded-full blur-2xl animate-ping"></div>
            </div>

            {/* Professional badge */}
            <div className="absolute top-6 left-6 bg-white/20 backdrop-blur-md px-4 py-2 rounded-2xl border border-white/30">
              <div className="flex items-center space-x-2">
                <Briefcase className="w-5 h-5 text-white" />
                <span className="text-white font-semibold">Verified Business</span>
              </div>
            </div>
            {profileData.bannerPhoto ? (
              <div className="relative w-full h-full">
                <img
                  src={profileData.bannerPhoto}
                  alt="Business banner"
                  className={`w-full h-full cursor-pointer transition-all duration-300 ${
                    bannerFit === 'cover' ? 'object-cover' :
                    bannerFit === 'contain' ? 'object-contain' : 'object-fill'
                  }`}
                  style={{
                    objectPosition: `${bannerPosition.x}% ${bannerPosition.y}%`,
                    transform: isDragging ? 'scale(1.02)' : 'scale(1)'
                  }}
                  onClick={() => setShowBannerModal(true)}
                  onMouseDown={(e) => {
                    if (editing.profile) {
                      setIsDragging(true);
                      const rect = e.currentTarget.getBoundingClientRect();
                      const x = ((e.clientX - rect.left) / rect.width) * 100;
                      const y = ((e.clientY - rect.top) / rect.height) * 100;
                      setBannerPosition({ x: Math.max(0, Math.min(100, x)), y: Math.max(0, Math.min(100, y)) });
                    }
                  }}
                  onMouseUp={() => setIsDragging(false)}
                  onMouseLeave={() => setIsDragging(false)}
                />

                {/* Banner Controls */}
                {editing.profile && (
                  <div className="absolute top-4 left-4 flex gap-2">
                    <div className="bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-md">
                      <select
                        value={bannerFit}
                        onChange={(e) => setBannerFit(e.target.value as 'cover' | 'contain' | 'fill')}
                        className="text-sm border-none bg-transparent focus:outline-none"
                      >
                        <option value="cover">Fit to Cover</option>
                        <option value="contain">Fit to Screen</option>
                        <option value="fill">Fill Container</option>
                      </select>
                    </div>
                    <button
                      onClick={() => setBannerPosition({ x: 50, y: 50 })}
                      className="bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-md hover:bg-white transition-colors"
                      title="Reset Position"
                    >
                      <RotateCcw className="w-4 h-4 text-gray-600" />
                    </button>
                    <button
                      onClick={() => setShowBannerModal(true)}
                      className="bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-md hover:bg-white transition-colors"
                      title="View Full Image"
                    >
                      <Maximize2 className="w-4 h-4 text-gray-600" />
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            )}

            {/* Upload Button */}
            {editing.profile && (
              <label className="absolute top-4 right-4 bg-white bg-opacity-90 rounded-lg p-2 shadow-md hover:bg-opacity-100 cursor-pointer">
                <Upload className="w-5 h-5 text-gray-600" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleBannerUpload}
                  className="hidden"
                />
              </label>
            )}

            {/* Position Indicator */}
            {editing.profile && profileData.bannerPhoto && (
              <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1 text-xs text-gray-600">
                Position: {Math.round(bannerPosition.x)}%, {Math.round(bannerPosition.y)}%
              </div>
            )}
          </div>

          <div className="relative px-6 pb-6">
            <div className="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
              {/* Profile Picture - Enhanced Provider Design */}
              <div className="relative -mt-20 mb-6 sm:mb-0">
                <div className="relative group">
                  <div className="w-44 h-44 rounded-full bg-gradient-to-r from-green-400 to-blue-500 p-1.5 shadow-2xl">
                    <img
                      src={profileData.profilePhoto || '/favicon.png'}
                      alt={profileData.businessName || 'Business Profile'}
                      className="w-full h-full rounded-full object-cover border-4 border-white shadow-lg group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  {isOwnProfile && (
                    <label className="absolute bottom-4 right-4 bg-gradient-to-r from-green-500 to-blue-500 rounded-full p-3 shadow-xl hover:shadow-2xl cursor-pointer transform hover:scale-110 transition-all duration-300 border-2 border-white">
                      <Camera className="w-5 h-5 text-white" />
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleAvatarUpload(file);
                        }}
                        className="hidden"
                      />
                    </label>
                  )}
                  {uploadProgress > 0 && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/60 backdrop-blur-sm rounded-full">
                      <div className="text-white text-lg font-bold">{uploadProgress}%</div>
                    </div>
                  )}
                  {/* Professional verification badge */}
                  <div className="absolute top-3 right-3 w-8 h-8 bg-green-500 rounded-full border-3 border-white shadow-lg flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-white" />
                  </div>
                </div>
              </div>

              {/* Store Settings */}
              {editing.profile && (
                <div className="bg-white/30 backdrop-blur-sm p-6 rounded-2xl border border-white/20 shadow-xl mb-6">
                  <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                    <ShoppingCart className="w-5 h-5 mr-2 text-purple-600" />
                    Store Settings
                  </h3>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="hasStore"
                      name="hasStore"
                      checked={profileData.hasStore}
                      onChange={(e) => setProfileData({ ...profileData, hasStore: e.target.checked })}
                      className="h-5 w-5 text-blue-600 rounded focus:ring-blue-500 border-gray-300"
                    />
                    <label htmlFor="hasStore" className="ml-3 text-gray-700 font-medium">
                      Enable Online Store
                    </label>
                  </div>
                  {profileData.hasStore && (
                    <p className="mt-2 text-sm text-gray-600">
                      Your store is visible at:{' '}
                      <span className="font-mono text-blue-600">/store/{provider?.id || 'your-id'}</span>
                    </p>
                  )}
                </div>
              )}

              {/* Business Information */}
              <div className="flex-1">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                  <div className="mb-4 sm:mb-0">
                    {editing.profile ? (
                      <div className="space-y-2">
                        <input
                          type="text"
                          value={profileData.businessName}
                          onChange={handleInputChange}
                          name="businessName"
                          className="text-2xl font-bold text-gray-900 bg-transparent border-b-2 border-blue-500 focus:outline-none w-full"
                          placeholder="Business Name"
                        />
                        <input
                          type="text"
                          value={profileData.ownerName}
                          onChange={handleInputChange}
                          name="ownerName"
                          className="text-lg text-gray-600 bg-transparent border-b border-gray-300 focus:outline-none w-full"
                          placeholder="Owner Name"
                        />
                        <select
                          value={profileData.serviceType}
                          onChange={handleInputChange}
                          name="serviceType"
                          className="text-sm text-gray-500 bg-transparent border-b border-gray-300 focus:outline-none w-full"
                        >
                          <option value="">Select Service Type</option>
                          <option value="Dog Walking">Dog Walking</option>
                          <option value="Pet Sitting">Pet Sitting</option>
                          <option value="Pet Grooming">Pet Grooming</option>
                          <option value="Veterinary">Veterinary</option>
                          <option value="Pet Training">Pet Training</option>
                          <option value="Pet Boarding">Pet Boarding</option>
                        </select>
                        <div className="grid grid-cols-2 gap-2">
                          <input
                            type="text"
                            value={profileData.city}
                            onChange={handleInputChange}
                            name="city"
                            className="text-sm text-gray-500 bg-transparent border-b border-gray-300 focus:outline-none"
                            placeholder="City"
                          />
                          <input
                            type="text"
                            value={profileData.state}
                            onChange={handleInputChange}
                            name="state"
                            className="text-sm text-gray-500 bg-transparent border-b border-gray-300 focus:outline-none"
                            placeholder="State"
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
                            {profileData.businessName || 'Your Business Name'}
                          </h1>
                          <p className="text-xl text-gray-700 font-medium">
                            {profileData.ownerName || 'Owner Name'}
                          </p>
                        </div>
                        <div className="flex flex-wrap gap-3">
                          <div className="flex items-center space-x-2 bg-green-50 px-4 py-2 rounded-full">
                            <Briefcase className="w-5 h-5 text-green-600" />
                            <span className="text-green-800 font-semibold">
                              {profileData.serviceType || 'Pet Service Provider'}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-full">
                            <MapPin className="w-5 h-5 text-blue-600" />
                            <span className="text-blue-800 font-semibold">
                              {profileData.city && profileData.state
                                ? `${profileData.city}, ${profileData.state}`
                                : 'Location not set'
                              }
                            </span>
                          </div>
                          {/* Professional rating badge */}
                          <div className="flex items-center space-x-2 bg-yellow-50 px-4 py-2 rounded-full">
                            <Award className="w-5 h-5 text-yellow-600" />
                            <span className="text-yellow-800 font-semibold">5.0 ★ Professional</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons - Enhanced Hot Design */}
                  <div className="flex flex-wrap gap-4 mt-6">
                    {isOwnProfile ? (
                      <>
                        {editing.profile ? (
                          <>
                            <button
                              onClick={handleSaveProfile}
                              disabled={saving}
                              className="px-8 py-4 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-2xl shadow-lg hover:shadow-xl disabled:opacity-50 flex items-center space-x-3 transform hover:scale-105 transition-all duration-300 border border-green-400 font-semibold"
                            >
                              <Save className="w-5 h-5" />
                              <span>{saving ? 'Saving...' : 'Save Profile'}</span>
                            </button>
                            <button
                              onClick={() => setEditing({ profile: false, banner: false })}
                              className="px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white rounded-2xl shadow-lg hover:shadow-xl flex items-center space-x-3 transform hover:scale-105 transition-all duration-300 border border-gray-400 font-semibold"
                            >
                              <X className="w-5 h-5" />
                              <span>Cancel</span>
                            </button>
                          </>
                        ) : (
                          <button
                            onClick={() => setEditing({ profile: true, banner: false })}
                            className="px-10 py-4 bg-gradient-to-r from-green-500 via-blue-500 to-cyan-500 hover:from-green-600 hover:via-blue-600 hover:to-cyan-600 text-white rounded-2xl shadow-xl hover:shadow-2xl flex items-center space-x-3 transform hover:scale-105 transition-all duration-300 border border-white/20 backdrop-blur-sm font-bold text-lg"
                          >
                            <Edit className="w-5 h-5" />
                            <span>Edit Profile</span>
                          </button>
                        )}
                      </>
                    ) : (
                      <>
                        {!isOwnProfile && provider?.hasStore && (
                          <a
                            href={`/store/${provider.id}`}
                            className="px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-2xl shadow-xl hover:shadow-2xl flex items-center space-x-3 transform hover:scale-105 transition-all duration-300 border border-white/20 backdrop-blur-sm font-bold text-lg"
                          >
                            <ShoppingCart className="w-5 h-5" />
                            <span>View Store</span>
                          </a>
                        )}
                        <button
                          onClick={handleFollowProvider}
                          className={`px-8 py-4 rounded-2xl flex items-center space-x-3 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl border backdrop-blur-sm font-bold text-lg ${
                            isFollowing
                              ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white border-green-400'
                              : 'bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white border-blue-400'
                          }`}
                        >
                          {isFollowing ? <UserCheck className="w-5 h-5" /> : <UserPlus className="w-5 h-5" />}
                          <span>{isFollowing ? 'Following' : 'Follow'}</span>
                        </button>
                        <button
                          onClick={handleAddFriend}
                          className="px-6 py-4 bg-white/60 backdrop-blur-sm hover:bg-white/80 text-gray-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-white/30 flex items-center space-x-2 font-semibold"
                        >
                          <UserPlus className="w-5 h-5" />
                          <span>Add Friend</span>
                        </button>
                        <button className="px-6 py-4 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-green-400 flex items-center space-x-2 font-semibold">
                          <MessageCircle className="w-5 h-5" />
                          <span>Message</span>
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Provider Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 text-center hover:shadow-xl transition-all duration-300">
            <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent mb-2">
              {provider?.totalBookings || 0}
            </div>
            <div className="text-gray-600">Total Bookings</div>
          </div>
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 text-center hover:shadow-xl transition-all duration-300">
            <div className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
              {provider?.rating || 0}★
            </div>
            <div className="text-gray-600">Rating</div>
          </div>
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 text-center hover:shadow-xl transition-all duration-300">
            <div className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
              {provider?.responseRate || 0}%
            </div>
            <div className="text-gray-600">Response Rate</div>
          </div>
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 text-center hover:shadow-xl transition-all duration-300">
            <div className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-2">
              {providerPosts.length}
            </div>
            <div className="text-gray-600">Posts</div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Sidebar - Business Details */}
          <div className="lg:col-span-1 space-y-6">
            {/* Business Contact Info */}
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
              <h2 className="text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-4">Contact Information</h2>
              {editing.profile ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input
                      type="email"
                      name="email"
                      value={profileData.email}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input
                      type="tel"
                      name="phone"
                      value={profileData.phone}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="(*************"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                    <input
                      type="url"
                      name="website"
                      value={profileData.website}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="https://yourbusiness.com"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Experience</label>
                    <input
                      type="text"
                      name="experience"
                      value={profileData.experience}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="5 years"
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {profileData.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="w-5 h-5 text-gray-400" />
                      <span className="text-gray-600">{profileData.email}</span>
                    </div>
                  )}
                  {profileData.phone && (
                    <div className="flex items-center space-x-3">
                      <Phone className="w-5 h-5 text-gray-400" />
                      <span className="text-gray-600">{profileData.phone}</span>
                    </div>
                  )}
                  {profileData.website && (
                    <div className="flex items-center space-x-3">
                      <Globe className="w-5 h-5 text-gray-400" />
                      <a href={profileData.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        Visit Website
                      </a>
                    </div>
                  )}
                  {profileData.experience && (
                    <div className="flex items-center space-x-3">
                      <Award className="w-5 h-5 text-gray-400" />
                      <span className="text-gray-600">{profileData.experience} experience</span>
                    </div>
                  )}
                  {!profileData.email && !profileData.phone && !profileData.website && !profileData.experience && (
                    <p className="text-gray-500 text-center py-4">No contact information available</p>
                  )}
                </div>
              )}
            </div>

            {/* Business Description */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">About</h2>
              {editing.profile ? (
                <textarea
                  name="description"
                  value={profileData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Tell clients about your business and services..."
                />
              ) : (
                <p className="text-gray-600">
                  {profileData.description || 'No description available yet.'}
                </p>
              )}
            </div>
          </div>

          {/* Right Content - Services and Posts */}
          <div className="lg:col-span-2 space-y-6">
            {/* Services Section */}
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">Services Offered</h2>
                {isOwnProfile && (
                  <button
                    onClick={() => router.push('/provider/dashboard?tab=services')}
                    className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-300 flex items-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add Service</span>
                  </button>
                )}
              </div>

              {currentServices && currentServices.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentServices.map((service) => (
                    <div key={service.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <h3 className="font-semibold text-gray-900">{service.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                      <div className="flex items-center justify-between mt-3">
                        <span className="text-lg font-bold text-blue-600">${service.price}</span>
                        <span className="text-sm text-gray-500">{service.duration} min</span>
                      </div>
                      {service.category && (
                        <div className="mt-2">
                          <span className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                            {service.category}
                          </span>
                        </div>
                      )}
                      {!isOwnProfile && (
                        <div className="mt-3">
                          <button
                            onClick={() => handleBookService(service)}
                            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                          >
                            Book This Service
                          </button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No services yet</h3>
                  <p className="text-gray-500 mb-4">
                    {isOwnProfile
                      ? 'Add your first service to start attracting clients'
                      : 'This provider hasn\'t added any services yet'
                    }
                  </p>
                  {isOwnProfile && (
                    <button
                      onClick={() => router.push('/provider/dashboard?tab=services')}
                      className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
                    >
                      Add Your First Service
                    </button>
                  )}
                </div>
              )}
            </div>

            {/* Posts Section */}
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">Recent Posts</h2>
                {isOwnProfile && (
                  <button
                    onClick={() => router.push('/profile')}
                    className="text-green-600 hover:text-blue-600 text-sm font-medium transition-colors duration-300"
                  >
                    Manage Posts
                  </button>
                )}
              </div>

              <div className="space-y-6">
                {providerPosts.length > 0 ? (
                  providerPosts.map((post) => (
                    <PostCard
                      key={post.id}
                      post={post}
                      showPrivacyIndicator={isOwnProfile}
                    />
                  ))
                ) : (
                  <div className="text-center py-12">
                    <PawPrint className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No posts yet</h3>
                    <p className="text-gray-500 mb-4">
                      {isOwnProfile
                        ? 'Share updates about your business and connect with pet owners'
                        : 'This provider hasn\'t shared any posts yet'
                      }
                    </p>
                    {isOwnProfile && (
                      <button
                        onClick={() => router.push('/profile')}
                        className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
                      >
                        Create Your First Post
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Banner Modal */}
        {showBannerModal && profileData.bannerPhoto && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div className="relative max-w-4xl max-h-full">
              <img
                src={profileData.bannerPhoto}
                alt="Business banner"
                className="max-w-full max-h-full object-contain"
              />
              <button
                onClick={() => setShowBannerModal(false)}
                className="absolute top-4 right-4 bg-white bg-opacity-90 rounded-full p-2 hover:bg-opacity-100"
              >
                <X className="w-6 h-6 text-gray-600" />
              </button>
            </div>
          </div>
        )}

        {/* Booking Request Modal */}
        {showBookingModal && selectedService && viewedProvider && (
          <BookingRequestModal
            isOpen={showBookingModal}
            onClose={() => {
              setShowBookingModal(false);
              setSelectedService(null);
            }}
            service={{
              id: selectedService.id || '',
              name: selectedService.name,
              price: selectedService.price,
              duration: selectedService.duration,
              description: selectedService.description || ''
            }}
            provider={{
              id: viewedProvider.id || '',
              businessName: viewedProvider.businessName || '',
              ownerName: viewedProvider.ownerName || '',
              email: viewedProvider.email || '',
              phone: viewedProvider.phone || ''
            }}
          />
        )}
      </div>
    </div>
  );
}
