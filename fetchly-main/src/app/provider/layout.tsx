'use client';

import { useAuth } from '@/contexts/AuthContext';
import { ProviderProvider } from '@/contexts/ProviderContext';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect } from 'react';

export default function ProviderLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Don't redirect for public provider profiles - they should be accessible to everyone
    const isPublicProfile = pathname.includes('/provider/public/');

    console.log('🔍 Provider Layout Check:', {
      pathname,
      isPublicProfile,
      user: user ? 'exists' : 'null',
      userRole: user?.role,
      isLoading
    });

    if (!isLoading && !isPublicProfile && (!user || user.role !== 'provider')) {
      console.log('🚫 Redirecting to signin - not a provider or public profile');
      router.push('/auth/signin');
    }
  }, [user, isLoading, router, pathname]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // Allow public provider profiles to render even without provider authentication
  const isPublicProfile = pathname.includes('/provider/public/');

  if (!isPublicProfile && (!user || user.role !== 'provider')) {
    return null;
  }

  return (
    <ProviderProvider>
      <div className="min-h-screen bg-gray-50">
        {children}
      </div>
    </ProviderProvider>
  );
}
