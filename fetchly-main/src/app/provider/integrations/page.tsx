'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Link,
  Plus,
  CheckCircle2,
  XCircle,
  Settings,
  ExternalLink,
  Zap,
  Calendar,
  CreditCard,
  Mail,
  BarChart3,
  MessageSquare,
  Cloud,
  Shield,
  Smartphone,
  Globe,
  Search,
  Filter,
  Star,
  Users,
  Clock,
  DollarSign,
  AlertCircle,
  RefreshCw,
  Download,
  Upload,
  Eye,
  Edit3,
  Trash2
} from 'lucide-react';

interface Integration {
  id: string;
  name: string;
  type: 'calendar' | 'payment' | 'communication' | 'analytics' | 'social' | 'storage' | 'security';
  icon: React.ComponentType<any>;
  connected: boolean;
  description: string;
  features: string[];
  pricing: string;
  popularity: number;
  rating: number;
  setupTime: string;
  lastSync?: string;
  status: 'active' | 'error' | 'syncing' | 'disconnected';
  website: string;
}

const mockIntegrations: Integration[] = [
  {
    id: 'google-calendar',
    name: 'Google Calendar',
    type: 'calendar',
    icon: Calendar,
    connected: true,
    description: 'Sync your appointments with Google Calendar for seamless scheduling',
    features: ['Two-way sync', 'Automatic reminders', 'Multiple calendar support', 'Mobile notifications'],
    pricing: 'Free',
    popularity: 95,
    rating: 4.8,
    setupTime: '2 minutes',
    lastSync: '2024-01-20 10:30 AM',
    status: 'active',
    website: 'https://calendar.google.com'
  },
  {
    id: 'stripe',
    name: 'Stripe',
    type: 'payment',
    icon: CreditCard,
    connected: true,
    description: 'Accept online payments securely with industry-leading payment processing',
    features: ['Credit card processing', 'Recurring payments', 'Fraud protection', 'Global payments'],
    pricing: '2.9% + 30¢ per transaction',
    popularity: 88,
    rating: 4.7,
    setupTime: '5 minutes',
    lastSync: '2024-01-20 09:15 AM',
    status: 'active',
    website: 'https://stripe.com'
  },
  {
    id: 'mailchimp',
    name: 'Mailchimp',
    type: 'communication',
    icon: Mail,
    connected: false,
    description: 'Email marketing and customer communication platform',
    features: ['Email campaigns', 'Customer segmentation', 'Automation', 'Analytics'],
    pricing: 'Free up to 2,000 contacts',
    popularity: 82,
    rating: 4.5,
    setupTime: '10 minutes',
    status: 'disconnected',
    website: 'https://mailchimp.com'
  },
  {
    id: 'quickbooks',
    name: 'QuickBooks',
    type: 'analytics',
    icon: BarChart3,
    connected: false,
    description: 'Accounting and financial management for small businesses',
    features: ['Invoice management', 'Expense tracking', 'Tax preparation', 'Financial reports'],
    pricing: '$15/month',
    popularity: 75,
    rating: 4.3,
    setupTime: '15 minutes',
    status: 'disconnected',
    website: 'https://quickbooks.intuit.com'
  },
  {
    id: 'facebook-business',
    name: 'Facebook Business',
    type: 'social',
    icon: Globe,
    connected: true,
    description: 'Manage your Facebook business presence and advertising',
    features: ['Page management', 'Ad campaigns', 'Customer insights', 'Messenger integration'],
    pricing: 'Free (ad spend separate)',
    popularity: 90,
    rating: 4.2,
    setupTime: '8 minutes',
    lastSync: '2024-01-19 06:00 PM',
    status: 'active',
    website: 'https://business.facebook.com'
  },
  {
    id: 'dropbox',
    name: 'Dropbox Business',
    type: 'storage',
    icon: Cloud,
    connected: false,
    description: 'Cloud storage and file sharing for business documents',
    features: ['File sync', 'Team collaboration', 'Version history', 'Advanced security'],
    pricing: '$15/user/month',
    popularity: 70,
    rating: 4.4,
    setupTime: '5 minutes',
    status: 'disconnected',
    website: 'https://dropbox.com/business'
  },
  {
    id: 'twilio',
    name: 'Twilio',
    type: 'communication',
    icon: MessageSquare,
    connected: false,
    description: 'SMS and voice communication platform for customer engagement',
    features: ['SMS messaging', 'Voice calls', 'WhatsApp integration', 'Programmable chat'],
    pricing: 'Pay per use',
    popularity: 65,
    rating: 4.6,
    setupTime: '12 minutes',
    status: 'disconnected',
    website: 'https://twilio.com'
  },
  {
    id: 'zapier',
    name: 'Zapier',
    type: 'analytics',
    icon: Zap,
    connected: false,
    description: 'Automate workflows between your favorite apps',
    features: ['App automation', 'Custom workflows', '3000+ app integrations', 'Multi-step zaps'],
    pricing: 'Free up to 100 tasks/month',
    popularity: 78,
    rating: 4.5,
    setupTime: '20 minutes',
    status: 'disconnected',
    website: 'https://zapier.com'
  }
];

const integrationTypes = [
  { id: 'all', label: 'All Integrations', icon: Link },
  { id: 'calendar', label: 'Calendar', icon: Calendar },
  { id: 'payment', label: 'Payment', icon: CreditCard },
  { id: 'communication', label: 'Communication', icon: MessageSquare },
  { id: 'analytics', label: 'Analytics', icon: BarChart3 },
  { id: 'social', label: 'Social Media', icon: Globe },
  { id: 'storage', label: 'Storage', icon: Cloud },
  { id: 'security', label: 'Security', icon: Shield }
];

export default function IntegrationsPage() {
  const { user } = useAuth();
  const [integrations, setIntegrations] = useState(mockIntegrations);
  const [selectedType, setSelectedType] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showConnectedOnly, setShowConnectedOnly] = useState(false);

  if (!user || user.role !== 'provider') {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-cool-800 mb-4">Access Denied</h1>
          <p className="text-cool-600">You need to be logged in as a service provider to access this page.</p>
        </div>
      </div>
    );
  }

  const filteredIntegrations = integrations.filter(integration => {
    const matchesType = selectedType === 'all' || integration.type === selectedType;
    const matchesSearch = integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         integration.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesConnection = !showConnectedOnly || integration.connected;
    return matchesType && matchesSearch && matchesConnection;
  });

  const connectedCount = integrations.filter(i => i.connected).length;
  const activeCount = integrations.filter(i => i.status === 'active').length;
  const errorCount = integrations.filter(i => i.status === 'error').length;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'syncing': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle2 className="w-4 h-4 text-green-600" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-600" />;
      case 'syncing': return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />;
      default: return <XCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const handleConnect = (integrationId: string) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === integrationId 
        ? { ...integration, connected: true, status: 'active' as const, lastSync: new Date().toLocaleString() }
        : integration
    ));
  };

  const handleDisconnect = (integrationId: string) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === integrationId 
        ? { ...integration, connected: false, status: 'disconnected' as const, lastSync: undefined }
        : integration
    ));
  };

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-cool-800 mb-2">Integrations</h1>
            <p className="text-cool-600">Connect external applications to streamline your workflow</p>
          </div>
          
          <div className="flex items-center gap-4">
            <button className="btn-secondary">
              <Download className="w-4 h-4 mr-2" />
              Export Settings
            </button>
            <button className="btn-primary">
              <Plus className="w-4 h-4 mr-2" />
              Browse Marketplace
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-primary-100 rounded-xl">
                <Link className="w-6 h-6 text-primary-600" />
              </div>
              <span className="text-green-500 text-sm font-medium">+2 this month</span>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">{connectedCount}</h3>
            <p className="text-cool-600 text-sm">Connected Apps</p>
          </div>

          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-100 rounded-xl">
                <CheckCircle2 className="w-6 h-6 text-green-600" />
              </div>
              <span className="text-green-500 text-sm font-medium">98% uptime</span>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">{activeCount}</h3>
            <p className="text-cool-600 text-sm">Active Integrations</p>
          </div>

          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-red-100 rounded-xl">
                <AlertCircle className="w-6 h-6 text-red-600" />
              </div>
              <span className="text-red-500 text-sm font-medium">{errorCount > 0 ? 'Needs attention' : 'All good'}</span>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">{errorCount}</h3>
            <p className="text-cool-600 text-sm">Issues</p>
          </div>

          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-secondary-100 rounded-xl">
                <Zap className="w-6 h-6 text-secondary-600" />
              </div>
              <span className="text-green-500 text-sm font-medium">+15% efficiency</span>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">24</h3>
            <p className="text-cool-600 text-sm">Automations</p>
          </div>
        </div>

        {/* Filters */}
        <div className="glass-card rounded-2xl p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Type Filter */}
              <div className="flex bg-white/50 rounded-lg p-1 overflow-x-auto">
                {integrationTypes.map((type) => {
                  const Icon = type.icon;
                  return (
                    <button
                      key={type.id}
                      onClick={() => setSelectedType(type.id)}
                      className={`flex items-center gap-2 px-4 py-2 rounded-md font-medium transition-all duration-200 whitespace-nowrap ${
                        selectedType === type.id 
                          ? 'bg-white text-primary-600 shadow-sm' 
                          : 'text-cool-600 hover:text-cool-800'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      {type.label}
                    </button>
                  );
                })}
              </div>

              {/* Connected Only Toggle */}
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={showConnectedOnly}
                  onChange={(e) => setShowConnectedOnly(e.target.checked)}
                  className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
                />
                <span className="text-sm text-cool-700">Connected only</span>
              </label>
            </div>

            <div className="flex items-center gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-cool-500" />
                <input
                  type="text"
                  placeholder="Search integrations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              <button className="btn-secondary">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </button>
            </div>
          </div>
        </div>

        {/* Integrations Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredIntegrations.map((integration) => {
            const Icon = integration.icon;
            return (
              <div key={integration.id} className="glass-card rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-3 bg-primary-100 rounded-xl">
                      <Icon className="w-6 h-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="font-bold text-cool-800">{integration.name}</h3>
                      <div className="flex items-center gap-2">
                        <span className="px-2 py-1 bg-secondary-100 text-secondary-700 text-xs rounded-full capitalize">
                          {integration.type}
                        </span>
                        {getStatusIcon(integration.status)}
                      </div>
                    </div>
                  </div>
                  <button className="p-2 hover:bg-cool-100 rounded-lg transition-colors duration-200">
                    <ExternalLink className="w-4 h-4 text-cool-500" />
                  </button>
                </div>
                
                <p className="text-cool-600 text-sm mb-4">{integration.description}</p>
                
                <div className="space-y-2 mb-4">
                  {integration.features.slice(0, 3).map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <CheckCircle2 className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm text-cool-600">{feature}</span>
                    </div>
                  ))}
                  {integration.features.length > 3 && (
                    <p className="text-xs text-cool-500 ml-6">+{integration.features.length - 3} more features</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <p className="text-cool-500">Pricing</p>
                    <p className="font-medium text-cool-800">{integration.pricing}</p>
                  </div>
                  <div>
                    <p className="text-cool-500">Setup Time</p>
                    <p className="font-medium text-cool-800">{integration.setupTime}</p>
                  </div>
                </div>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium text-cool-700">{integration.rating}</span>
                    <span className="text-xs text-cool-500">({integration.popularity}% use this)</span>
                  </div>
                  {integration.lastSync && (
                    <span className="text-xs text-cool-500">Last sync: {integration.lastSync}</span>
                  )}
                </div>
                
                <div className="flex gap-2">
                  {integration.connected ? (
                    <>
                      <button className="flex-1 btn-secondary text-sm py-2">
                        <Settings className="w-4 h-4 mr-1" />
                        Configure
                      </button>
                      <button 
                        onClick={() => handleDisconnect(integration.id)}
                        className="btn-secondary text-sm py-2 px-3 text-red-600 hover:bg-red-50"
                      >
                        Disconnect
                      </button>
                    </>
                  ) : (
                    <button 
                      onClick={() => handleConnect(integration.id)}
                      className="w-full btn-primary text-sm py-2"
                    >
                      <Plus className="w-4 h-4 mr-1" />
                      Connect
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {filteredIntegrations.length === 0 && (
          <div className="glass-card rounded-xl p-12 text-center">
            <Link className="w-16 h-16 text-cool-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-cool-800 mb-2">No integrations found</h3>
            <p className="text-cool-600 mb-6">Try adjusting your search or filter criteria</p>
            <button 
              onClick={() => {
                setSearchQuery('');
                setSelectedType('all');
                setShowConnectedOnly(false);
              }}
              className="btn-primary"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
