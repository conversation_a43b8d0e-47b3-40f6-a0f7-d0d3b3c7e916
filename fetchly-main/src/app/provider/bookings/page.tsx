'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Calendar,
  Clock,
  User,
  Phone,
  MapPin,
  DollarSign,
  Eye,
  Edit3,
  MessageSquare,
  Filter,
  Search,
  Plus,
  CheckCircle,
  XCircle,
  AlertCircle,
  Download,
  Upload,
  ChevronDown,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface Booking {
  id: string;
  petOwner: string;
  petOwnerEmail: string;
  petOwnerPhone: string;
  petName: string;
  petType: string;
  service: string;
  date: string;
  time: string;
  duration: number;
  status: 'pending_provider_approval' | 'confirmed_awaiting_payment' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  amount: number;
  notes?: string;
  address?: string;
}

const mockBookings: Booking[] = [
  {
    id: 'BK001',
    petOwner: '<PERSON>',
    petOwnerEmail: '<EMAIL>',
    petOwnerPhone: '+****************',
    petName: 'Buddy',
    petType: 'Golden Retriever',
    service: 'Full Service Grooming',
    date: '2024-01-20',
    time: '10:00',
    duration: 120,
    status: 'pending_provider_approval',
    amount: 85,
    notes: '<PERSON> is very friendly but gets anxious during nail trimming',
    address: '123 Oak Street, San Francisco, CA'
  },
  {
    id: 'BK002',
    petOwner: 'Mike Wilson',
    petOwnerEmail: '<EMAIL>',
    petOwnerPhone: '+****************',
    petName: 'Max',
    petType: 'German Shepherd',
    service: 'Veterinary Checkup',
    date: '2024-01-20',
    time: '14:30',
    duration: 60,
    status: 'pending',
    amount: 120,
    notes: 'Annual checkup and vaccinations',
    address: '456 Pine Avenue, San Francisco, CA'
  },
  {
    id: 'BK003',
    petOwner: 'Emily Davis',
    petOwnerEmail: '<EMAIL>',
    petOwnerPhone: '+****************',
    petName: 'Luna',
    petType: 'Persian Cat',
    service: 'Basic Grooming',
    date: '2024-01-21',
    time: '09:00',
    duration: 90,
    status: 'confirmed',
    amount: 65,
    notes: 'Luna needs special shampoo for sensitive skin',
    address: '789 Elm Drive, San Francisco, CA'
  },
  {
    id: 'BK004',
    petOwner: 'David Brown',
    petOwnerEmail: '<EMAIL>',
    petOwnerPhone: '+****************',
    petName: 'Charlie',
    petType: 'Beagle',
    service: 'Dental Cleaning',
    date: '2024-01-19',
    time: '11:00',
    duration: 90,
    status: 'completed',
    amount: 150,
    address: '321 Maple Court, San Francisco, CA'
  }
];

export default function BookingsPage() {
  const { user } = useAuth();
  const [bookings, setBookings] = useState(mockBookings);
  const [selectedDate, setSelectedDate] = useState('2024-01-20');
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list');

  if (!user || user.role !== 'provider') {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-cool-800 mb-4">Access Denied</h1>
          <p className="text-cool-600">You need to be logged in as a service provider to access this page.</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending_provider_approval': return 'bg-orange-100 text-orange-700';
      case 'confirmed_awaiting_payment': return 'bg-blue-100 text-blue-700';
      case 'confirmed': return 'bg-green-100 text-green-700';
      case 'completed': return 'bg-emerald-100 text-emerald-700';
      case 'cancelled': return 'bg-red-100 text-red-700';
      case 'no-show': return 'bg-gray-100 text-gray-700';
      // Legacy statuses
      case 'pending': return 'bg-yellow-100 text-yellow-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending_provider_approval': return <AlertCircle className="w-4 h-4" />;
      case 'confirmed_awaiting_payment': return <Clock className="w-4 h-4" />;
      case 'confirmed': return <CheckCircle className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'cancelled': return <XCircle className="w-4 h-4" />;
      case 'no-show': return <XCircle className="w-4 h-4" />;
      // Legacy statuses
      case 'pending': return <AlertCircle className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending_provider_approval': return 'Needs Approval';
      case 'confirmed_awaiting_payment': return 'Awaiting Payment';
      case 'confirmed': return 'Confirmed';
      case 'completed': return 'Completed';
      case 'cancelled': return 'Cancelled';
      case 'no-show': return 'No Show';
      case 'pending': return 'Pending';
      default: return status;
    }
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;
    const matchesSearch = booking.petOwner.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         booking.petName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         booking.service.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesDate = viewMode === 'calendar' ? booking.date === selectedDate : true;
    
    return matchesStatus && matchesSearch && matchesDate;
  });

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-cool-800 mb-2">Booking Management</h1>
            <p className="text-cool-600">Manage your appointments and schedule</p>
          </div>
          
          <div className="flex items-center gap-4">
            <button className="btn-secondary">
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
            <button className="btn-primary">
              <Plus className="w-4 h-4 mr-2" />
              New Booking
            </button>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="glass-card rounded-2xl p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              {/* View Mode Toggle */}
              <div className="flex bg-white/50 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-4 py-2 rounded-md font-medium transition-all duration-200 ${
                    viewMode === 'list' ? 'bg-white text-primary-600 shadow-sm' : 'text-cool-600'
                  }`}
                >
                  List View
                </button>
                <button
                  onClick={() => setViewMode('calendar')}
                  className={`px-4 py-2 rounded-md font-medium transition-all duration-200 ${
                    viewMode === 'calendar' ? 'bg-white text-primary-600 shadow-sm' : 'text-cool-600'
                  }`}
                >
                  Calendar View
                </button>
              </div>

              {/* Date Picker */}
              {viewMode === 'calendar' && (
                <div className="flex items-center gap-2">
                  <button className="p-2 hover:bg-white/50 rounded-lg transition-colors duration-200">
                    <ChevronLeft className="w-4 h-4 text-cool-600" />
                  </button>
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    className="px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                  <button className="p-2 hover:bg-white/50 rounded-lg transition-colors duration-200">
                    <ChevronRight className="w-4 h-4 text-cool-600" />
                  </button>
                </div>
              )}
            </div>

            <div className="flex items-center gap-4">
              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
                <option value="no-show">No Show</option>
              </select>

              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-cool-500" />
                <input
                  type="text"
                  placeholder="Search bookings..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Bookings Content */}
        {viewMode === 'list' ? (
          <div className="glass-card rounded-2xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Booking ID</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Customer & Pet</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Service</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Date & Time</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Status</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Amount</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-white/20">
                  {filteredBookings.map((booking) => (
                    <tr key={booking.id} className="hover:bg-white/30 transition-colors duration-200">
                      <td className="px-6 py-4">
                        <span className="font-mono text-sm text-cool-700">{booking.id}</span>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <p className="font-medium text-cool-800">{booking.petOwner}</p>
                          <p className="text-sm text-cool-600">{booking.petName} ({booking.petType})</p>
                          <p className="text-xs text-cool-500">{booking.petOwnerPhone}</p>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <p className="font-medium text-cool-800">{booking.service}</p>
                          <p className="text-sm text-cool-600">{booking.duration} minutes</p>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <p className="font-medium text-cool-800">{booking.date}</p>
                          <p className="text-sm text-cool-600">{booking.time}</p>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium w-fit ${getStatusColor(booking.status)}`}>
                          {getStatusIcon(booking.status)}
                          <span>{getStatusText(booking.status)}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <p className="font-bold text-cool-800">${booking.amount}</p>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex gap-2">
                          <button className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg transition-colors duration-200">
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200">
                            <Edit3 className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-accent-600 hover:bg-accent-100 rounded-lg transition-colors duration-200">
                            <MessageSquare className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="glass-card rounded-2xl p-6">
            <h3 className="text-lg font-bold text-cool-800 mb-4">
              Appointments for {selectedDate}
            </h3>
            <div className="space-y-4">
              {filteredBookings.length > 0 ? (
                filteredBookings.map((booking) => (
                  <div key={booking.id} className="bg-white/50 rounded-xl p-4 border-l-4 border-primary-500">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="text-center">
                          <p className="text-lg font-bold text-cool-800">{booking.time}</p>
                          <p className="text-sm text-cool-600">{booking.duration}min</p>
                        </div>
                        <div>
                          <p className="font-medium text-cool-800">{booking.service}</p>
                          <p className="text-sm text-cool-600">{booking.petOwner} - {booking.petName}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                          {getStatusIcon(booking.status)}
                          <span className="capitalize">{booking.status}</span>
                        </div>
                        <span className="font-bold text-cool-800">${booking.amount}</span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Calendar className="w-16 h-16 text-cool-400 mx-auto mb-4" />
                  <p className="text-cool-600">No appointments scheduled for this date</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
