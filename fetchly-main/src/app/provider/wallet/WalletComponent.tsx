'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { doc, onSnapshot, collection, query, where, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { stripeService, StripeServiceError } from '@/lib/services/stripe';
import { LoadingSpinner, PageLoader } from '@/components/LoadingSpinner';
import { format } from 'date-fns';
import { limit } from 'firebase/firestore';

interface StripeAccount {
  id: string;
  isConnected: boolean;
  email?: string;
  detailsSubmitted?: boolean;
  payoutsEnabled?: boolean;
  chargesEnabled?: boolean;
  requirements?: {
    currently_due: string[];
    eventually_due: string[];
    past_due: string[];
  };
  payouts_enabled?: boolean;
  dashboardUrl?: string;
}

interface Balance {
  available: number;
  pending: number;
  currency: string;
}

interface Transaction {
  id: string;
  amount: number;
  currency: string;
  description: string;
  created: number;
  type: 'payment' | 'payout' | 'refund' | 'charge';
  status: 'succeeded' | 'pending' | 'failed' | 'in_transit' | 'paid';
}

const WalletComponent = () => {
  const { user } = useAuth();
  const [stripeAccount, setStripeAccount] = useState<StripeAccount | null>(null);
  const [balance, setBalance] = useState<Balance | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [accountLoading, setAccountLoading] = useState(true);
  const [balanceLoading, setBalanceLoading] = useState(false);
  const [transactionsLoading, setTransactionsLoading] = useState(false);

  // Fetch Stripe account info and transactions
  useEffect(() => {
    if (!user?.id) return;

    setAccountLoading(true);
    setTransactionsLoading(true);
    
    // Subscribe to account changes
    const accountUnsubscribe = onSnapshot(
      doc(db, 'stripeAccounts', user.id),
      (doc) => {
        if (doc.exists()) {
          const accountData = doc.data() as StripeAccount;
          setStripeAccount(accountData);
          
          // If account is connected, fetch balance and transactions
          if (accountData.isConnected) {
            fetchBalance();
            fetchTransactions();
          }
        } else {
          setStripeAccount({
            id: '',
            isConnected: false,
          });
        }
        setAccountLoading(false);
      },
      (error) => {
        console.error('Error fetching Stripe account:', error);
        setError('Failed to load account information');
        setAccountLoading(false);
      }
    );

    // Subscribe to transactions
    const transactionsQuery = query(
      collection(db, 'transactions'),
      where('userId', '==', user.id),
      orderBy('created', 'desc'),
      limit(50)
    );
    
    const transactionsUnsubscribe = onSnapshot(
      transactionsQuery,
      (snapshot) => {
        const transactionsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Transaction[];
        setTransactions(transactionsData);
        setTransactionsLoading(false);
      },
      (error) => {
        console.error('Error fetching transactions:', error);
        setError('Failed to load transaction history');
        setTransactionsLoading(false);
      }
    );

    return () => {
      accountUnsubscribe();
      transactionsUnsubscribe();
    };
  }, [user?.id]);

  // Fetch account balance
  const fetchBalance = async () => {
    if (!stripeAccount?.isConnected) return;
    
    try {
      setBalanceLoading(true);
      const balanceData = await stripeService.getBalance();
      setBalance(balanceData);
      setError(null);
    } catch (err) {
      console.error('Error fetching balance:', err);
      setError(
        err instanceof Error 
          ? err.message 
          : 'Failed to load balance information. Please try again later.'
      );
    } finally {
      setBalanceLoading(false);
    }
  };

  // Fetch transaction history
  const fetchTransactions = async () => {
    if (!user?.id) return;
    
    try {
      setTransactionsLoading(true);
      // Transactions are now handled by the real-time listener
      // This function is kept for consistency and future use
    } catch (err) {
      console.error('Error fetching transactions:', err);
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to load transaction history. Please try again later.'
      );
    } finally {
      setTransactionsLoading(false);
    }
  };

  // Fetch balance when account is connected
  useEffect(() => {
    if (stripeAccount?.isConnected) {
      fetchBalance();
    }
  }, [stripeAccount?.isConnected]);

  // Open Stripe dashboard
  const openStripeDashboard = async () => {
    try {
      const url = await stripeService.getDashboardLink();
      window.open(url, '_blank');
      setError(null);
    } catch (err) {
      console.error('Error opening Stripe dashboard:', err);
      setError(
        err instanceof Error 
          ? err.message 
          : 'Failed to open Stripe dashboard. Please try again later.'
      );
    }
  };

  // Handle Stripe connection
  const handleConnectStripe = async () => {
    if (!user?.id || !user?.email) {
      setError('User information is missing. Please sign in again.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/stripe/onboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          email: user.email,
          returnUrl: `${window.location.origin}/provider/wallet`,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.url) {
        throw new Error('No URL returned from the server');
      }
      
      window.location.href = data.url;
    } catch (err) {
      console.error('Error connecting to Stripe:', err);
      setError(
        err instanceof Error 
          ? `Failed to connect to Stripe: ${err.message}` 
          : 'Failed to connect to Stripe. Please try again.'
      );
      setIsLoading(false);
    }
  };

  // Show loading state
  if (accountLoading) {
    return <PageLoader />;
  }

  // Show error state
  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Your Wallet</h1>
      
      {!stripeAccount?.isConnected ? (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <div className="mx-auto h-12 w-12 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="mt-4 text-lg font-medium text-gray-900">Connect your Stripe account</h2>
          <p className="mt-1 text-sm text-gray-500">Start receiving payments by connecting your Stripe account.</p>
          <div className="mt-6">
            <button
              onClick={handleConnectStripe}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Connecting...' : 'Connect with Stripe'}
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Account Overview</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Status</p>
                <div className="flex items-center mt-1">
                  <span className={`h-2 w-2 rounded-full mr-2 ${stripeAccount?.isConnected ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
                  <span className="text-sm text-gray-900">
                    {stripeAccount?.isConnected ? 'Connected' : 'Pending'}
                  </span>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Stripe Account ID</p>
                <p className="mt-1 text-sm text-gray-900 font-mono">
                  {stripeAccount?.id ? `••••${stripeAccount.id.slice(-4)}` : 'N/A'}
                </p>
              </div>
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-md font-medium text-gray-900 mb-3">Account Balance</h3>
              {balanceLoading ? (
                <div className="flex items-center">
                  <LoadingSpinner size="sm" />
                  <span className="ml-2 text-sm text-gray-500">Loading balance...</span>
                </div>
              ) : balance ? (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Available:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {stripeService.formatCurrency(balance.available, balance.currency)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Pending:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {stripeService.formatCurrency(balance.pending, balance.currency)}
                    </span>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-gray-500">No balance information available</p>
              )}
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={openStripeDashboard}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Open Stripe Dashboard
              </button>
            </div>
          </div>
          
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Transactions</h2>
            <p className="text-sm text-gray-500">
              Your transaction history will appear here.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default WalletComponent;
