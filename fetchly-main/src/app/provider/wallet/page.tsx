'use client';

import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import ErrorBoundary from '@/components/ErrorBoundary';
import dynamic from 'next/dynamic';

// Initialize Stripe with your publishable key
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

// Dynamically import the wallet component with no SSR
const SimpleWallet = dynamic(() => import('./SimpleWallet'), {
  ssr: false,
  loading: () => <div className="flex justify-center items-center min-h-[400px]">Loading wallet...</div>
});

const WalletPage = () => {
  return (
    <ErrorBoundary>
      <Elements stripe={stripePromise}>
        <SimpleWallet />
      </Elements>
    </ErrorBoundary>
  );
};

export default WalletPage;
