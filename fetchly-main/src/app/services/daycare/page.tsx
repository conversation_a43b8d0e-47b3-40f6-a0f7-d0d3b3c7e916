'use client';

import { useState } from 'react';
import { Users, Star, MapPin, Clock, DollarSign, CheckCircle, Calendar, Play, Shield } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const daycarePackages = [
  {
    title: "Half Day Care",
    price: "$25-35/day",
    duration: "4 hours",
    includes: ["Supervised playtime", "Snack included", "Basic exercise", "Socialization"],
    popular: false
  },
  {
    title: "Full Day Care",
    price: "$45-65/day",
    duration: "8 hours",
    includes: ["All day supervision", "2 meals included", "Multiple play sessions", "Rest periods", "Daily report"],
    popular: true
  },
  {
    title: "Premium Day Care",
    price: "$65-85/day",
    duration: "10 hours",
    includes: ["Extended hours", "Premium meals", "Training activities", "Grooming touch-ups", "Photo updates", "One-on-one time"],
    popular: false
  }
];

const providers = [
  {
    id: 1,
    name: "Happy Paws Daycare",
    rating: 4.9,
    reviews: 156,
    distance: "0.7 miles",
    price: "$35-75/day",
    features: ["Indoor/Outdoor Play", "Small Groups", "Trained Staff", "Live Updates"],
    availability: "Spots Available",
    image: "/api/placeholder/300/200"
  },
  {
    id: 2,
    name: "Playful Pups Center",
    rating: 4.8,
    reviews: 203,
    distance: "1.3 miles",
    price: "$40-80/day",
    features: ["Swimming Pool", "Agility Course", "Nap Rooms", "Pickup Service"],
    availability: "Waitlist Available",
    image: "/api/placeholder/300/200"
  },
  {
    id: 3,
    name: "Furry Friends Daycare",
    rating: 4.7,
    reviews: 89,
    distance: "2.1 miles",
    price: "$30-60/day",
    features: ["Home-like Setting", "Senior Pet Care", "Medical Support", "Flexible Hours"],
    availability: "Open Enrollment",
    image: "/api/placeholder/300/200"
  }
];

export default function DaycarePage() {
  const [selectedDays, setSelectedDays] = useState('');
  const [location, setLocation] = useState('');

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-secondary-500/5 to-accent-500/10"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-primary-400/20 to-cool-400/20 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }}></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Users className="w-12 h-12 text-primary-500" />
              <h1 className="text-4xl md:text-6xl font-bold text-cool-800">
                Pet <span className="text-gradient">Daycare</span>
              </h1>
            </div>
            <p className="text-xl md:text-2xl text-cool-600 max-w-3xl mx-auto mb-8">
              Safe, fun, and social environment for your pet while you're away
            </p>
            
            {/* Quick Search */}
            <div className="glass-card rounded-2xl p-6 max-w-2xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Days Needed</label>
                  <select
                    value={selectedDays}
                    onChange={(e) => setSelectedDays(e.target.value)}
                    className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                  >
                    <option value="">Select days</option>
                    <option value="1-2">1-2 days/week</option>
                    <option value="3-4">3-4 days/week</option>
                    <option value="5">5 days/week</option>
                    <option value="occasional">Occasional</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Location</label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
                    <input
                      type="text"
                      placeholder="Enter location"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    />
                  </div>
                </div>
                <div className="flex items-end">
                  <Link href="/search?service=daycare" className="btn-primary w-full text-center">
                    Find Daycare
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Daycare Options */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Daycare Options
            </h2>
            <p className="text-xl text-cool-600">
              Flexible care options to fit your schedule and budget
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {daycarePackages.map((package_, index) => (
              <div key={index} className={`glass-card rounded-2xl p-6 hover:shadow-xl transition-all duration-300 relative ${package_.popular ? 'border-2 border-primary-500' : ''}`}>
                {package_.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-cool-800 mb-2">{package_.title}</h3>
                  <div className="text-3xl font-bold text-gradient mb-2">{package_.price}</div>
                  <div className="flex items-center justify-center gap-2 text-cool-600">
                    <Clock className="w-4 h-4" />
                    <span>{package_.duration}</span>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  {package_.includes.map((item, idx) => (
                    <div key={idx} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-500 flex-shrink-0" />
                      <span className="text-cool-700">{item}</span>
                    </div>
                  ))}
                </div>

                <Link 
                  href={`/search?service=daycare&type=${package_.title.toLowerCase().replace(/\s+/g, '-')}`}
                  className={package_.popular ? 'btn-primary w-full text-center' : 'btn-secondary w-full text-center'}
                >
                  Enroll Now
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Daycare Centers */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Top Daycare Centers
            </h2>
            <p className="text-xl text-cool-600">
              Trusted facilities where your pet will play, learn, and socialize
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {providers.map((provider) => (
              <div key={provider.id} className="glass-card rounded-2xl overflow-hidden hover:shadow-xl transition-all duration-300 group">
                <div className="relative h-48 bg-gradient-to-br from-primary-100 to-secondary-100">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div className="absolute top-4 right-4">
                    <button className="p-2 bg-white/90 rounded-full hover:bg-white transition-all duration-300">
                      <Image
                        src="/fetchlylogo.png"
                        alt="Fetchly Logo"
                        width={20}
                        height={20}
                        className="w-5 h-5"
                      />
                    </button>
                  </div>
                  <div className="absolute bottom-4 left-4">
                    <span className="px-3 py-1 bg-primary-500 text-white text-sm font-medium rounded-full">
                      Pet Daycare
                    </span>
                  </div>
                </div>

                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-xl font-bold text-cool-800 group-hover:text-primary-500 transition-colors duration-300">
                      {provider.name}
                    </h3>
                    <div className="text-right">
                      <div className="flex items-center text-sm text-cool-600 mb-1">
                        <Star className="w-4 h-4 text-yellow-500 mr-1" />
                        {provider.rating} ({provider.reviews})
                      </div>
                      <div className="text-sm text-cool-600">{provider.distance}</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 mb-4 text-sm text-cool-600">
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1" />
                      {provider.price}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {provider.availability}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {provider.features.slice(0, 3).map((feature, index) => (
                      <span key={index} className="px-2 py-1 bg-white/50 text-cool-600 text-xs rounded-full">
                        {feature}
                      </span>
                    ))}
                  </div>

                  <div className="flex gap-2">
                    <Link href={`/provider/${provider.id}`} className="btn-secondary flex-1 text-center">
                      Tour Facility
                    </Link>
                    <Link href={`/booking?provider=${provider.id}&service=daycare`} className="btn-primary flex-1 text-center">
                      Enroll Now
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link href="/search?service=daycare" className="btn-secondary">
              View All Daycare Centers
            </Link>
          </div>
        </div>
      </section>

      {/* Benefits of Pet Daycare */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
                Why Choose Pet Daycare?
              </h2>
              <p className="text-xl text-cool-600">
                More than just pet sitting - it's about enrichment and socialization
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Socialization</h3>
                <p className="text-cool-600 text-sm">Healthy interaction with other pets and people</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-secondary-500 to-secondary-600 flex items-center justify-center">
                  <Play className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Exercise & Play</h3>
                <p className="text-cool-600 text-sm">Structured activities to keep pets active and engaged</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 flex items-center justify-center">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Safe Environment</h3>
                <p className="text-cool-600 text-sm">Supervised play in a secure, controlled setting</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-warm-500 to-warm-600 flex items-center justify-center">
                  <Image
                    src="/favicon.png"
                    alt="Fetchly Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Peace of Mind</h3>
                <p className="text-cool-600 text-sm">Know your pet is happy and cared for while you work</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enrollment Process */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4 text-cool-800">
                How to Enroll Your Pet
              </h2>
              <p className="text-xl text-cool-600">
                Simple steps to get your pet started in daycare
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 text-white flex items-center justify-center font-bold text-lg">
                  1
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Schedule Tour</h3>
                <p className="text-cool-600 text-sm">Visit the facility and meet the staff</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-secondary-500 to-secondary-600 text-white flex items-center justify-center font-bold text-lg">
                  2
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Assessment</h3>
                <p className="text-cool-600 text-sm">Pet temperament and compatibility evaluation</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 text-white flex items-center justify-center font-bold text-lg">
                  3
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Trial Day</h3>
                <p className="text-cool-600 text-sm">Half-day trial to ensure good fit</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-warm-500 to-warm-600 text-white flex items-center justify-center font-bold text-lg">
                  4
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Start Care</h3>
                <p className="text-cool-600 text-sm">Begin regular daycare schedule</p>
              </div>
            </div>

            <div className="text-center mt-8">
              <Link href="/help/daycare-enrollment" className="btn-primary">
                Complete Enrollment Guide
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
