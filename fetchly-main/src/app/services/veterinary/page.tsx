'use client';

import { useState } from 'react';
import { Stethoscope, Star, MapPin, Clock, DollarSign, CheckCircle, Calendar, Shield, Activity } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const veterinaryServices = [
  {
    title: "Wellness Exam",
    price: "$65-85",
    duration: "30-45 min",
    includes: ["Physical Examination", "Vaccination Updates", "Health Consultation", "Basic Lab Work"],
    popular: true
  },
  {
    title: "Emergency Care",
    price: "$150-300",
    duration: "1-3 hours",
    includes: ["Urgent Assessment", "Emergency Treatment", "Pain Management", "Follow-up Care"],
    popular: false
  },
  {
    title: "Surgical Procedures",
    price: "$300-1500",
    duration: "2-6 hours",
    includes: ["Pre-surgical Exam", "Anesthesia", "Surgery", "Post-op Monitoring", "Recovery Care"],
    popular: false
  }
];

const providers = [
  {
    id: 1,
    name: "City Animal Hospital",
    rating: 4.9,
    reviews: 234,
    distance: "0.5 miles",
    price: "$65-200",
    specialties: ["Emergency Care", "Surgery", "Internal Medicine"],
    availability: "Open 24/7",
    image: "/api/placeholder/300/200"
  },
  {
    id: 2,
    name: "Compassionate Care Veterinary",
    rating: 4.8,
    reviews: 189,
    distance: "1.1 miles",
    price: "$55-180",
    specialties: ["Wellness Care", "Dentistry", "Senior Pet Care"],
    availability: "Same Day Appointments",
    image: "/api/placeholder/300/200"
  },
  {
    id: 3,
    name: "Advanced Pet Medical Center",
    rating: 4.7,
    reviews: 156,
    distance: "2.3 miles",
    price: "$70-250",
    specialties: ["Cardiology", "Oncology", "Orthopedics"],
    availability: "Specialist Referrals",
    image: "/api/placeholder/300/200"
  }
];

export default function VeterinaryPage() {
  const [selectedService, setSelectedService] = useState('');
  const [location, setLocation] = useState('');

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-secondary-500/5 to-accent-500/10"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-primary-400/20 to-cool-400/20 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }}></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Stethoscope className="w-12 h-12 text-primary-500" />
              <h1 className="text-4xl md:text-6xl font-bold text-cool-800">
                Veterinary <span className="text-gradient">Care</span>
              </h1>
            </div>
            <p className="text-xl md:text-2xl text-cool-600 max-w-3xl mx-auto mb-8">
              Comprehensive medical care from trusted veterinary professionals
            </p>
            
            {/* Quick Search */}
            <div className="glass-card rounded-2xl p-6 max-w-2xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Service Type</label>
                  <select
                    value={selectedService}
                    onChange={(e) => setSelectedService(e.target.value)}
                    className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                  >
                    <option value="">Select service</option>
                    <option value="wellness">Wellness Exam</option>
                    <option value="emergency">Emergency Care</option>
                    <option value="surgery">Surgery</option>
                    <option value="dental">Dental Care</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Location</label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
                    <input
                      type="text"
                      placeholder="Enter location"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    />
                  </div>
                </div>
                <div className="flex items-end">
                  <Link href="/search?service=veterinary" className="btn-primary w-full text-center">
                    Find Vets
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Types */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Veterinary Services
            </h2>
            <p className="text-xl text-cool-600">
              Comprehensive care for every stage of your pet's life
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {veterinaryServices.map((service, index) => (
              <div key={index} className={`glass-card rounded-2xl p-6 hover:shadow-xl transition-all duration-300 relative ${service.popular ? 'border-2 border-primary-500' : ''}`}>
                {service.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                      Most Common
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-cool-800 mb-2">{service.title}</h3>
                  <div className="text-3xl font-bold text-gradient mb-2">{service.price}</div>
                  <div className="flex items-center justify-center gap-2 text-cool-600">
                    <Clock className="w-4 h-4" />
                    <span>{service.duration}</span>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  {service.includes.map((item, idx) => (
                    <div key={idx} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-500 flex-shrink-0" />
                      <span className="text-cool-700">{item}</span>
                    </div>
                  ))}
                </div>

                <Link 
                  href={`/search?service=veterinary&type=${service.title.toLowerCase().replace(/\s+/g, '-')}`}
                  className={service.popular ? 'btn-primary w-full text-center' : 'btn-secondary w-full text-center'}
                >
                  Book Appointment
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Providers */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Trusted Veterinarians
            </h2>
            <p className="text-xl text-cool-600">
              Experienced professionals dedicated to your pet's health
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {providers.map((provider) => (
              <div key={provider.id} className="glass-card rounded-2xl overflow-hidden hover:shadow-xl transition-all duration-300 group">
                <div className="relative h-48 bg-gradient-to-br from-primary-100 to-secondary-100">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div className="absolute top-4 right-4">
                    <button className="p-2 bg-white/90 rounded-full hover:bg-white transition-all duration-300">
                      <Image
                        src="/fetchlylogo.png"
                        alt="Fetchly Logo"
                        width={20}
                        height={20}
                        className="w-5 h-5"
                      />
                    </button>
                  </div>
                  <div className="absolute bottom-4 left-4">
                    <span className="px-3 py-1 bg-primary-500 text-white text-sm font-medium rounded-full">
                      Veterinary Clinic
                    </span>
                  </div>
                </div>

                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-xl font-bold text-cool-800 group-hover:text-primary-500 transition-colors duration-300">
                      {provider.name}
                    </h3>
                    <div className="text-right">
                      <div className="flex items-center text-sm text-cool-600 mb-1">
                        <Star className="w-4 h-4 text-yellow-500 mr-1" />
                        {provider.rating} ({provider.reviews})
                      </div>
                      <div className="text-sm text-cool-600">{provider.distance}</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 mb-4 text-sm text-cool-600">
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1" />
                      {provider.price}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {provider.availability}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {provider.specialties.slice(0, 3).map((specialty, index) => (
                      <span key={index} className="px-2 py-1 bg-white/50 text-cool-600 text-xs rounded-full">
                        {specialty}
                      </span>
                    ))}
                  </div>

                  <div className="flex gap-2">
                    <Link href={`/provider/${provider.id}`} className="btn-secondary flex-1 text-center">
                      View Profile
                    </Link>
                    <Link href={`/booking?provider=${provider.id}&service=veterinary`} className="btn-primary flex-1 text-center">
                      Book Now
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link href="/search?service=veterinary" className="btn-secondary">
              View All Veterinarians
            </Link>
          </div>
        </div>
      </section>

      {/* Why Choose Professional Veterinary Care */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
                Why Regular Veterinary Care?
              </h2>
              <p className="text-xl text-cool-600">
                Preventive care is the key to a long, healthy life for your pet
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Prevention</h3>
                <p className="text-cool-600 text-sm">Early detection and prevention of health issues</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-secondary-500 to-secondary-600 flex items-center justify-center">
                  <Activity className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Wellness</h3>
                <p className="text-cool-600 text-sm">Comprehensive health monitoring and care</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 flex items-center justify-center">
                  <Stethoscope className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Expertise</h3>
                <p className="text-cool-600 text-sm">Professional medical knowledge and experience</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-warm-500 to-warm-600 flex items-center justify-center">
                  <Image
                    src="/favicon.png"
                    alt="Fetchly Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Peace of Mind</h3>
                <p className="text-cool-600 text-sm">Confidence in your pet's health and wellbeing</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Emergency Banner */}
      <section className="py-8 bg-gradient-to-r from-warm-500/10 to-warm-600/10">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-2xl p-6 text-center">
            <h3 className="text-2xl font-bold text-cool-800 mb-2">Pet Emergency?</h3>
            <p className="text-cool-600 mb-4">Don't wait - get immediate veterinary care</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/emergency" className="btn-primary">
                Find Emergency Care
              </Link>
              <a href="tel:1-800-PET-HELP" className="btn-secondary">
                Call Emergency Hotline
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
