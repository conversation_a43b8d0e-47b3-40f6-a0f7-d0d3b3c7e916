@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Nunito:wght@300;400;500;600;700;800&display=swap');
@import "tailwindcss";

/*
  Fetchly Visual Redesign - Frosted Glass & Mint-Blue Aesthetic
  ------------------------------------------------------------
  New design system with sophisticated wellness-focused aesthetic
  - Mint-blue gradient backgrounds
  - Frosted glass containers
  - Preserved brand logo colors
  - Enhanced accessibility and modern UI
*/
:root {
  /* Main Background Gradients */
  --background: linear-gradient(135deg, #F0FDF4 0%, #F0F9FF 100%);
  --background-alt: linear-gradient(135deg, #F0F9FF 0%, #F0FDF4 100%);

  /* Brand Logo Colors (Preserved) */
  --brand-fetch: #87CEEB; /* Sky Blue for "Fetch" */
  --brand-ly: #9CA3AF; /* Soft Gray for "ly" */

  /* Primary Color Palette */
  --primary: #06B6D4; /* Aqua */
  --primary-hover: #0891B2; /* Darker Aqua */
  --secondary: #0EA5E9; /* Sky Blue */
  --accent: #10B981; /* Soft Mint Green */
  --accent-light: #34D399; /* Light Mint */

  /* Text Colors */
  --foreground: #374151; /* Charcoal Gray */
  --text-secondary: #6B7280; /* Medium Gray */
  --text-muted: #9CA3AF; /* Light Gray */
  --text-white: #FFFFFF;

  /* Frosted Glass System */
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);

  /* Gradient Definitions */
  --gradient-cta: linear-gradient(135deg, #06B6D4 0%, #0EA5E9 100%);
  --gradient-cta-hover: linear-gradient(135deg, #0891B2 0%, #0284C7 100%);
  --gradient-footer: linear-gradient(135deg, #1E293B 0%, #0EA5E9 100%);

  /* Border Colors */
  --border-light: #E5E7EB;
  --border-medium: #D1D5DB;
  --border-dark: #9CA3AF;

  /* Legacy Support - Updated Values */
}

/* Dark Mode Variables */
.dark {
  /* Dark Mode Background Gradients */
  --background: linear-gradient(135deg, #0A1E2D 0%, #0A1E2D 100%);
  --background-alt: linear-gradient(135deg, #0A1E2D 0%, #1E90FF 100%);

  /* Dark Mode Primary Colors */
  --primary: #1E90FF; /* Electric Blue */
  --primary-hover: #00BFFF; /* Sky Blue */
  --secondary: #00BFFF; /* Sky Blue */
  --accent: #32CD32; /* Neon Green */
  --accent-light: #3EB489; /* Mint Green */

  /* Dark Mode Text Colors */
  --foreground: #F5F5F5; /* Off-White */
  --text-secondary: #D3D3D3; /* Light Gray */
  --text-muted: #708090; /* Slate Gray */
  --text-white: #F5F5F5;

  /* Dark Mode Glass System */
  --glass-bg: rgba(10, 30, 45, 0.9);
  --glass-border: rgba(30, 144, 255, 0.3);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);

  /* Dark Mode Gradients */
  --gradient-cta: linear-gradient(135deg, #1E90FF 0%, #00BFFF 100%);
  --gradient-cta-hover: linear-gradient(135deg, #00BFFF 0%, #32CD32 100%);
  --gradient-footer: linear-gradient(135deg, #0A1E2D 0%, #1E90FF 100%);

  /* Dark Mode Border Colors */
  --border-light: #708090;
  --border-medium: #D3D3D3;
  --border-dark: #F5F5F5;

  /* Dark Mode Specific Colors */
  --dark-navy: #0A1E2D;
  --dark-electric: #1E90FF;
  --dark-sky: #00BFFF;
  --dark-neon: #32CD32;
  --dark-mint: #3EB489;
  --dark-light-gray: #D3D3D3;
  --dark-off-white: #F5F5F5;
  --dark-slate: #708090;
}

/* Universal Dark Mode Classes */
.dark * {
  border-color: var(--dark-slate) !important;
}

.dark .bg-white {
  background-color: var(--dark-navy) !important;
}

.dark .bg-gray-50,
.dark .bg-gray-100,
.dark .bg-green-50,
.dark .bg-blue-50,
.dark .bg-purple-50,
.dark .bg-pink-50,
.dark .bg-yellow-50,
.dark .bg-red-50 {
  background-color: var(--dark-navy) !important;
}

.dark .bg-gray-200,
.dark .bg-gray-300 {
  background-color: var(--dark-slate) !important;
}

.dark .text-gray-900,
.dark .text-gray-800,
.dark .text-gray-700 {
  color: var(--dark-off-white) !important;
}

.dark .text-gray-600,
.dark .text-gray-500 {
  color: var(--dark-light-gray) !important;
}

.dark .text-gray-400,
.dark .text-gray-300 {
  color: var(--dark-slate) !important;
}

.dark .border-gray-200,
.dark .border-gray-300,
.dark .border-white {
  border-color: var(--dark-slate) !important;
}

.dark .shadow-lg,
.dark .shadow-xl,
.dark .shadow-2xl {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
}

/* Comprehensive Dark Mode Button Styles */
.dark .bg-green-600,
.dark .bg-green-500,
.dark .bg-blue-600,
.dark .bg-blue-500 {
  background-color: var(--dark-electric) !important;
}

.dark .bg-green-600:hover,
.dark .bg-green-500:hover,
.dark .bg-blue-600:hover,
.dark .bg-blue-500:hover {
  background-color: var(--dark-sky) !important;
}

/* Dark Mode Card and Container Styles */
.dark .bg-gradient-to-r,
.dark .bg-gradient-to-l,
.dark .bg-gradient-to-t,
.dark .bg-gradient-to-b,
.dark .bg-gradient-to-br,
.dark .bg-gradient-to-bl,
.dark .bg-gradient-to-tr,
.dark .bg-gradient-to-tl {
  background: linear-gradient(135deg, var(--dark-navy) 0%, var(--dark-electric) 100%) !important;
}

/* Dark Mode Input Styles */
.dark input,
.dark textarea,
.dark select {
  background-color: var(--dark-navy) !important;
  border-color: var(--dark-slate) !important;
  color: var(--dark-off-white) !important;
}

.dark input::placeholder,
.dark textarea::placeholder {
  color: var(--dark-slate) !important;
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
  border-color: var(--dark-electric) !important;
  box-shadow: 0 0 0 3px rgba(30, 144, 255, 0.1) !important;
}

/* Dark Mode Link Styles */
.dark a {
  color: var(--dark-light-gray) !important;
}

.dark a:hover {
  color: var(--dark-electric) !important;
}

/* Dark Mode Icon Colors */
.dark .text-green-600,
.dark .text-green-500,
.dark .text-blue-600,
.dark .text-blue-500 {
  color: var(--dark-electric) !important;
}

.dark .text-yellow-300,
.dark .text-yellow-400 {
  color: var(--dark-neon) !important;
}

/* Dark Mode Success/Error States */
.dark .text-green-100,
.dark .text-green-200 {
  color: var(--dark-light-gray) !important;
}

.dark .text-red-500,
.dark .text-red-600 {
  color: #ff6b6b !important;
}

/* Dark Mode Hover Effects */
.dark .hover\:bg-green-50:hover,
.dark .hover\:bg-blue-50:hover,
.dark .hover\:bg-gray-50:hover {
  background-color: rgba(30, 144, 255, 0.1) !important;
}

/* Dark Mode Modal and Dropdown Styles */
.dark .backdrop-blur-sm,
.dark .backdrop-blur-md,
.dark .backdrop-blur-lg {
  background-color: rgba(10, 30, 45, 0.8) !important;
}

/* Dark Mode Badge and Tag Styles */
.dark .bg-yellow-100,
.dark .bg-green-100,
.dark .bg-blue-100,
.dark .bg-purple-100,
.dark .bg-pink-100 {
  background-color: rgba(30, 144, 255, 0.2) !important;
}

.dark .text-yellow-800,
.dark .text-green-800,
.dark .text-blue-800,
.dark .text-purple-800,
.dark .text-pink-800 {
  color: var(--dark-off-white) !important;
}

/* Dark Mode Table Styles */
.dark table {
  background-color: var(--dark-navy) !important;
}

.dark th {
  background-color: var(--dark-slate) !important;
  color: var(--dark-off-white) !important;
  border-color: var(--dark-slate) !important;
}

.dark td {
  border-color: var(--dark-slate) !important;
  color: var(--dark-light-gray) !important;
}

.dark tr:hover {
  background-color: rgba(30, 144, 255, 0.1) !important;
}

/* Dark Mode Navigation Styles */
.dark nav {
  background-color: var(--dark-navy) !important;
  border-color: var(--dark-slate) !important;
}

/* Dark Mode Progress and Loading Styles */
.dark .bg-gray-200 {
  background-color: var(--dark-slate) !important;
}

.dark .animate-pulse {
  background-color: rgba(30, 144, 255, 0.1) !important;
}

/* Dark Mode Scrollbar */
.dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: var(--dark-navy);
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--dark-slate);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--dark-electric);
}

body {
  background: var(--background);
  background-size: 400% 400%;
  animation: gradient 20s ease infinite;
  color: var(--foreground);
  font-family: var(--font-sans);
  min-height: 100vh;
  background-attachment: fixed;
  /* Mobile optimizations */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* Safe area support for iOS */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Fetchly Frosted Glass Design System */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: 1rem;
  transition: all 0.3s ease;
}

.glass-modal {
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 25px 50px rgba(31, 38, 135, 0.25);
  border-radius: 1.5rem;
}

.glass-button {
  background: var(--gradient-cta);
  border: none;
  border-radius: 0.75rem;
  color: var(--text-white);
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
  cursor: pointer;
}

.glass-button:hover {
  background: var(--gradient-cta-hover);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
}

.glass-button:active {
  transform: translateY(0) scale(1.02);
}

/* Glass Card Hover Effect */
.glass-card:hover {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 25px 50px rgba(31, 38, 135, 0.2);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

.dark .glass-card:hover {
  background: rgba(10, 30, 45, 0.95);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.6);
  border-color: rgba(30, 144, 255, 0.4);
}

/* Enhanced Button Styles */
.btn-primary {
  background: var(--gradient-cta);
  background-size: 200% 200%;
  color: var(--text-white);
  padding: 0.875rem 2rem;
  border-radius: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  color: var(--foreground);
  border: 1px solid var(--border-light);
  padding: 0.875rem 2rem;
  border-radius: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.dark .btn-secondary {
  background: rgba(10, 30, 45, 0.8);
  color: var(--dark-off-white);
  border-color: var(--dark-electric);
}

.dark .btn-secondary:hover {
  background: rgba(30, 144, 255, 0.2);
  border-color: var(--dark-sky);
}

/* Text Utilities */
.text-primary {
  color: var(--foreground);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-muted);
}

/* Background Utilities */
.bg-gradient-main {
  background: var(--background);
}

.bg-gradient-alt {
  background: var(--background-alt);
}

.bg-gradient-footer {
  background: var(--gradient-footer);
}

/* Enhanced Button Layout */
.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  background: var(--gradient-cta-hover);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(31, 38, 135, 0.15);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-secondary {
  background: rgba(248, 250, 252, 0.9); /* Light gray background */
  color: #1e293b; /* Dark slate text */
  padding: 0.875rem 2rem;
  border-radius: 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid rgba(14, 165, 233, 0.3); /* Fetchly blue border */
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.1) 0%, rgba(100, 116, 139, 0.1) 100%);
  border-color: rgba(14, 165, 233, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(14, 165, 233, 0.2);
}

.text-gradient {
  background: linear-gradient(135deg, #0ea5e9 0%, #64748b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
}

.text-gradient {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes pawWalk {
  0% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  25% {
    transform: translate(25vw, 10vh) rotate(15deg);
    opacity: 0.8;
  }
  50% {
    transform: translate(50vw, -5vh) rotate(-10deg);
    opacity: 0.6;
  }
  75% {
    transform: translate(75vw, 15vh) rotate(20deg);
    opacity: 0.4;
  }
  90% {
    opacity: 0.2;
  }
  100% {
    transform: translate(100vw, 5vh) rotate(0deg);
    opacity: 0;
  }
}

.animate-pawWalk {
  animation: pawWalk 12s linear infinite;
}

/* Mobile-specific optimizations for Capacitor */
@media (max-width: 768px) {
  /* Touch-friendly button sizes */
  .btn-primary, .btn-secondary {
    min-height: 44px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  /* Larger touch targets */
  button, .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved spacing for mobile */
  .glass-card {
    padding: 1rem;
    margin: 0.5rem;
  }

  /* Mobile-optimized text sizes */
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }

  /* Prevent zoom on input focus */
  input, select, textarea {
    font-size: 16px;
  }
}

/* iOS specific optimizations */
@supports (-webkit-touch-callout: none) {
  .ios-safe-area {
    padding-top: max(env(safe-area-inset-top), 20px);
    padding-bottom: max(env(safe-area-inset-bottom), 20px);
  }
}

/* Android specific optimizations */
@media (max-width: 768px) and (orientation: portrait) {
  .android-keyboard-adjust {
    height: calc(100vh - env(keyboard-inset-height, 0px));
  }
}

/* Smooth scrolling for mobile */
* {
  -webkit-overflow-scrolling: touch;
}

/* Disable text selection on interactive elements */
button, .btn-primary, .btn-secondary, .clickable {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Provider Dashboard Sidebar/Tab Button Styles */
.sidebar-selected {
  background: var(--sidebar-selected-bg) !important;
  color: var(--sidebar-selected-text) !important;
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.12);
  transform: scale(1.05);
  font-weight: 600;
}
.sidebar-unselected {
  background: transparent;
  color: #1e293b;
  transition: background 0.2s, color 0.2s;
}
.sidebar-unselected:hover {
  background: var(--sidebar-hover-bg);
  color: var(--sidebar-hover-text);
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.08);
}

/* Scrollbar Hide Utility */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
