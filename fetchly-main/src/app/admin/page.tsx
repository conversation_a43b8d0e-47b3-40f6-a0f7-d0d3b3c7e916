'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdmin } from '@/contexts/AdminContext';
import { Loader2 } from 'lucide-react';

export default function AdminPage() {
  const { admin, loading } = useAdmin();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (admin) {
        // Already logged in, redirect to dashboard
        router.push('/admin/dashboard');
      } else {
        // Not logged in, redirect to login
        router.push('/admin/login');
      }
    }
  }, [admin, loading, router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="w-8 h-8 animate-spin text-red-600 mx-auto mb-4" />
        <p className="text-gray-600">Redirecting to admin panel...</p>
      </div>
    </div>
  );
}
