'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAdmin } from '@/contexts/AdminContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import DashboardOverview from '@/components/admin/DashboardOverview';
import BookingsManagement from '@/components/admin/BookingsManagement';
import ProvidersManagement from '@/components/admin/ProvidersManagement';
import UsersManagement from '@/components/admin/UsersManagement';
import PaymentsManagement from '@/components/admin/PaymentsManagement';
import AnalyticsDashboard from '@/components/admin/AnalyticsDashboard';
import SystemSettings from '@/components/admin/SystemSettings';
import FeaturedProvidersManagement from '@/components/admin/FeaturedProvidersManagement';
import ReviewsManagement from '@/components/admin/ReviewsManagement';
import SupportTicketsManagement from '@/components/admin/SupportTicketsManagement';
import ProviderIdManagement from '@/components/admin/ProviderIdManagement';
import FeedbackManagement from '@/components/admin/FeedbackManagement';
import { Loader2 } from 'lucide-react';

export default function AdminDashboard() {
  const { admin, loading } = useAdmin();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    if (!loading && !admin) {
      router.push('/admin/login');
    }
  }, [admin, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-red-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  if (!admin) {
    return null;
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <DashboardOverview />;
      case 'bookings':
        return <BookingsManagement />;
      case 'providers':
        return <ProvidersManagement />;
      case 'users':
        return <UsersManagement />;
      case 'featured':
        return <FeaturedProvidersManagement />;
      case 'reviews':
        return <ReviewsManagement />;
      case 'support':
        return <SupportTicketsManagement />;
      case 'feedback':
        return <FeedbackManagement />;
      case 'provider-ids':
        return <ProviderIdManagement />;
      case 'payments':
        return <PaymentsManagement />;
      case 'analytics':
        return <AnalyticsDashboard />;
      case 'settings':
        return <SystemSettings />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <AdminSidebar 
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <AdminHeader 
          setSidebarOpen={setSidebarOpen}
          admin={admin}
        />

        {/* Content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
          {renderContent()}
        </main>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}
