'use client';

import { useState } from 'react';
import { Eye, EyeOff, Mail, Lock, User, Phone, ArrowRight, Check, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

import PasswordStrengthIndicator, { calculatePasswordStrength } from '@/components/PasswordStrengthIndicator';

export default function SignUpPage() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    userType: 'pet_owner',
    agreeToTerms: false,
    subscribeNewsletter: true
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [isRedirecting, setIsRedirecting] = useState(false);

  const { register, isLoading } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (!formData.agreeToTerms) {
      setError('Please agree to the terms and conditions');
      return;
    }

    // Validate password strength
    const passwordStrength = calculatePasswordStrength(formData.password);
    if (!passwordStrength.isValid) {
      setError('Password does not meet security requirements. Please use at least 12 characters with uppercase, lowercase, numbers, and special characters.');
      return;
    }

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    const userData = {
      name: `${formData.firstName} ${formData.lastName}`.trim(),
      email: formData.email,
      password: formData.password,
      phone: formData.phone,
      role: formData.userType as 'pet_owner' | 'provider' | 'admin'
    };

    console.log('🔧 Registering user with role:', formData.userType);
    console.log('🔧 User data:', userData);

    const result = await register(userData);

    console.log('🔧 Registration result:', result);
    console.log('🔧 User role after registration:', result.user?.role);

    if (result.success && result.user) {
      // Automatic redirect without modal
      console.log('🔧 User registered successfully, redirecting...');
      const userRole = result.user.role === 'provider' ? 'provider' : 'petowner';
      setIsRedirecting(true);

      // Check for redirect parameter first
      const searchParams = new URLSearchParams(window.location.search);
      const redirectTo = searchParams.get('redirect');

      // Add a small delay to ensure auth state is properly set
      setTimeout(() => {
        if (redirectTo) {
          console.log(`🔧 Redirecting to: ${redirectTo}`);
          window.location.href = decodeURIComponent(redirectTo);
        } else {
          // Redirect to appropriate dashboard based on user role
          console.log(`🔧 Redirecting to ${userRole} dashboard`);

          if (userRole === 'provider') {
            console.log('✅ Redirecting to provider dashboard');
            window.location.href = '/provider/dashboard';
          } else {
            console.log('✅ Redirecting to pet owner dashboard');
            window.location.href = '/dashboard';
          }
        }
      }, 500); // 500ms delay to ensure auth state is set
    } else {
      setError(result.error || 'Registration failed');
    }
  };



  return (
    <div className="min-h-screen pt-20 py-12 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-secondary-500/5 to-accent-500/10"></div>
      <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full blur-2xl animate-float"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-secondary-500/20 to-accent-500/20 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }}></div>
      <div className="absolute top-1/3 right-1/4 w-24 h-24 bg-gradient-to-r from-accent-500/15 to-warm-500/15 rounded-full blur-xl animate-float" style={{ animationDelay: '2s' }}></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-lg mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-6">
              <Image
                src="/favicon.png"
                alt="Fetchly Logo"
                width={64}
                height={64}
                className="w-16 h-16"
              />
            </div>
            <h1 className="text-3xl font-bold text-cool-800 mb-2">Join Fetchly Today!</h1>
            <p className="text-cool-600">Create your account and start connecting with pet care professionals</p>
          </div>

          {/* Sign Up Form */}
          <div className="glass-card rounded-2xl p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* User Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  I am a:
                </label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, userType: 'pet_owner' })}
                    className={`p-4 rounded-xl border-2 transition-all duration-300 relative ${
                      formData.userType === 'pet_owner'
                        ? 'border-green-500 bg-gradient-to-r from-green-50 to-blue-50 text-green-700 shadow-lg'
                        : 'border-white/30 bg-white/90 text-gray-700 hover:border-green-300 hover:bg-green-50'
                    }`}
                  >
                    {formData.userType === 'pet_owner' && (
                      <div className="absolute top-2 right-2">
                        <Check className="w-5 h-5 text-green-600" />
                      </div>
                    )}
                    <div className="text-center">
                      <div className="text-2xl mb-2">🐕</div>
                      <div className="font-medium">Pet Owner</div>
                      <div className="text-xs text-gray-500 mt-1">Find pet services</div>
                    </div>
                  </button>
                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, userType: 'provider' })}
                    className={`p-4 rounded-xl border-2 transition-all duration-300 relative ${
                      formData.userType === 'provider'
                        ? 'border-blue-500 bg-gradient-to-r from-blue-50 to-green-50 text-blue-700 shadow-lg'
                        : 'border-white/30 bg-white/90 text-gray-700 hover:border-blue-300 hover:bg-blue-50'
                    }`}
                  >
                    {formData.userType === 'provider' && (
                      <div className="absolute top-2 right-2">
                        <Check className="w-5 h-5 text-blue-600" />
                      </div>
                    )}
                    <div className="text-center">
                      <div className="text-2xl mb-2">👨‍⚕️</div>
                      <div className="font-medium">Service Provider</div>
                      <div className="text-xs text-gray-500 mt-1">Offer pet services</div>
                    </div>
                  </button>
                </div>

                {/* Role Confirmation */}
                <div className="mt-3 p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
                  <div className="flex items-center gap-2 text-sm">
                    <Check className="w-4 h-4 text-green-600" />
                    <span className="text-gray-700">
                      Selected: <span className="font-medium text-green-700">
                        {formData.userType === 'pet_owner' ? 'Pet Owner' : 'Service Provider'}
                      </span>
                      {formData.userType === 'pet_owner'
                        ? ' - You can book and manage pet services'
                        : ' - You can offer and manage pet services'
                      }
                    </span>
                  </div>
                </div>
              </div>

              {/* Name Fields */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">
                    First Name
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
                    <input
                      type="text"
                      value={formData.firstName}
                      onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                      className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                      placeholder="First name"
                      required
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={formData.lastName}
                    onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                    className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    placeholder="Last name"
                    required
                  />
                </div>
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-cool-700 mb-2">
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    placeholder="Enter your email"
                    required
                  />
                </div>
              </div>

              {/* Phone */}
              <div>
                <label className="block text-sm font-medium text-cool-700 mb-2">
                  Phone Number
                </label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    placeholder="Enter your phone number"
                    required
                  />
                </div>
              </div>

              {/* Password */}
              <div>
                <label className="block text-sm font-medium text-cool-700 mb-2">
                  Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className="w-full pl-10 pr-12 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    placeholder="Create a password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-cool-500 hover:text-primary-500 transition-colors duration-300"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>

                {/* Password Strength Indicator */}
                {formData.password && (
                  <div className="mt-3">
                    <PasswordStrengthIndicator
                      password={formData.password}
                      showRequirements={true}
                    />
                  </div>
                )}
              </div>

              {/* Confirm Password */}
              <div>
                <label className="block text-sm font-medium text-cool-700 mb-2">
                  Confirm Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                    className="w-full pl-10 pr-12 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    placeholder="Confirm your password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-cool-500 hover:text-primary-500 transition-colors duration-300"
                  >
                    {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              {/* Error Display */}
              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <AlertCircle className="w-5 h-5 text-red-500" />
                  <span className="text-red-700 text-sm">{error}</span>
                </div>
              )}

              {/* Terms and Newsletter */}
              <div className="space-y-3">
                <label className="flex items-start gap-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.agreeToTerms}
                    onChange={(e) => setFormData({ ...formData, agreeToTerms: e.target.checked })}
                    className="w-5 h-5 text-primary-500 border-2 border-white/30 rounded focus:ring-primary-500 mt-0.5"
                    required
                  />
                  <span className="text-sm text-cool-700">
                    I agree to the{' '}
                    <Link href="/terms" className="text-primary-500 hover:text-primary-600 transition-colors duration-300">
                      Terms of Service
                    </Link>{' '}
                    and{' '}
                    <Link href="/privacy" className="text-primary-500 hover:text-primary-600 transition-colors duration-300">
                      Privacy Policy
                    </Link>
                  </span>
                </label>
                
                <label className="flex items-start gap-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.subscribeNewsletter}
                    onChange={(e) => setFormData({ ...formData, subscribeNewsletter: e.target.checked })}
                    className="w-5 h-5 text-primary-500 border-2 border-white/30 rounded focus:ring-primary-500 mt-0.5"
                  />
                  <span className="text-sm text-cool-700">
                    Subscribe to our newsletter for pet care tips and updates
                  </span>
                </label>
              </div>

              {/* Sign Up Button */}
              <button
                type="submit"
                disabled={isLoading || isRedirecting}
                className="btn-primary w-full flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isRedirecting ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Redirecting to {formData.userType === 'provider' ? 'Provider' : 'Pet Owner'} Dashboard...
                  </>
                ) : isLoading ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <>
                    Create Account
                    <ArrowRight className="w-5 h-5" />
                  </>
                )}
              </button>
            </form>

            {/* Sign In Link */}
            <div className="text-center mt-6 pt-6 border-t border-white/30">
              <p className="text-cool-600">
                Already have an account?{' '}
                <Link 
                  href="/auth/signin" 
                  className="text-primary-500 hover:text-primary-600 font-medium transition-colors duration-300"
                >
                  Sign in here
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
}
