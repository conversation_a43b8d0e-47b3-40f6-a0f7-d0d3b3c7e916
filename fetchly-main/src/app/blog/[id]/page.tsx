'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Heart, MessageCircle, Share2, AlertTriangle, Copy, Facebook, Twitter, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { BlogService, BlogPost, BlogComment } from '@/lib/services/blog-service';

// Remove local interfaces since we're importing from blog-service

export default function BlogPostPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [comments, setComments] = useState<BlogComment[]>([]);
  const [loading, setLoading] = useState(true);
  const [liked, setLiked] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [submittingComment, setSubmittingComment] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);

  // Mock data - Fetchly Team authored content with veterinary disclaimers
  const mockPosts: { [key: string]: BlogPost } = {
    '1': {
      id: '1',
      title: 'Essential Grooming Tips for Long-Haired Dogs',
      excerpt: 'Learn essential grooming techniques to keep your long-haired dog healthy and beautiful.',
    content: `
      <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-yellow-700">
              <strong>Important Veterinary Disclaimer:</strong> This content is for educational purposes only. Always consult with a qualified veterinarian before making any changes to your pet's grooming routine, especially if your pet has skin conditions, allergies, or health concerns.
            </p>
          </div>
        </div>
      </div>

      <h2>Introduction</h2>
      <p>Long-haired dogs are absolutely beautiful, but they require special care to keep their coats healthy and manageable. At Fetchly, we've worked with professional groomers across Puerto Rico to compile these essential tips for maintaining long-haired breeds in our tropical climate.</p>

      <h2>Daily Brushing is Essential</h2>
      <p>The most important thing you can do for your long-haired dog is to brush them daily. This prevents matting, reduces shedding, and distributes natural oils throughout their coat. In Puerto Rico's humid climate, this becomes even more critical.</p>

      <h3>Choosing the Right Brush</h3>
      <ul>
        <li><strong>Slicker brushes</strong> - Great for removing loose undercoat</li>
        <li><strong>Pin brushes</strong> - Perfect for daily maintenance</li>
        <li><strong>Undercoat rakes</strong> - Essential for double-coated breeds</li>
        <li><strong>Dematting combs</strong> - For stubborn tangles</li>
      </ul>

      <h2>Bathing Frequency</h2>
      <p>In Puerto Rico's climate, you might need to bathe your long-haired dog more frequently than in cooler climates. Generally, every 4-6 weeks is appropriate, but active dogs or those with skin conditions may need more frequent baths. <strong>Always consult your veterinarian about the appropriate bathing frequency for your specific pet.</strong></p>

      <h3>Pre-Bath Preparation</h3>
      <p>Always brush your dog thoroughly before bathing. Wet mats become tighter and harder to remove. If you find mats, work them out gently with a dematting spray and comb.</p>

      <h2>Dealing with Puerto Rico's Climate</h2>
      <p>Our tropical climate can be challenging for long-haired breeds. Here are some specific tips:</p>

      <ul>
        <li>Consider a "summer cut" during the hottest months (discuss with your vet first)</li>
        <li>Keep your dog well-hydrated</li>
        <li>Provide plenty of shade and air conditioning</li>
        <li>Watch for signs of overheating and contact your vet immediately if concerned</li>
      </ul>

      <h2>Professional Grooming</h2>
      <p>While daily care is essential, professional grooming every 6-8 weeks helps maintain your dog's coat health. A professional can:</p>

      <ul>
        <li>Properly trim and shape the coat</li>
        <li>Clean ears and trim nails</li>
        <li>Check for skin issues</li>
        <li>Provide breed-specific styling</li>
      </ul>

      <h2>When to Contact Your Veterinarian</h2>
      <p><strong>Always consult your veterinarian if you notice:</strong></p>
      <ul>
        <li>Excessive scratching or skin irritation</li>
        <li>Unusual odors from the coat or skin</li>
        <li>Changes in coat texture or appearance</li>
        <li>Any signs of skin conditions or allergies</li>
        <li>Behavioral changes related to grooming</li>
      </ul>

      <h2>Conclusion</h2>
      <p>Caring for a long-haired dog in Puerto Rico requires dedication, but the results are worth it. With proper daily care, regular professional grooming, and guidance from your veterinarian, your furry friend will stay comfortable, healthy, and beautiful year-round.</p>

      <p>At Fetchly, we connect you with certified professional groomers throughout Puerto Rico who understand the unique needs of long-haired breeds in our tropical climate. <strong>Remember: this information is educational only - always consult with your veterinarian for personalized pet care advice.</strong></p>
    `,
    author: {
      name: 'Fetchly Team',
      avatar: '/favicon.png',
      isPro: true,
      specialization: 'Pet Care Experts'
    },
    category: 'Grooming',
    tags: ['grooming', 'long-hair', 'maintenance', 'tropical-climate'],
    publishedAt: new Date(),
    readTime: 5,
    likes: 0,
    comments: 0,
    image: 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=800',
    featured: true,
    likedBy: []
  },
  '2': {
    id: '2',
    title: 'Pet Safety During Hurricane Season in Puerto Rico',
    excerpt: 'Essential tips to keep your pets safe during hurricane season in the Caribbean.',
    content: `
      <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-red-700">
              <strong>Emergency Preparedness:</strong> This guide is for preparedness only. In case of actual emergency, follow official evacuation orders and contact emergency services immediately.
            </p>
          </div>
        </div>
      </div>

      <h2>Hurricane Preparedness for Pet Owners</h2>
      <p>Living in Puerto Rico means being prepared for hurricane season. Your pets depend on you to keep them safe during these challenging times. Here's your comprehensive guide to hurricane preparedness for pets.</p>

      <h2>Emergency Kit Essentials</h2>
      <p>Prepare an emergency kit for your pets that includes:</p>
      <ul>
        <li>At least 7 days of food and water</li>
        <li>Medications and medical records</li>
        <li>Carriers or crates for each pet</li>
        <li>Leashes, collars with ID tags</li>
        <li>Comfort items (toys, blankets)</li>
        <li>Waste bags and litter</li>
        <li>First aid supplies</li>
      </ul>

      <h2>Evacuation Planning</h2>
      <p>Know your evacuation routes and pet-friendly shelters. Many public shelters don't accept pets, so identify:</p>
      <ul>
        <li>Pet-friendly hotels outside the evacuation zone</li>
        <li>Friends or family who can house your pets</li>
        <li>Boarding facilities in safe areas</li>
        <li>Animal shelters that accept evacuated pets</li>
      </ul>

      <h2>During the Storm</h2>
      <p>Keep pets indoors and in a safe room away from windows. Maintain routines as much as possible to reduce stress. Never leave pets outside or tied up during a storm.</p>
    `,
    author: {
      name: 'Fetchly Emergency Team',
      avatar: '/favicon.png',
      isPro: true,
      specialization: 'Emergency Preparedness'
    },
    category: 'Safety',
    tags: ['hurricane', 'emergency', 'safety', 'puerto-rico'],
    publishedAt: new Date('2024-01-15'),
    readTime: 8,
    likes: 0,
    comments: 0,
    image: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=800',
    featured: false,
    likedBy: []
  },
  '3': {
    id: '3',
    title: 'Choosing the Right Veterinarian in Puerto Rico',
    excerpt: 'A comprehensive guide to finding the best veterinary care for your pet in Puerto Rico.',
    content: `
      <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-blue-700">
              <strong>Professional Advice:</strong> This guide helps you make informed decisions about veterinary care. Always prioritize licensed veterinarians for your pet's health needs.
            </p>
          </div>
        </div>
      </div>

      <div class="my-6">
        <img src="/vet2.jpg" alt="Veterinarian examining a dog in Puerto Rico" class="w-full h-64 object-cover rounded-lg shadow-lg" />
        <p class="text-sm text-gray-600 mt-2 text-center italic">A qualified veterinarian examining a pet in Puerto Rico</p>
      </div>

      <h2>What to Look for in a Veterinarian</h2>
      <p>Finding the right veterinarian is crucial for your pet's health and your peace of mind. Here are key factors to consider when choosing a vet in Puerto Rico.</p>

      <h2>Credentials and Experience</h2>
      <p>Ensure your veterinarian is licensed by the Puerto Rico Board of Veterinary Medicine. Look for:</p>
      <ul>
        <li>Valid veterinary license</li>
        <li>Continuing education certifications</li>
        <li>Specialization in your pet's species</li>
        <li>Experience with tropical diseases</li>
      </ul>

      <h2>Services and Facilities</h2>
      <p>A good veterinary clinic should offer comprehensive services including:</p>
      <ul>
        <li>Preventive care and vaccinations</li>
        <li>Emergency services or referrals</li>
        <li>Surgical capabilities</li>
        <li>Diagnostic equipment (X-ray, lab)</li>
        <li>Pharmacy services</li>
      </ul>

      <h2>Communication and Comfort</h2>
      <p>Your vet should communicate clearly, answer questions patiently, and make both you and your pet feel comfortable during visits.</p>
    `,
    author: {
      name: 'Dr. Maria Rodriguez',
      avatar: '/favicon.png',
      isPro: true,
      specialization: 'Veterinary Guidance'
    },
    category: 'Health',
    tags: ['veterinarian', 'health', 'puerto-rico', 'choosing-vet'],
    publishedAt: new Date('2024-01-14'),
    readTime: 6,
    likes: 0,
    comments: 0,
    image: '/vet1.jpg',
    featured: false,
    likedBy: []
  }
};

  // Load real comments from Firebase instead of hardcoded ones
  const loadComments = async (postId: string) => {
    try {
      // In a real implementation, this would load from Firebase
      // For now, return empty array to remove hardcoded comments
      return [];
    } catch (error) {
      console.error('Error loading comments:', error);
      return [];
    }
  };

  useEffect(() => {
    // Simulate loading
    setTimeout(async () => {
      const postId = params.id as string;
      const selectedPost = mockPosts[postId];

      if (selectedPost) {
        setPost(selectedPost);
        // Load real comments from Firebase
        const postComments = await loadComments(postId);
        setComments(postComments);
      } else {
        // If post not found, show the first post as fallback
        setPost(mockPosts['1']);
        const fallbackComments = await loadComments('1');
        setComments(fallbackComments);
      }

      setLoading(false);
    }, 1000);
  }, [params.id]);

  const handleLike = async () => {
    if (!post) return;

    try {
      // For logged-in users
      if (user) {
        const wasLiked = post.likedBy?.includes(user.id) || false;
        const newLikeCount = wasLiked ? post.likes - 1 : post.likes + 1;
        const newLikedBy = wasLiked
          ? (post.likedBy || []).filter(id => id !== user.id)
          : [...(post.likedBy || []), user.id];

        setLiked(!wasLiked);
        setPost({
          ...post,
          likes: newLikeCount,
          likedBy: newLikedBy
        });

        // Update on the server
        await BlogService.togglePostLike(post.id, user.id);
      } else {
        // For non-logged users, use localStorage
        const likedPosts = JSON.parse(localStorage.getItem('likedBlogPosts') || '[]');
        const wasLiked = likedPosts.includes(post.id);
        const newLikeCount = wasLiked ? post.likes - 1 : post.likes + 1;

        setLiked(!wasLiked);
        setPost({
          ...post,
          likes: newLikeCount
        });

        // Update localStorage
        if (wasLiked) {
          const updatedLikes = likedPosts.filter((id: string) => id !== post.id);
          localStorage.setItem('likedBlogPosts', JSON.stringify(updatedLikes));
        } else {
          likedPosts.push(post.id);
          localStorage.setItem('likedBlogPosts', JSON.stringify(likedPosts));
        }
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      // Revert on error
      setLiked(!liked);
      setPost({
        ...post,
        likes: liked ? post.likes - 1 : post.likes + 1
      });
    }
  };

  const handleCommentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || !post) return;

    // For non-logged users, ask for name
    let authorName = user?.name || 'Anonymous User';
    let authorAvatar = user?.avatar || '/favicon.png';

    if (!user) {
      const guestName = prompt('Please enter your name to comment:');
      if (!guestName?.trim()) return;
      authorName = guestName.trim();
    }

    setSubmittingComment(true);
    try {
      // Create optimistic comment
      const tempComment: BlogComment = {
        id: `temp-${Date.now()}`,
        postId: post.id,
        author: {
          name: authorName,
          avatar: authorAvatar,
          userId: user?.id || 'guest'
        },
        content: newComment,
        publishedAt: new Date(),
        likes: 0,
        likedBy: []
      };

      // Add to UI immediately
      setComments([tempComment, ...comments]);
      setNewComment('');

      // Update server
      const savedComment = await BlogService.addComment(
        post.id,
        user.id,
        user.name || user.email?.split('@')[0] || 'Anonymous',
        user.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
        newComment
      );

      // Replace temp comment with saved one
      setComments(prev => [savedComment, ...prev.filter(c => c.id !== tempComment.id)]);

      // Update comment count
      setPost({
        ...post,
        comments: post.comments + 1
      });
    } catch (error) {
      console.error('Error adding comment:', error);
      // Remove temp comment on error
      setComments(prev => prev.filter(c => !c.id.startsWith('temp-')));
    } finally {
      setSubmittingComment(false);
    }
  };

  const handleCommentLike = async (commentId: string) => {
    if (!user) return;

    try {
      const result = await BlogService.toggleCommentLike(commentId, user.id);
      setComments(comments.map(comment =>
        comment.id === commentId
          ? { ...comment, likes: result.newLikeCount }
          : comment
      ));
    } catch (error) {
      console.error('Error toggling comment like:', error);
    }
  };

  const handleShare = (platform?: string) => {
    if (!post) return;

    const url = window.location.href;
    const title = post.title;
    const text = `Check out this helpful article from Fetchly: ${title}`;

    switch (platform) {
      case 'copy':
        navigator.clipboard.writeText(url);
        alert('Link copied to clipboard!');
        break;
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'community':
        // Redirect to community with pre-filled post
        const communityText = `${text}\n\n${url}`;
        router.push(`/community?share=${encodeURIComponent(communityText)}`);
        break;
      default:
        setShowShareMenu(!showShareMenu);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen pt-20 bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading article...</p>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen pt-20 bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Article Not Found</h1>
          <Link href="/blog" className="text-blue-600 hover:text-blue-700">
            ← Back to Blog
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50">
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-6">
          <button
            onClick={() => router.back()}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors group"
          >
            <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
            <span className="font-medium">Back to Blog</span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <article className="bg-white rounded-2xl shadow-lg overflow-hidden">
              {/* Article Header */}
              <div className="p-8">
                <div className="flex items-center space-x-2 text-sm text-gray-600 mb-4">
                  <span className="bg-gradient-to-r from-green-600 to-blue-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                    {post.category}
                  </span>
                  <span>•</span>
                  <span>{post.readTime} min read</span>
                  <span>•</span>
                  <span>{post.publishedAt.toLocaleDateString()}</span>
                </div>

                <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">
                  {post.title}
                </h1>

                <div className="flex items-center space-x-4 mb-8">
                  <img
                    src={post.author.avatar}
                    alt={post.author.name}
                    className="w-12 h-12 rounded-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/favicon.png';
                    }}
                  />
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-gray-900">{post.author.name}</h3>
                      {post.author.isPro && (
                        <span className="bg-gradient-to-r from-green-600 to-blue-600 text-white px-2 py-1 rounded-full text-xs font-semibold">
                          VERIFIED
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{post.author.specialization}</p>
                  </div>
                </div>
              </div>

              {/* Article Content */}
              <div className="px-8 pb-8">
                <div className="prose prose-lg max-w-none">
                  <div dangerouslySetInnerHTML={{ __html: post.content.replace(/\n/g, '<br />') }} />
                </div>
              </div>

              {/* Veterinary Disclaimer Footer */}
              <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-xl">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="text-lg font-semibold text-blue-900 mb-2">Important Medical Disclaimer</h3>
                    <p className="text-blue-800 text-sm leading-relaxed">
                      This article is provided by Fetchly for educational purposes only and should not replace professional veterinary advice.
                      Every pet is unique, and what works for one may not be suitable for another. Always consult with a qualified veterinarian
                      before making any changes to your pet's care routine, diet, exercise, or treatment plan. If your pet shows signs of illness
                      or distress, seek immediate veterinary attention.
                    </p>
                    <p className="text-blue-700 text-xs mt-3 font-medium">
                      Fetchly connects pet owners with certified professionals but does not provide veterinary services directly.
                    </p>
                  </div>
                </div>
              </div>

              {/* Article Actions */}
              <div className="flex items-center justify-between pt-8 mt-8 border-t border-gray-200">
                <div className="flex items-center space-x-6">
                  {user ? (
                    <button
                      onClick={handleLike}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                        liked
                          ? 'bg-red-100 text-red-700'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      <Heart className={`w-5 h-5 ${liked ? 'fill-current' : ''}`} />
                      <span>{post?.likes || 0}</span>
                    </button>
                  ) : (
                    <Link href="/auth/signup" className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg hover:from-green-600 hover:to-blue-600 transition-colors">
                      <Heart className="w-5 h-5" />
                      <span>{post?.likes || 0}</span>
                      <span className="text-sm">• Sign up to like</span>
                    </Link>
                  )}
                  <div className="flex items-center space-x-2 text-gray-700">
                    <MessageCircle className="w-5 h-5" />
                    <span>{post?.comments || 0}</span>
                  </div>
                </div>
                <div className="relative">
                  <button
                    onClick={() => handleShare()}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                  >
                    <Share2 className="w-5 h-5" />
                    <span>Share</span>
                  </button>

                  {showShareMenu && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                      <div className="py-2">
                        <button
                          onClick={() => handleShare('copy')}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          <Copy className="w-4 h-4 mr-2" />
                          Copy Link
                        </button>
                        <button
                          onClick={() => handleShare('community')}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          <MessageCircle className="w-4 h-4 mr-2" />
                          Share to Community
                        </button>
                        <button
                          onClick={() => handleShare('facebook')}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          <Facebook className="w-4 h-4 mr-2" />
                          Share on Facebook
                        </button>
                        <button
                          onClick={() => handleShare('twitter')}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          <Twitter className="w-4 h-4 mr-2" />
                          Share on Twitter
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </article>

            {/* Comments Section */}
            <div className="bg-white rounded-2xl shadow-lg p-8 mt-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Comments ({comments.length})</h2>

              {/* Comment Form - Available to everyone */}
              <form onSubmit={handleCommentSubmit} className="mb-8">
                <div className="flex space-x-4">
                  <img
                    src={user?.avatar || '/favicon.png'}
                    alt={user?.name || 'Guest User'}
                    className="w-10 h-10 rounded-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/favicon.png';
                    }}
                  />
                  <div className="flex-1">
                    <textarea
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      placeholder={user ? "Share your thoughts..." : "Share your thoughts... (You'll be asked for your name when posting)"}
                      className="w-full p-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                      rows={3}
                    />
                    <div className="flex justify-end mt-3">
                      <button
                        type="submit"
                        disabled={!newComment.trim() || submittingComment}
                        className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                      >
                        {submittingComment && (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        )}
                        <span>{submittingComment ? 'Posting...' : 'Post Comment'}</span>
                      </button>
                    </div>
                  </div>
                </div>
              </form>

              {/* Comments List */}
              <div className="space-y-6">
                {comments.map((comment) => (
                  <div key={comment.id} className="flex space-x-4">
                    <img
                      src={comment.author.avatar}
                      alt={comment.author.name}
                      className="w-10 h-10 rounded-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/favicon.png';
                      }}
                    />
                    <div className="flex-1">
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-gray-900">{comment.author.name}</h4>
                          <span className="text-sm text-gray-500">
                            {comment.publishedAt.toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-gray-700">{comment.content}</p>
                      </div>
                      <div className="flex items-center space-x-4 mt-2">
                        <button
                          onClick={() => handleCommentLike(comment.id)}
                          className={`flex items-center space-x-1 text-sm transition-colors ${
                            user && comment.likedBy?.includes(user.id)
                              ? 'text-red-600 hover:text-red-700'
                              : 'text-gray-500 hover:text-gray-700'
                          }`}
                          disabled={!user}
                        >
                          <Heart className={`w-4 h-4 ${
                            user && comment.likedBy?.includes(user.id) ? 'fill-current' : ''
                          }`} />
                          <span>{comment.likes}</span>
                        </button>
                        {user && (
                          <button className="text-sm text-gray-500 hover:text-gray-700">
                            Reply
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Author Bio */}
            <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
              <h3 className="text-lg font-bold text-gray-900 mb-4">About the Author</h3>
              <div className="text-center">
                <img
                  src={post?.author.avatar}
                  alt={post?.author.name}
                  className="w-20 h-20 rounded-full object-cover mx-auto mb-4"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/favicon.png';
                  }}
                />
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <h4 className="font-semibold text-gray-900">{post?.author.name}</h4>
                  {post?.author.isPro && (
                    <span className="bg-gradient-to-r from-green-600 to-blue-600 text-white px-2 py-1 rounded-full text-xs font-semibold">
                      VERIFIED
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-3">{post?.author.specialization}</p>
                <p className="text-sm text-gray-700">
                  The Fetchly team consists of certified pet care professionals, veterinarians, and animal behaviorists
                  dedicated to providing reliable, educational content for pet owners in Puerto Rico and beyond.
                </p>
                <div className="mt-4 p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                  <p className="text-xs text-gray-600">
                    <strong>Our Mission:</strong> Connecting pet owners with trusted professionals while providing
                    educational resources to promote pet health and wellbeing.
                  </p>
                </div>
              </div>
            </div>

            {/* Related Articles */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Related Articles</h3>
              <div className="space-y-4">
                <Link href="/blog/2" className="block hover:bg-gray-50 p-3 rounded-lg transition-colors">
                  <h4 className="font-semibold text-gray-900 text-sm mb-1">Understanding Your Cat's Behavior</h4>
                  <p className="text-xs text-gray-600">5 min read</p>
                </Link>
                <Link href="/blog/3" className="block hover:bg-gray-50 p-3 rounded-lg transition-colors">
                  <h4 className="font-semibold text-gray-900 text-sm mb-1">Exercise for Senior Dogs</h4>
                  <p className="text-xs text-gray-600">6 min read</p>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
