'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import {
  Search,
  SlidersHorizontal,
  Loader2,
  X,
  RefreshCw
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';

import ProviderCard from './ProviderCard';

// Provider interface
interface Provider {
  id: string;
  businessName: string;
  ownerName: string;
  serviceType: string;
  description: string;
  city: string;
  state: string;
  zipCode: string;
  address: string;
  phone: string;
  email: string;
  rating: number;
  reviewCount: number;
  profilePhoto?: string;
  verified: boolean;
  featured: boolean;
  specialties: string[];
  responseTime: string;
  price: number;
  approved: boolean;
  createdAt: string;
}

const serviceTypes = ['All Services', 'Grooming', 'Veterinary', 'Pet Hotel', 'Dog Walking', 'Training', 'Pet Sitting'];
const sortOptions = ['Recommended', 'Highest Rated', 'Nearest', 'Lowest Price', 'Most Reviews'];

export default function SearchPage() {
  const { user, isAuthenticated } = useAuth();

  const router = useRouter();
  const searchParams = useSearchParams();
  
  // State
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [searching, setSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasSearched, setHasSearched] = useState(false);
  
  // Search form state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedService, setSelectedService] = useState('All Services');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [selectedDate, setSelectedDate] = useState('');
  const [sortBy, setSortBy] = useState('Recommended');
  const [showFilters, setShowFilters] = useState(false);

  // Initialize from URL params
  useEffect(() => {
    const query = searchParams.get('q') || '';
    const service = searchParams.get('service') || 'All Services';
    const location = searchParams.get('location') || '';

    setSearchQuery(query);
    setSelectedService(service);
    setSelectedLocation(location);

    // Perform initial search if there are params, otherwise load all
    if (query || location || service !== 'All Services') {
      performSearch(query, location, service);
    } else {
      loadAllProviders();
    }
  }, [searchParams]);

  // Load all active providers (initial load)
  const loadAllProviders = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      setHasSearched(false);

      console.log('🔍 Loading all active providers from Firebase...');

      // Try simple approach first - just get approved providers without services check
      console.log('🔍 Attempting simple provider query...');

      try {
        // Direct Firebase query without complex service checking
        const { collection, getDocs, query, where } = await import('firebase/firestore');
        const { db } = await import('@/lib/firebase/config');

        const providersQuery = query(
          collection(db, 'providers'),
          where('status', '==', 'approved')
        );

        const providersSnapshot = await getDocs(providersQuery);
        console.log(`📊 Direct query found ${providersSnapshot.size} approved providers`);

        const providers = providersSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Provider[];

        // Debug: Log the first provider to see structure
        if (providers.length > 0) {
          console.log('🔍 First provider structure:', providers[0]);
          console.log('🔍 Provider IDs vs User IDs:');
          providers.forEach((p, index) => {
            console.log(`Provider ${index + 1}:`, {
              documentId: p.id,
              userId: p.userId || p.uid || 'NO_USER_ID',
              businessName: p.businessName || p.name,
              email: p.email
            });
          });
          setProviders(providers);
        } else {
          // Fallback: try getting ALL providers regardless of status
          console.log('❌ No approved providers, trying all providers...');
          const allProvidersQuery = collection(db, 'providers');
          const allProvidersSnapshot = await getDocs(allProvidersQuery);
          console.log(`📊 Found ${allProvidersSnapshot.size} total providers`);

          const allProviders = allProvidersSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Provider[];

          if (allProviders.length > 0) {
            console.log('🔍 First provider from all:', allProviders[0]);
            setProviders(allProviders);
          }
        }
      } catch (directError) {
        console.error('❌ Direct query failed:', directError);
        // Set empty providers if query fails
        setProviders([]);
      }

      setHasSearched(true);

      if (providers.length === 0) {
        console.log('❌ No providers found');
      } else {
        console.log('✅ Providers loaded successfully');
      }
    } catch (error) {
      console.error('❌ Error loading providers:', error);
      setError('Failed to load providers. Please try again.');
      toast.error('Failed to load providers');
    } finally {
      setLoading(false);
    }
  }, []);

  // Perform search with Firebase
  const performSearch = useCallback(async (query: string, location: string, serviceType: string) => {
    try {
      setSearching(true);
      setError(null);
      setHasSearched(false);

      console.log('🔍 Searching Firebase with:', { query, location, serviceType });

      // Get all providers first using direct query to avoid permissions issues
      console.log('🔍 Performing direct search query...');

      const { collection, getDocs, query: firestoreQuery, where } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/config');

      // Simple query for approved providers
      const providersQuery = firestoreQuery(
        collection(db, 'providers'),
        where('status', '==', 'approved')
      );

      const providersSnapshot = await getDocs(providersQuery);
      let results = providersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Provider[];

      console.log(`📊 Got ${results.length} approved providers from direct query`);

      // Filter by search criteria
      if (query || location || (serviceType && serviceType !== 'All Services')) {
        results = results.filter(provider => {
          let matches = true;

          // Search query filter (search all text fields)
          if (query) {
            const searchTerm = query.toLowerCase();
            const searchableText = [
              provider.businessName,
              provider.ownerName,
              provider.serviceType,
              provider.description,
              provider.city,
              provider.state,
              provider.zipCode,
              provider.address,
              ...(provider.specialties || [])
            ].join(' ').toLowerCase();

            matches = matches && searchableText.includes(searchTerm);
          }

          // Location filter (city, state, or zipcode)
          if (location) {
            const locationTerm = location.toLowerCase();
            const locationMatch =
              provider.city?.toLowerCase().includes(locationTerm) ||
              provider.state?.toLowerCase().includes(locationTerm) ||
              provider.zipCode?.includes(location) ||
              provider.address?.toLowerCase().includes(locationTerm);

            matches = matches && locationMatch;
          }

          // Service type filter
          if (serviceType && serviceType !== 'All Services') {
            const serviceMatch = provider.serviceType?.toLowerCase().includes(serviceType.toLowerCase());
            matches = matches && serviceMatch;
          }

          return matches;
        });
      }

      console.log(`📊 Search results: ${results.length} providers found`);
      setProviders(results);
      setHasSearched(true);

      if (results.length === 0) {
        console.log('❌ No providers match search criteria');
      } else {
        toast.success(`Found ${results.length} providers`);
      }
    } catch (error) {
      console.error('❌ Error searching providers:', error);
      setError('Search failed. Please try again.');
      toast.error('Search failed');
    } finally {
      setSearching(false);
    }
  }, []);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Update URL with search params
    const params = new URLSearchParams();
    if (searchQuery) params.set('q', searchQuery);
    if (selectedLocation) params.set('location', selectedLocation);
    if (selectedService !== 'All Services') params.set('service', selectedService);
    
    router.push(`/search?${params.toString()}`);
    
    // Perform search
    performSearch(searchQuery, selectedLocation, selectedService);
  };

  const refreshSearch = () => {
    if (searchQuery || selectedLocation || selectedService !== 'All Services') {
      performSearch(searchQuery, selectedLocation, selectedService);
    } else {
      loadAllProviders();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-teal-50 dark:from-dark-navy dark:via-dark-navy dark:to-dark-navy">
      {/* Hero Section with Search */}
      <div className="relative bg-gradient-to-br from-green-400 via-blue-400 to-teal-500 dark:from-dark-navy dark:via-dark-electric dark:to-dark-sky text-white dark:text-dark-offWhite">
        <div className="absolute inset-0 bg-black/20 dark:bg-black/40"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Find Perfect Pet Care
            </h1>
            <p className="text-xl text-white/90 dark:text-dark-lightGray max-w-2xl mx-auto">
              Connect with verified pet service professionals in your area
            </p>
          </div>

          {/* Search Form */}
          <form onSubmit={handleSearch} className="max-w-4xl mx-auto">
            <div className="backdrop-blur-xl bg-white/10 border border-white/20 rounded-3xl p-6 shadow-2xl">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Search Input */}
                <div className="md:col-span-2 relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search services, providers, or locations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800 placeholder-gray-500"
                  />
                </div>

                {/* Service Type */}
                <div>
                  <select
                    value={selectedService}
                    onChange={(e) => setSelectedService(e.target.value)}
                    className="w-full py-4 px-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800"
                  >
                    {serviceTypes.map(service => (
                      <option key={service} value={service}>{service}</option>
                    ))}
                  </select>
                </div>

                {/* Search Button */}
                <div>
                  <button
                    type="submit"
                    disabled={loading || searching}
                    className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-4 px-6 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 flex items-center justify-center gap-2"
                  >
                    {searching ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin" />
                        Searching...
                      </>
                    ) : (
                      <>
                        <Search className="w-5 h-5" />
                        Search
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Additional Filters */}
              <div className="mt-4 flex flex-wrap gap-4">
                <input
                  type="text"
                  placeholder="Location (city, state, zip code)"
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="flex-1 min-w-[200px] py-3 px-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800 placeholder-gray-500"
                />
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="py-3 px-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800"
                />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="py-3 px-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800"
                >
                  {sortOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Results Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Results Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {loading || searching ? 'Searching...' : 
               hasSearched ? `${providers.length} Providers Found` : 'Loading Providers...'}
            </h2>
            {searchQuery && hasSearched && (
              <p className="text-gray-600 mt-1">
                Results for "{searchQuery}"
                {selectedLocation && ` in ${selectedLocation}`}
              </p>
            )}
          </div>
          
          <div className="flex items-center gap-4">
            <button
              onClick={refreshSearch}
              disabled={loading || searching}
              className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${(loading || searching) ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors"
            >
              <SlidersHorizontal className="w-4 h-4" />
              Filters
            </button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="backdrop-blur-xl bg-red-50/90 border border-red-200/30 rounded-3xl p-6 mb-8">
            <p className="text-red-600 text-center">{error}</p>
            <button
              onClick={refreshSearch}
              className="mt-4 mx-auto block px-6 py-2 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Loading State */}
        {(loading || searching) && (
          <div className="flex justify-center items-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p className="text-gray-600">
                {searching ? 'Searching providers...' : 'Loading providers...'}
              </p>
            </div>
          </div>
        )}

        {/* Provider Cards */}
        {!loading && !searching && hasSearched && (
          <>
            {providers.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {providers.map((provider) => {
                  // Show simple cards for non-logged users, detailed for logged users
                  const showSimpleCard = !isAuthenticated;

                  return (
                    <ProviderCard
                      key={provider.id}
                      provider={provider}
                      showSimpleCard={showSimpleCard}
                    />
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="backdrop-blur-xl bg-white/90 border border-gray-200/30 rounded-3xl p-12 max-w-md mx-auto">
                  <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">No providers found</h3>
                  <p className="text-gray-600 mb-6">
                    {selectedLocation 
                      ? `No providers available in ${selectedLocation}. Try expanding your search area or adjusting your criteria.`
                      : 'No providers match your search criteria. Try adjusting your filters or search terms.'
                    }
                  </p>
                  <button
                    onClick={refreshSearch}
                    className="px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-xl hover:from-green-600 hover:to-blue-600 transition-all"
                  >
                    Refresh Search
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
