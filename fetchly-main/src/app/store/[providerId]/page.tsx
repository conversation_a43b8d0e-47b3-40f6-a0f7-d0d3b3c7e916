'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { ShoppingCart, Star, MapPin, Phone, Mail, Globe, Heart, Share2, ExternalLink, Package, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';
import { Provider } from '@/lib/firebase/providers';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface Product {
  id: string;
  name: string;
  price: number;
  imageUrl: string;
  description?: string;
  category?: string;
  inStock?: boolean;
}

export default function ProviderStorePage() {
  const { providerId } = useParams();
  const { user } = useAuth();
  const router = useRouter();
  const [provider, setProvider] = useState<Provider | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [cart, setCart] = useState<{[key: string]: number}>({});
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    const loadStoreData = async () => {
      if (!providerId) return;
      
      try {
        setLoading(true);
        const providerDoc = await getDoc(doc(db, 'providers', providerId as string));
        
        if (providerDoc.exists()) {
          setProvider({ id: providerDoc.id, ...providerDoc.data() } as Provider);
          
          const productsSnapshot = await getDocs(
            query(collection(db, 'products'), where('providerId', '==', providerId))
          );
          
          const productsData = productsSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Product[];
          
          setProducts(productsData);
        } else {
          toast.error('Store not found');
        }
      } catch (error) {
        console.error('Error loading store:', error);
        toast.error('Failed to load store');
      } finally {
        setLoading(false);
      }
    };
    
    loadStoreData();
  }, [providerId]);

  // Add to cart function
  const addToCart = (productId: string) => {
    setCart(prev => ({
      ...prev,
      [productId]: (prev[productId] || 0) + 1
    }));
    toast.success('Added to cart!');
  };

  // Get cart total
  const cartTotal = Object.entries(cart).reduce((total, [productId, quantity]) => {
    const product = products.find(p => p.id === productId);
    return total + (product ? product.price * quantity : 0);
  }, 0);

  // Get unique categories
  const categories = ['all', ...new Set(products.map(p => p.category).filter(Boolean))];

  // Filter products by category
  const filteredProducts = selectedCategory === 'all'
    ? products
    : products.filter(p => p.category === selectedCategory);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-green-50">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
            <h2 className="text-xl font-semibold text-gray-700 mb-2">Loading Store</h2>
            <p className="text-gray-500">Please wait while we fetch the products...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!provider) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-green-50">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center max-w-md mx-auto p-6">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Package className="w-8 h-8 text-gray-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-4">Store Not Found</h1>
            <p className="text-gray-600 mb-6">The store you're looking for doesn't exist or has been removed.</p>
            <Link
              href="/community"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Community
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-green-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 rounded-xl hover:bg-gray-100 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <h1 className="text-xl font-bold text-gray-800">{provider.businessName}'s Store</h1>
            </div>

            {/* Cart Summary */}
            {Object.keys(cart).length > 0 && (
              <div className="flex items-center space-x-4">
                <div className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-4 py-2 rounded-xl">
                  <div className="flex items-center space-x-2">
                    <ShoppingCart className="w-4 h-4" />
                    <span className="font-medium">${cartTotal.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Store Header */}
        <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-xl border border-white/20 p-8 mb-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Provider Avatar */}
            <div className="flex-shrink-0">
              <div className="w-32 h-32 rounded-3xl overflow-hidden bg-gradient-to-br from-green-100 to-blue-100 border-4 border-white shadow-lg">
                {provider.profilePhoto ? (
                  <img
                    src={provider.profilePhoto}
                    alt={provider.businessName}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-green-500 to-blue-500">
                    <span className="text-4xl font-bold text-white">
                      {provider.businessName?.charAt(0) || 'P'}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Store Info */}
            <div className="flex-1">
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-800 mb-2">{provider.businessName}</h1>
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="flex items-center">
                      <Star className="w-5 h-5 text-yellow-400 fill-yellow-400 mr-1" />
                      <span className="font-semibold text-gray-700">{provider.rating?.toFixed(1) || 'New'}</span>
                      <span className="text-gray-500 ml-1">({provider.reviewCount || 0} reviews)</span>
                    </div>
                    {provider.location && (
                      <div className="flex items-center text-gray-600">
                        <MapPin className="w-4 h-4 mr-1" />
                        <span className="text-sm">{provider.location}</span>
                      </div>
                    )}
                  </div>
                  {provider.description && (
                    <p className="text-gray-600 leading-relaxed max-w-2xl">{provider.description}</p>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3">
                  <button className="flex items-center justify-center px-4 py-2 bg-white border-2 border-gray-200 rounded-xl hover:border-red-300 hover:text-red-600 transition-all">
                    <Heart className="w-4 h-4 mr-2" />
                    <span className="font-medium">Follow</span>
                  </button>
                  <button className="flex items-center justify-center px-4 py-2 bg-white border-2 border-gray-200 rounded-xl hover:border-blue-300 hover:text-blue-600 transition-all">
                    <Share2 className="w-4 h-4 mr-2" />
                    <span className="font-medium">Share</span>
                  </button>
                  <Link
                    href={`/provider/${provider.id}`}
                    className="flex items-center justify-center px-4 py-2 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    <span className="font-medium">View Profile</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Category Filter */}
        {categories.length > 1 && (
          <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20 p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Categories</h3>
            <div className="flex flex-wrap gap-3">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-xl font-medium transition-all ${
                    selectedCategory === category
                      ? 'bg-gradient-to-r from-green-500 to-blue-500 text-white shadow-lg'
                      : 'bg-white border-2 border-gray-200 text-gray-700 hover:border-green-300 hover:text-green-600'
                  }`}
                >
                  {category === 'all' ? 'All Products' : category}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.length > 0 ? (
            filteredProducts.map((product) => (
              <div key={product.id} className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20 overflow-hidden hover:shadow-xl transition-all duration-300 group">
                {/* Product Image */}
                <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
                  {product.imageUrl ? (
                    <img
                      src={product.imageUrl}
                      alt={product.name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Package className="w-12 h-12 text-gray-400" />
                    </div>
                  )}

                  {/* Stock Status */}
                  <div className="absolute top-3 left-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      product.inStock !== false
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {product.inStock !== false ? 'In Stock' : 'Out of Stock'}
                    </span>
                  </div>

                  {/* Quick Actions */}
                  <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button className="p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors">
                      <Heart className="w-4 h-4 text-gray-600 hover:text-red-500" />
                    </button>
                  </div>
                </div>

                {/* Product Info */}
                <div className="p-6">
                  <div className="mb-3">
                    <h3 className="font-semibold text-lg text-gray-800 mb-1 line-clamp-2">{product.name}</h3>
                    {product.description && (
                      <p className="text-sm text-gray-600 line-clamp-2">{product.description}</p>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      <span className="text-2xl font-bold text-gray-800">${product.price.toFixed(2)}</span>
                      {cart[product.id] && (
                        <span className="text-sm text-green-600 font-medium">
                          {cart[product.id]} in cart
                        </span>
                      )}
                    </div>

                    <button
                      onClick={() => addToCart(product.id)}
                      disabled={product.inStock === false}
                      className={`flex items-center px-4 py-2 rounded-xl font-medium transition-all ${
                        product.inStock !== false
                          ? 'bg-gradient-to-r from-green-600 to-blue-600 text-white hover:from-green-700 hover:to-blue-700 hover:scale-105'
                          : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      {product.inStock !== false ? 'Add to Cart' : 'Out of Stock'}
                    </button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-full">
              <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20 p-12 text-center">
                <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-800 mb-2">No Products Available</h3>
                <p className="text-gray-600 mb-6">
                  {selectedCategory === 'all'
                    ? "This store doesn't have any products yet."
                    : `No products found in the "${selectedCategory}" category.`}
                </p>
                {selectedCategory !== 'all' && (
                  <button
                    onClick={() => setSelectedCategory('all')}
                    className="px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all"
                  >
                    View All Products
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
