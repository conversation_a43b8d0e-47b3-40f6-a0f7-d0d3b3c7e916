'use client';

import { Book, CheckCircle, Calendar, CreditCard, MessageCircle, Star } from 'lucide-react';
import Link from 'next/link';

export default function BookingGuidePage() {
  const steps = [
    {
      icon: Book,
      title: "Browse Services",
      description: "Search for pet services in your area using our filters",
      details: [
        "Use the search bar to find services by location",
        "Filter by service type (grooming, walking, sitting, etc.)",
        "Check availability and pricing",
        "Read provider profiles and reviews"
      ]
    },
    {
      icon: Star,
      title: "Choose a Provider",
      description: "Select the perfect provider for your pet's needs",
      details: [
        "Review provider ratings and reviews",
        "Check their experience and certifications",
        "Look at their service offerings",
        "Verify their availability for your preferred dates"
      ]
    },
    {
      icon: Calendar,
      title: "Schedule Your Service",
      description: "Pick your preferred date and time",
      details: [
        "Select from available time slots",
        "Choose service duration if applicable",
        "Add any special instructions for your pet",
        "Confirm your contact information"
      ]
    },
    {
      icon: MessageCircle,
      title: "Provide Pet Details",
      description: "Share important information about your pet",
      details: [
        "Add your pet's basic information (name, breed, age)",
        "Include any medical conditions or allergies",
        "Share behavioral notes and preferences",
        "Upload recent photos if requested"
      ]
    },
    {
      icon: CreditCard,
      title: "Complete Payment",
      description: "Secure payment processing",
      details: [
        "Review service details and pricing",
        "Choose your payment method",
        "Apply any available discounts or credits",
        "Confirm your booking"
      ]
    },
    {
      icon: CheckCircle,
      title: "Confirmation & Follow-up",
      description: "Your booking is confirmed!",
      details: [
        "Receive booking confirmation via email",
        "Get provider contact information",
        "Set up any pre-service communication",
        "Prepare for your scheduled service"
      ]
    }
  ];

  const tips = [
    {
      title: "Book in Advance",
      description: "Popular providers fill up quickly, especially on weekends. Book at least 48 hours ahead for best availability."
    },
    {
      title: "Be Detailed",
      description: "The more information you provide about your pet, the better service they'll receive. Include any quirks or preferences."
    },
    {
      title: "Prepare Your Pet",
      description: "Make sure your pet is ready for the service - up-to-date on vaccinations and comfortable with new people."
    },
    {
      title: "Stay Available",
      description: "Keep your phone handy during the service in case the provider needs to contact you with questions."
    }
  ];

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-6">
              <div className="p-4 bg-white/20 rounded-full">
                <Book className="w-12 h-12" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Booking Guide
            </h1>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Step-by-step instructions to book the perfect pet service on Fetchly
            </p>
          </div>
        </div>
      </div>

      {/* Steps Section */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              How to Book a Service
            </h2>
            
            <div className="space-y-8">
              {steps.map((step, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-8 relative">
                  <div className="flex items-start space-x-6">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                        {index + 1}
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-4">
                        <step.icon className="w-6 h-6 text-blue-600" />
                        <h3 className="text-2xl font-bold text-gray-900">{step.title}</h3>
                      </div>
                      <p className="text-lg text-gray-600 mb-6">{step.description}</p>
                      <ul className="space-y-2">
                        {step.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-start space-x-3">
                            <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700">{detail}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Tips Section */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Pro Tips for Successful Bookings
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {tips.map((tip, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{tip.title}</h3>
                  <p className="text-gray-600">{tip.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
              <h2 className="text-3xl font-bold mb-4">Ready to Book Your First Service?</h2>
              <p className="text-xl text-blue-100 mb-8">
                Browse our trusted providers and find the perfect match for your pet
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/providers" className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                  Browse Providers
                </Link>
                <Link href="/help" className="bg-white/20 text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/30 transition-colors">
                  Back to Help Center
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
