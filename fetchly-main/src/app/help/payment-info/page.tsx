'use client';

import { CreditCard, Shield, DollarSign, RefreshCw, AlertCircle, CheckCircle, Smartphone, Lock } from 'lucide-react';
import Link from 'next/link';

export default function PaymentInfoPage() {
  const paymentMethods = [
    {
      icon: CreditCard,
      title: "Credit & Debit Cards",
      description: "Visa, Mastercard, American Express, Discover",
      features: ["Instant processing", "Secure encryption", "Fraud protection"]
    },
    {
      icon: Smartphone,
      title: "Digital Wallets",
      description: "Apple Pay, Google Pay, Samsung Pay",
      features: ["One-tap payments", "Biometric security", "No card details shared"]
    },
    {
      icon: DollarSign,
      title: "Fetchly Wallet",
      description: "Pre-loaded credits for faster checkout",
      features: ["Instant payments", "Bonus credits", "Easy refunds"]
    }
  ];

  const securityFeatures = [
    {
      icon: Lock,
      title: "256-bit SSL Encryption",
      description: "All payment data is encrypted using bank-level security"
    },
    {
      icon: Shield,
      title: "PCI DSS Compliant",
      description: "We meet the highest standards for payment card security"
    },
    {
      icon: CheckCircle,
      title: "Fraud Protection",
      description: "Advanced fraud detection protects your transactions"
    }
  ];

  const billingFaqs = [
    {
      question: "When am I charged for a service?",
      answer: "Payment is processed when you confirm your booking. For recurring services, you'll be charged before each appointment."
    },
    {
      question: "Can I get a refund if I cancel?",
      answer: "Refund policies vary by provider and service type. Generally, cancellations 24+ hours in advance are eligible for full refunds."
    },
    {
      question: "Are there any additional fees?",
      answer: "Fetchly charges a small service fee (typically 3-5%) to cover payment processing and platform maintenance. This is clearly shown before payment."
    },
    {
      question: "How do I update my payment method?",
      answer: "Go to your Account Settings > Payment Methods to add, remove, or update your payment information."
    },
    {
      question: "What if my payment fails?",
      answer: "You'll receive an email notification and can retry with a different payment method. Your booking will be held for 24 hours."
    },
    {
      question: "Do you store my payment information?",
      answer: "We use secure tokenization - your actual card details are never stored on our servers, only encrypted tokens for future use."
    }
  ];

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-green-50 via-white to-blue-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-6">
              <div className="p-4 bg-white/20 rounded-full">
                <CreditCard className="w-12 h-12" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Payment Information
            </h1>
            <p className="text-xl text-green-100 max-w-2xl mx-auto">
              Everything you need to know about payments, billing, and security on Fetchly
            </p>
          </div>
        </div>
      </div>

      {/* Payment Methods */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Accepted Payment Methods
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {paymentMethods.map((method, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-8 text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center text-white mx-auto mb-6">
                    <method.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{method.title}</h3>
                  <p className="text-gray-600 mb-6">{method.description}</p>
                  <ul className="space-y-2">
                    {method.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center justify-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="text-sm text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Security Section */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Your Payment Security
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              {securityFeatures.map((feature, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-6 text-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center text-white mx-auto mb-4">
                    <feature.icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>

            <div className="bg-blue-50 rounded-2xl p-8 text-center">
              <Shield className="w-16 h-16 text-blue-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-gray-900 mb-4">100% Secure Payments</h3>
              <p className="text-lg text-gray-700 max-w-2xl mx-auto">
                We partner with industry-leading payment processors to ensure your financial information 
                is always protected with the highest security standards.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Billing & Payment FAQs
            </h2>
            
            <div className="space-y-6">
              {billingFaqs.map((faq, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{faq.question}</h3>
                  <p className="text-gray-700">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Support CTA */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl p-8 text-white">
              <h2 className="text-3xl font-bold mb-4">Need Help with Payments?</h2>
              <p className="text-xl text-green-100 mb-8">
                Our support team is here to help with any payment or billing questions
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact" className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                  Contact Support
                </Link>
                <Link href="/help" className="bg-white/20 text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/30 transition-colors">
                  Back to Help Center
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
