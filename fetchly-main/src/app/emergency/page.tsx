'use client';

import { useState } from 'react';
import { Phone, MapPin, Clock, AlertTriangle, Heart, Zap, Shield, Navigation } from 'lucide-react';
import Link from 'next/link';

// Puerto Rico Emergency Veterinary Hospitals Database - REAL CLINICS ONLY
const puertoRicoVetHospitals = [
  // Aguada - AMC Puerto Rico
  {
    id: 1,
    name: "AMC Puerto Rico",
    phone: "(*************",
    address: "Ave. Nativo Alers, Desvío Sur Carr. 417 Km 0.3, Aguada, PR 00602",
    city: "Aguada",
    zipCode: "00602",
    state: "PR",
    specialties: ["Emergency Medicine", "Critical Care", "Surgery", "Internal Medicine"],
    website: "https://www.amcpr.org/"
  },
  // San Juan - Veterinaria 24-7
  {
    id: 2,
    name: "Veterinaria 24-7",
    phone: "(*************",
    address: "270 A Ave. Jesús T. Piñero, San Juan PR 00927",
    city: "San Juan",
    zipCode: "00927",
    state: "PR",
    specialties: ["Emergency Care", "24/7 Service", "Critical Care", "Trauma"],
    website: "https://www.veterinaria24-7.com/"
  },
  // Ponce - Ponce Animal Hospital
  {
    id: 3,
    name: "Ponce Animal Hospital",
    phone: "(*************",
    address: "3096 Emilio Fagot Avenue, Ponce, Puerto Rico 00716",
    city: "Ponce",
    zipCode: "00716",
    state: "PR",
    specialties: ["Veterinary Care", "Emergency Services", "Surgery", "Preventive Care"],
    website: "https://ponceanimalhospitalpr.com/"
  }
];

const emergencyTips = [
  {
    title: "Choking",
    description: "Open mouth, remove visible objects, perform rescue breathing if needed",
    image: "/choking.svg",
    color: "warm"
  },
  {
    title: "Poisoning",
    description: "Contact poison control immediately, do not induce vomiting unless instructed",
    image: "/poisoning.svg",
    color: "secondary"
  },
  {
    title: "Bleeding",
    description: "Apply direct pressure with clean cloth, elevate if possible",
    image: "/bleeding.svg",
    color: "primary"
  },
  {
    title: "Seizures",
    description: "Keep pet safe from injury, time the seizure, stay calm",
    image: "/seizures.svg",
    color: "accent"
  }
];

export default function EmergencyPage() {
  const [userLocation, setUserLocation] = useState('');
  const [emergencyType, setEmergencyType] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);

  const handleEmergencyCall = (phone: string) => {
    window.location.href = `tel:${phone}`;
  };

  const handleEmergencySearch = () => {
    if (!zipCode.trim()) {
      alert('Please enter a zip code to search for emergency hospitals in Puerto Rico');
      return;
    }

    // Filter by specific zip code
    let filtered = puertoRicoVetHospitals.filter(hospital =>
      hospital.zipCode === zipCode.trim()
    );

    // If no exact zip match, show all Puerto Rico hospitals
    if (filtered.length === 0) {
      filtered = puertoRicoVetHospitals;
    }

    setSearchResults(filtered);
  };

  const getDirections = (address: string) => {
    const encodedAddress = encodeURIComponent(address);

    // Detect if user is on iOS device
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

    if (isIOS) {
      // Open Apple Maps on iOS devices
      window.open(`http://maps.apple.com/?q=${encodedAddress}`, '_blank');
    } else {
      // Open Google Maps on other devices
      window.open(`https://www.google.com/maps/search/?api=1&query=${encodedAddress}`, '_blank');
    }
  };

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-r from-green-600 to-blue-600">
      {/* Hero Section */}
      <section className="py-12 relative overflow-hidden">
        <div className="absolute top-10 left-10 w-24 h-24 bg-gradient-to-r from-green-400/30 to-blue-400/30 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-gradient-to-r from-blue-400/30 to-green-400/30 rounded-full blur-xl animate-pulse" style={{ animationDelay: '1s' }}></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h1 className="text-3xl md:text-5xl font-bold mb-4 text-white">
              <span className="text-yellow-300">Emergency</span> Pet Care
            </h1>
            <p className="text-xl text-green-100 max-w-2xl mx-auto mb-8">
              Get immediate help for your pet in critical situations. Available 24/7.
            </p>
          </div>

          {/* Emergency Hospital Search */}
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 max-w-2xl mx-auto border border-white/20">
            <h3 className="text-xl font-bold text-gray-800 mb-4 text-center">
              Search for Emergency Hospital
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Emergency Type</label>
                <select
                  value={emergencyType}
                  onChange={(e) => setEmergencyType(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 text-gray-800"
                >
                  <option value="">Select emergency type</option>
                  <option value="choking">Choking</option>
                  <option value="poisoning">Poisoning</option>
                  <option value="bleeding">Severe Bleeding</option>
                  <option value="seizure">Seizure</option>
                  <option value="trauma">Trauma/Injury</option>
                  <option value="breathing">Breathing Problems</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Zip Code</label>
                <input
                  type="text"
                  placeholder="Enter your zip code"
                  value={zipCode}
                  onChange={(e) => setZipCode(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 text-gray-800 placeholder-gray-500"
                />
              </div>
            </div>
            <button
              onClick={handleEmergencySearch}
              className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 w-full mt-4"
            >
              Search Hospital
            </button>
          </div>
        </div>
      </section>

      {/* Emergency Clinics Results */}
      {searchResults.length > 0 && (
        <section id="find-clinic" className="py-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {searchResults.map((clinic) => (
              <div key={clinic.id} className="bg-white rounded-2xl p-6 hover:shadow-xl transition-all duration-300 border-l-4 border-green-500 shadow-lg flex flex-col h-full">
                {/* Header */}
                <div className="mb-4">
                  <h3 className="text-xl font-bold text-gray-800 mb-2">{clinic.name}</h3>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <MapPin className="w-4 h-4 text-green-600" />
                    <span>{clinic.city}, Puerto Rico</span>
                  </div>
                </div>

                {/* Contact Info */}
                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <Phone className="w-4 h-4 text-green-600" />
                    <span className="font-medium">{clinic.phone}</span>
                  </div>
                </div>

                {/* Address */}
                <div className="mb-4">
                  <p className="text-sm text-gray-600 leading-relaxed">{clinic.address}</p>
                </div>

                {/* Specialties */}
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-800 mb-2">Services:</h4>
                  <div className="flex flex-wrap gap-1">
                    {clinic.specialties.map((specialty: string, index: number) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full font-medium">
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Website */}
                {clinic.website && (
                  <div className="mb-4">
                    <a
                      href={clinic.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:text-blue-800 underline"
                    >
                      Visit Website
                    </a>
                  </div>
                )}

                {/* Spacer to push buttons to bottom */}
                <div className="flex-1"></div>
                
                {/* Action Buttons */}
                <div className="flex gap-2 mt-4">
                  <button
                    onClick={() => handleEmergencyCall(clinic.phone)}
                    className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-4 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2 flex-1"
                  >
                    <Phone className="w-4 h-4" />
                    Call Now
                  </button>
                  <button
                    onClick={() => getDirections(clinic.address)}
                    className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-4 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2 flex-1"
                  >
                    <Navigation className="w-4 h-4" />
                    Get Directions
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      )}

      {/* Emergency Tips */}
      <section className="py-16 relative">
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-white">
              Emergency First Aid Tips
            </h2>
            <p className="text-xl text-green-100">
              Quick actions that could save your pet's life
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {emergencyTips.map((tip, index) => {
              return (
                <div key={index} className="bg-white rounded-2xl p-6 text-center hover:shadow-xl transition-all duration-300 group border border-gray-100">
                  <div className="w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300" 
                       style={{
                         backgroundColor: 
                           tip.color === 'primary' ? '#3B82F6' : 
                           tip.color === 'secondary' ? '#8B5CF6' : 
                           tip.color === 'accent' ? '#EC4899' : 
                           '#F59E0B',
                         color: 'white'
                       }}>
                    <img
                      src={tip.image}
                      alt={tip.title}
                      className="w-10 h-10"
                    />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-gray-800">{tip.title}</h3>
                  <p className="text-gray-600 text-sm">{tip.description}</p>
                </div>
              );
            })}
          </div>

          <div className="text-center mt-12">
            <div className="glass-card rounded-2xl p-6 max-w-2xl mx-auto">
              <h3 className="text-xl font-bold text-cool-800 mb-4">
                ⚠️ Important Reminder
              </h3>
              <p className="text-cool-600">
                These tips are for emergency situations only. Always contact a veterinary professional 
                immediately for proper medical care. Do not attempt treatments beyond your expertise.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Emergency Preparedness */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4 text-cool-800">
                Emergency Preparedness Kit
              </h2>
              <p className="text-xl text-cool-600">
                Be prepared for emergencies with these essential items
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-3">
                <h4 className="font-bold text-cool-800">Medical Supplies</h4>
                <ul className="space-y-2 text-cool-600">
                  <li>• Gauze pads and bandages</li>
                  <li>• Antiseptic wipes</li>
                  <li>• Digital thermometer</li>
                  <li>• Hydrogen peroxide</li>
                  <li>• Emergency medications</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-bold text-cool-800">Important Information</h4>
                <ul className="space-y-2 text-cool-600">
                  <li>• Vet contact numbers</li>
                  <li>• Pet medical records</li>
                  <li>• Emergency clinic locations</li>
                  <li>• Pet insurance info</li>
                  <li>• Poison control number</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-bold text-cool-800">Emergency Tools</h4>
                <ul className="space-y-2 text-cool-600">
                  <li>• Pet carrier or crate</li>
                  <li>• Leash and collar</li>
                  <li>• Flashlight</li>
                  <li>• Blankets</li>
                  <li>• Emergency food/water</li>
                </ul>
              </div>
            </div>


          </div>
        </div>
      </section>
    </div>
  );
}
