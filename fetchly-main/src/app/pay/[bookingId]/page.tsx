'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { CreditCard, Lock, Calendar, Clock, User, PawPrint, DollarSign } from 'lucide-react';
import toast from 'react-hot-toast';
import { apiClient } from '@/lib/api-client';

interface BookingData {
  id: string;
  providerName: string;
  serviceName: string;
  petName: string;
  scheduledDate: string;
  scheduledTime: string;
  finalPrice?: number;
  totalPrice: number;
  status: string;
  paymentStatus: string;
  userId: string;
}

function PaymentForm({ booking }: { booking: BookingData }) {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState('');

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsProcessing(true);
    setPaymentError('');

    try {
      console.log('🎯 Creating Stripe Checkout session...');

      // Create Stripe Checkout session
      const response = await apiClient.post('/api/payments/create-checkout', {
        bookingId: booking.id,
      });

      if (response.success) {
        console.log('✅ Checkout session created, redirecting...');
        // Redirect to Stripe Checkout
        window.location.href = response.checkoutUrl;
      } else {
        setPaymentError(response.error || 'Failed to create checkout session');
      }
    } catch (error: any) {
      console.error('❌ Checkout error:', error);
      setPaymentError(error.message || 'Payment processing failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const amount = booking.finalPrice || booking.totalPrice;

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <CreditCard className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Complete Your Payment</h1>
          <p className="text-gray-600">Secure payment for your pet service booking</p>
        </div>

        {/* Booking Summary */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Booking Summary</h2>
          
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <User className="w-5 h-5 text-gray-500" />
              <span className="text-gray-700">Provider: <strong>{booking.providerName}</strong></span>
            </div>
            
            <div className="flex items-center gap-3">
              <PawPrint className="w-5 h-5 text-gray-500" />
              <span className="text-gray-700">Service: <strong>{booking.serviceName}</strong></span>
            </div>
            
            <div className="flex items-center gap-3">
              <PawPrint className="w-5 h-5 text-gray-500" />
              <span className="text-gray-700">Pet: <strong>{booking.petName}</strong></span>
            </div>
            
            <div className="flex items-center gap-3">
              <Calendar className="w-5 h-5 text-gray-500" />
              <span className="text-gray-700">Date: <strong>{booking.scheduledDate}</strong></span>
            </div>
            
            <div className="flex items-center gap-3">
              <Clock className="w-5 h-5 text-gray-500" />
              <span className="text-gray-700">Time: <strong>{booking.scheduledTime}</strong></span>
            </div>
            
            <div className="flex items-center gap-3 pt-3 border-t">
              <DollarSign className="w-5 h-5 text-green-600" />
              <span className="text-lg font-semibold text-green-600">Total: ${amount.toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* Payment Form */}
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center gap-2 mb-6">
            <Lock className="w-5 h-5 text-green-600" />
            <h2 className="text-xl font-semibold text-gray-800">Secure Payment</h2>
          </div>

          {paymentError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-700 text-sm">{paymentError}</p>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
                <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">Secure Checkout</h3>
                <p className="text-gray-600 text-sm">
                  You'll be redirected to our secure payment processor to complete your purchase.
                </p>
              </div>
            </div>

            <button
              type="submit"
              disabled={isProcessing}
              className={`w-full py-4 px-6 rounded-xl font-semibold text-white transition-all duration-300 ${
                isProcessing
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 shadow-lg hover:shadow-xl'
              }`}
            >
              {isProcessing ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Redirecting to Checkout...
                </div>
              ) : (
                `Pay $${amount.toFixed(2)} - Secure Checkout`
              )}
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              🔒 Your payment information is secure and encrypted. 
              We use Stripe for secure payment processing.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function PaymentPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [booking, setBooking] = useState<BookingData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const bookingId = params.bookingId as string;

  useEffect(() => {
    const fetchBooking = async () => {
      if (!isAuthenticated || !user) {
        router.push('/auth/signin');
        return;
      }

      try {
        const bookingRef = doc(db, 'bookings', bookingId);
        const bookingDoc = await getDoc(bookingRef);

        if (!bookingDoc.exists()) {
          setError('Booking not found');
          return;
        }

        const bookingData = { id: bookingDoc.id, ...bookingDoc.data() } as BookingData;

        // Verify user owns this booking
        if (bookingData.userId !== user.id) {
          setError('Unauthorized access');
          return;
        }

        // Check if already paid
        if (bookingData.paymentStatus === 'paid') {
          router.push(`/booking-success/${bookingId}`);
          return;
        }

        setBooking(bookingData);
      } catch (error) {
        console.error('Error fetching booking:', error);
        setError('Failed to load booking details');
      } finally {
        setLoading(false);
      }
    };

    fetchBooking();
  }, [bookingId, isAuthenticated, user, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading payment details...</p>
        </div>
      </div>
    );
  }

  if (error || !booking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error || 'Booking not found'}</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Go to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return <PaymentForm booking={booking} />;
}
