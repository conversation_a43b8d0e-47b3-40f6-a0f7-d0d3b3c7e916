'use client';

import { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { collection, query, where, orderBy, onSnapshot, addDoc, doc, getDoc, updateDoc, limit, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Send, ArrowLeft, User, Phone, Video, MoreVertical, Smile, Paperclip, Mic, Search, Info } from 'lucide-react';
import { toast } from 'react-hot-toast';
import Link from 'next/link';

interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  message: string;
  timestamp: any;
  read: boolean;
  senderName: string;
  senderEmail: string;
}

interface ChatUser {
  id: string;
  name: string;
  email: string;
  profileImage?: string;
  role?: string;
  isOnline?: boolean;
  lastSeen?: any;
}

interface RecentChat {
  id: string;
  otherUser: ChatUser;
  lastMessage: string;
  lastMessageTime: any;
  unreadCount: number;
  isOnline: boolean;
}

export default function ChatPage() {
  const { user } = useAuth();
  const searchParams = useSearchParams();
  const chatId = searchParams.get('id');
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [otherUser, setOtherUser] = useState<ChatUser | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [recentChats, setRecentChats] = useState<RecentChat[]>([]);
  const [onlineFriends, setOnlineFriends] = useState<ChatUser[]>([]);
  const [sidebarTab, setSidebarTab] = useState<'chats' | 'friends'>('chats');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Typing indicator component
  const TypingIndicator = () => (
    <div className="flex justify-start mb-3">
      <div className="flex items-end space-x-2 max-w-xs lg:max-w-md">
        <div className="w-8 h-8 rounded-full flex-shrink-0">
          {otherUser?.profileImage ? (
            <img src={otherUser.profileImage} alt={otherUser.name} className="w-8 h-8 rounded-full object-cover" />
          ) : (
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
          )}
        </div>
        <div className="bg-white text-gray-900 rounded-2xl rounded-bl-md border border-gray-200 px-4 py-3 shadow-sm">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    </div>
  );

  // Load real data from Firebase
  useEffect(() => {
    if (!user?.id) return;

    // Load recent chats from Firebase
    const loadRecentChats = async () => {
      try {
        // Simplified queries to avoid index requirements
        const messagesQuery1 = query(
          collection(db, 'messages'),
          where('senderId', '==', user.id),
          limit(25)
        );

        const messagesQuery2 = query(
          collection(db, 'messages'),
          where('receiverId', '==', user.id),
          limit(25)
        );

        const chatMap = new Map<string, RecentChat>();

        const processMessages = async (snapshot: any) => {
          for (const docSnapshot of snapshot.docs) {
            const messageData = docSnapshot.data();
            const otherUserId = messageData.senderId === user.id ? messageData.receiverId : messageData.senderId;

            if (!chatMap.has(otherUserId)) {
              // Get other user's data
              let otherUserData = null;
              try {
                // Try users collection first
                let userRef = doc(db, 'users', otherUserId);
                let userDoc = await getDoc(userRef);

                if (!userDoc.exists()) {
                  // Try providers collection
                  userRef = doc(db, 'providers', otherUserId);
                  userDoc = await getDoc(userRef);
                }

                if (userDoc.exists()) {
                  const userData = userDoc.data();
                  otherUserData = {
                    id: otherUserId,
                    name: userData.name || userData.businessName || 'Unknown User',
                    email: userData.email || '',
                    profileImage: userData.profileImage || userData.profilePicture,
                    role: userData.role || userData.userType || 'user',
                    isOnline: userData.isOnline || false,
                    lastSeen: userData.lastSeen
                  };
                }
              } catch (error) {
                console.error('Error loading user data:', error);
              }

              if (otherUserData) {
                chatMap.set(otherUserId, {
                  id: `${user.id}_${otherUserId}`,
                  otherUser: otherUserData,
                  lastMessage: messageData.message || '',
                  lastMessageTime: messageData.timestamp,
                  unreadCount: 0, // TODO: Implement unread count
                  isOnline: otherUserData.isOnline || false
                });
              }
            }
          }
        };

        const unsubscribe1 = onSnapshot(messagesQuery1, processMessages);
        const unsubscribe2 = onSnapshot(messagesQuery2, async (snapshot) => {
          await processMessages(snapshot);
          setRecentChats(Array.from(chatMap.values()));
        });

        return () => {
          unsubscribe1();
          unsubscribe2();
        };
      } catch (error) {
        console.error('Error loading recent chats:', error);
      }
    };

    // Load online friends from Firebase
    const loadOnlineFriends = async () => {
      try {
        // Get all online users from both collections
        const onlineFriendsData: ChatUser[] = [];

        // Query online users from users collection
        const usersQuery = query(
          collection(db, 'users'),
          where('isOnline', '==', true)
        );

        const usersSnapshot = await getDocs(usersQuery);
        usersSnapshot.forEach((doc) => {
          const userData = doc.data();
          if (doc.id !== user.id) { // Don't include current user
            onlineFriendsData.push({
              id: doc.id,
              name: userData.name || 'Unknown User',
              email: userData.email || '',
              profileImage: userData.profileImage,
              role: userData.role || 'pet_owner',
              isOnline: true,
              lastSeen: userData.lastSeen
            });
          }
        });

        // Query online providers from providers collection
        const providersQuery = query(
          collection(db, 'providers'),
          where('isOnline', '==', true)
        );

        const providersSnapshot = await getDocs(providersQuery);
        providersSnapshot.forEach((doc) => {
          const providerData = doc.data();
          if (doc.id !== user.id) { // Don't include current user
            onlineFriendsData.push({
              id: doc.id,
              name: providerData.businessName || providerData.name || 'Unknown Provider',
              email: providerData.email || '',
              profileImage: providerData.profilePicture || providerData.profileImage,
              role: providerData.userType || 'provider',
              isOnline: true,
              lastSeen: providerData.lastSeen
            });
          }
        });

        setOnlineFriends(onlineFriendsData);
      } catch (error) {
        console.error('Error loading online friends:', error);
        // Fallback: show some online users even if query fails
        setOnlineFriends([]);
      }
    };

    loadRecentChats();
    loadOnlineFriends();
  }, [user?.id]);

  // Load chat messages
  useEffect(() => {
    if (!user?.id || !chatId) return;

    setLoading(true);

    // Query messages for this chat
    const q = query(
      collection(db, 'messages'),
      where('chatId', '==', chatId),
      orderBy('timestamp', 'asc')
    );

    const unsubscribe = onSnapshot(q, async (snapshot) => {
      const messagesData: Message[] = [];
      let otherUserId: string | null = null;

      snapshot.forEach((doc) => {
        const data = doc.data();
        const message: Message = {
          id: doc.id,
          senderId: data.senderId,
          receiverId: data.receiverId,
          message: data.message,
          timestamp: data.timestamp,
          read: data.read || false,
          senderName: data.senderName || 'Unknown User',
          senderEmail: data.senderEmail || ''
        };
        messagesData.push(message);

        // Determine the other user
        if (data.senderId !== user.id) {
          otherUserId = data.senderId;
        } else if (data.receiverId !== user.id) {
          otherUserId = data.receiverId;
        }
      });

      setMessages(messagesData);

      // Load other user's info
      if (otherUserId && !otherUser) {
        try {
          // Try users collection first
          let userRef = doc(db, 'users', otherUserId);
          let userDoc = await getDoc(userRef);
          
          if (!userDoc.exists()) {
            // Try providers collection
            userRef = doc(db, 'providers', otherUserId);
            userDoc = await getDoc(userRef);
          }

          if (userDoc.exists()) {
            const userData = userDoc.data();
            setOtherUser({
              id: otherUserId,
              name: userData.name || userData.businessName || 'Unknown User',
              email: userData.email || '',
              profileImage: userData.profileImage || userData.profilePicture,
              role: userData.role || userData.userType || 'user'
            });
          }
        } catch (error) {
          console.error('Error loading other user:', error);
        }
      }

      setLoading(false);
    });

    return () => unsubscribe();
  }, [user?.id, chatId, otherUser]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newMessage.trim() || !user?.id || !chatId || sending) return;

    setSending(true);

    try {
      await addDoc(collection(db, 'messages'), {
        chatId,
        senderId: user.id,
        receiverId: otherUser?.id || '',
        message: newMessage.trim(),
        timestamp: new Date(),
        read: false,
        senderName: user.name || 'Unknown User',
        senderEmail: user.email || ''
      });

      setNewMessage('');
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = '24px';
      }
      toast.success('Message sent!');
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setSending(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Please log in</h2>
          <p className="text-gray-600">You need to be logged in to access chat.</p>
          <Link href="/auth/signin" className="mt-4 inline-block bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  if (!chatId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Invalid Chat</h2>
          <p className="text-gray-600">No chat ID provided.</p>
          <Link href="/messages" className="mt-4 inline-block bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
            Back to Messages
          </Link>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading chat...</span>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-100 flex">
      {/* Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        {/* Sidebar Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <Link href="/dashboard" className="p-2 hover:bg-gray-100 rounded-full transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <h1 className="text-xl font-bold text-gray-900">Messages</h1>
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
              <Search className="w-5 h-5 text-gray-600" />
            </button>
          </div>

          {/* Tab Switcher */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setSidebarTab('chats')}
              className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                sidebarTab === 'chats'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Recent Chats
            </button>
            <button
              onClick={() => setSidebarTab('friends')}
              className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                sidebarTab === 'friends'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Online Friends
            </button>
          </div>
        </div>

        {/* Sidebar Content */}
        <div className="flex-1 overflow-y-auto">
          {sidebarTab === 'chats' ? (
            /* Recent Chats */
            <div className="p-2">
              {recentChats.length === 0 ? (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Send className="w-8 h-8 text-gray-400" />
                  </div>
                  <p className="text-gray-500 text-sm">No recent chats</p>
                </div>
              ) : (
                recentChats.map((chat) => (
                  <Link
                    key={chat.id}
                    href={`/chat?id=${chat.id}`}
                    className={`flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors ${
                      chatId === chat.id ? 'bg-blue-50 border border-blue-200' : ''
                    }`}
                  >
                    <div className="relative">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        {chat.otherUser.profileImage ? (
                          <img src={chat.otherUser.profileImage} alt={chat.otherUser.name} className="w-12 h-12 rounded-full object-cover" />
                        ) : (
                          <User className="w-6 h-6 text-white" />
                        )}
                      </div>
                      {chat.isOnline && (
                        <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                      )}
                    </div>
                    <div className="ml-3 flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 truncate">{chat.otherUser.name}</p>
                        <p className="text-xs text-gray-500">
                          {chat.lastMessageTime?.toDate?.()?.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) || ''}
                        </p>
                      </div>
                      <div className="flex items-center justify-between">
                        <p className="text-sm text-gray-500 truncate">{chat.lastMessage}</p>
                        {chat.unreadCount > 0 && (
                          <span className="bg-blue-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center">
                            {chat.unreadCount}
                          </span>
                        )}
                      </div>
                    </div>
                  </Link>
                ))
              )}
            </div>
          ) : (
            /* Online Friends */
            <div className="p-2">
              {onlineFriends.length === 0 ? (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <User className="w-8 h-8 text-gray-400" />
                  </div>
                  <p className="text-gray-500 text-sm">No friends online</p>
                </div>
              ) : (
                onlineFriends.map((friend) => (
                  <Link
                    key={friend.id}
                    href={`/chat?id=${friend.id}`}
                    className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="relative">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        {friend.profileImage ? (
                          <img src={friend.profileImage} alt={friend.name} className="w-12 h-12 rounded-full object-cover" />
                        ) : (
                          <User className="w-6 h-6 text-white" />
                        )}
                      </div>
                      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                    </div>
                    <div className="ml-3 flex-1">
                      <p className="text-sm font-medium text-gray-900">{friend.name}</p>
                      <p className="text-xs text-green-600">Online now</p>
                    </div>
                  </Link>
                ))
              )}
            </div>
          )}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {chatId ? (
          <>
            {/* Chat Header */}
            <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4 flex-shrink-0">
              <div className="flex items-center justify-between">
                {otherUser && (
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        {otherUser.profileImage ? (
                          <img src={otherUser.profileImage} alt={otherUser.name} className="w-10 h-10 rounded-full object-cover" />
                        ) : (
                          <User className="w-5 h-5 text-white" />
                        )}
                      </div>
                      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                    </div>
                    <div>
                      <h1 className="text-lg font-semibold text-gray-900">{otherUser.name}</h1>
                      <p className="text-sm text-green-600">Online now</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
                    <Phone className="w-5 h-5 text-gray-600" />
                  </button>
                  <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
                    <Video className="w-5 h-5 text-gray-600" />
                  </button>
                  <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
                    <Info className="w-5 h-5 text-gray-600" />
                  </button>
                </div>
              </div>
            </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-hidden bg-gray-50">
        <div className="h-full overflow-y-auto px-4 py-4 space-y-3" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f3f4f6' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}>
          {messages.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <Send className="w-10 h-10 text-blue-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">Start the conversation</h3>
                <p className="text-gray-500 max-w-sm">Send a message to begin chatting with {otherUser?.name || 'this user'}.</p>
              </div>
            </div>
          ) : (
            <>
              {messages.map((message, index) => {
                const isOwn = message.senderId === user.id;
                const showAvatar = !isOwn && (index === 0 || messages[index - 1].senderId !== message.senderId);
                const showTime = index === messages.length - 1 ||
                  messages[index + 1].senderId !== message.senderId ||
                  (message.timestamp?.toDate?.() && messages[index + 1]?.timestamp?.toDate?.() &&
                   Math.abs(message.timestamp.toDate().getTime() - messages[index + 1].timestamp.toDate().getTime()) > 300000); // 5 minutes

                return (
                  <div key={message.id} className={`flex ${isOwn ? 'justify-end' : 'justify-start'} mb-1`}>
                    <div className={`flex items-end space-x-2 max-w-xs lg:max-w-md ${isOwn ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      {/* Avatar for other user */}
                      {!isOwn && (
                        <div className={`w-8 h-8 rounded-full flex-shrink-0 ${showAvatar ? 'visible' : 'invisible'}`}>
                          {otherUser?.profileImage ? (
                            <img src={otherUser.profileImage} alt={otherUser.name} className="w-8 h-8 rounded-full object-cover" />
                          ) : (
                            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                              <User className="w-4 h-4 text-white" />
                            </div>
                          )}
                        </div>
                      )}

                      {/* Message bubble */}
                      <div className={`relative px-4 py-2 rounded-2xl shadow-sm ${
                        isOwn
                          ? 'bg-blue-500 text-white rounded-br-md'
                          : 'bg-white text-gray-900 rounded-bl-md border border-gray-200'
                      }`}>
                        <p className="text-sm leading-relaxed">{message.message}</p>

                        {/* Timestamp and status */}
                        {showTime && (
                          <div className={`flex items-center justify-end mt-1 space-x-1 ${isOwn ? 'text-blue-100' : 'text-gray-500'}`}>
                            <p className="text-xs">
                              {message.timestamp?.toDate?.()?.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) || 'Just now'}
                            </p>
                            {isOwn && (
                              <div className="flex space-x-0.5">
                                <div className="w-3 h-3 text-blue-100">
                                  <svg viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
                                  </svg>
                                </div>
                                <div className="w-3 h-3 text-blue-100">
                                  <svg viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
                                  </svg>
                                </div>
                              </div>
                            )}
                          </div>
                        )}

                        {/* Message tail */}
                        <div className={`absolute bottom-0 ${
                          isOwn
                            ? 'right-0 transform translate-x-1 translate-y-1'
                            : 'left-0 transform -translate-x-1 translate-y-1'
                        }`}>
                          <div className={`w-3 h-3 transform rotate-45 ${
                            isOwn ? 'bg-blue-500' : 'bg-white border-r border-b border-gray-200'
                          }`}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}

              {/* Typing indicator */}
              {isTyping && <TypingIndicator />}

              <div ref={messagesEndRef} />
            </>
          )}
        </div>
      </div>

      {/* Modern Message Input */}
      <div className="bg-white border-t border-gray-200 px-4 py-3 flex-shrink-0">
        <form onSubmit={handleSendMessage} className="flex items-end space-x-3">
          {/* Attachment button */}
          <button
            type="button"
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors"
          >
            <Paperclip className="w-5 h-5" />
          </button>

          {/* Message input container */}
          <div className="flex-1 relative">
            <div className="flex items-end bg-gray-100 rounded-2xl px-4 py-2 min-h-[44px]">
              <textarea
                ref={textareaRef}
                value={newMessage}
                onChange={(e) => {
                  setNewMessage(e.target.value);
                  // Auto-resize textarea
                  const target = e.target as HTMLTextAreaElement;
                  target.style.height = 'auto';
                  target.style.height = Math.min(target.scrollHeight, 128) + 'px';
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage(e);
                  }
                }}
                placeholder="Type a message..."
                className="flex-1 bg-transparent resize-none outline-none text-gray-900 placeholder-gray-500 max-h-32 min-h-[24px] py-1"
                rows={1}
                disabled={sending}
                style={{
                  height: '24px',
                  maxHeight: '128px'
                }}
              />

              {/* Emoji button */}
              <button
                type="button"
                className="ml-2 p-1 text-gray-500 hover:text-gray-700 rounded-full transition-colors"
              >
                <Smile className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Send/Voice button */}
          {newMessage.trim() ? (
            <button
              type="submit"
              disabled={sending}
              className="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-lg"
            >
              {sending ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                <Send className="w-5 h-5" />
              )}
            </button>
          ) : (
            <button
              type="button"
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors"
            >
              <Mic className="w-5 h-5" />
            </button>
          )}
        </form>
      </div>
          </>
        ) : (
          /* No Chat Selected */
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Send className="w-12 h-12 text-blue-500" />
              </div>
              <h2 className="text-2xl font-semibold text-gray-700 mb-2">Welcome to Fetchly Chat</h2>
              <p className="text-gray-500 max-w-md">
                Select a conversation from the sidebar to start chatting with other pet owners and service providers.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
