import React from 'react';
import Link from 'next/link';
import { FileText, Shield, Cookie, Eye, AlertTriangle, Scale } from 'lucide-react';

export default function LegalPage() {
  const legalDocuments = [
    {
      title: 'Privacy Policy',
      description: 'Learn how we collect, use, and protect your personal information in compliance with federal and state privacy laws.',
      icon: Shield,
      href: '/legal/privacy-policy',
      lastUpdated: 'January 1, 2025',
      highlights: ['CCPA Compliance', 'Data Protection', 'User Rights', 'Information Security']
    },
    {
      title: 'Terms of Service',
      description: 'Understand the terms and conditions governing your use of the Fetchly platform and services.',
      icon: FileText,
      href: '/legal/terms-of-service',
      lastUpdated: 'January 1, 2025',
      highlights: ['User Responsibilities', 'Service Limitations', 'Dispute Resolution', 'Legal Compliance']
    },
    {
      title: 'Cookie Policy',
      description: 'Detailed information about how we use cookies and similar tracking technologies on our platform.',
      icon: <PERSON><PERSON>,
      href: '/legal/cookie-policy',
      lastUpdated: 'January 1, 2025',
      highlights: ['Cookie Types', 'Third-Party Cookies', 'Opt-Out Options', 'Privacy Controls']
    },
    {
      title: 'Accessibility Statement',
      description: 'Our commitment to making our platform accessible to all users, including those with disabilities.',
      icon: Eye,
      href: '/legal/accessibility',
      lastUpdated: 'January 1, 2025',
      highlights: ['WCAG 2.1 AA', 'Assistive Technology', 'Accessibility Features', 'Support Resources']
    },
    {
      title: 'Legal Disclaimer',
      description: 'Important disclaimers regarding the use of our platform and the limitations of our services.',
      icon: AlertTriangle,
      href: '/legal/disclaimer',
      lastUpdated: 'January 1, 2025',
      highlights: ['Service Limitations', 'Medical Disclaimers', 'Platform Nature', 'User Responsibilities']
    }
  ];

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-gray-50 via-primary-50 to-accent-50">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <Scale className="w-12 h-12 text-primary-600 mr-4" />
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                Legal Information
              </h1>
            </div>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto">
              Comprehensive legal documentation designed to protect both Fetchly and our users 
              across all 50 states and territories. Our legal framework ensures compliance with 
              federal and state regulations while providing clear guidelines for platform use.
            </p>
          </div>

          {/* Legal Documents Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            {legalDocuments.map((doc, index) => {
              const IconComponent = doc.icon;
              return (
                <div key={index} className="glass-card rounded-3xl p-8 hover:shadow-2xl transition-all duration-300 group">
                  <div className="flex items-start mb-6">
                    <div className="flex-shrink-0 mr-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-900 mb-2 group-hover:text-primary-700 transition-colors">
                        {doc.title}
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">
                        Last Updated: {doc.lastUpdated}
                      </p>
                    </div>
                  </div>

                  <p className="text-gray-700 mb-6 leading-relaxed">
                    {doc.description}
                  </p>

                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-gray-800 mb-3">Key Topics:</h4>
                    <div className="flex flex-wrap gap-2">
                      {doc.highlights.map((highlight, idx) => (
                        <span 
                          key={idx}
                          className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium"
                        >
                          {highlight}
                        </span>
                      ))}
                    </div>
                  </div>

                  <Link 
                    href={doc.href}
                    className="inline-flex items-center justify-center w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white py-3 px-6 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl group-hover:scale-105"
                  >
                    Read {doc.title}
                    <FileText className="w-4 h-4 ml-2" />
                  </Link>
                </div>
              );
            })}
          </div>

          {/* Legal Summary */}
          <div className="glass-card rounded-3xl p-8 mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-6 text-center">
              Legal Framework Overview
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Privacy Protection</h3>
                <p className="text-gray-700">
                  Comprehensive privacy protections compliant with CCPA, VCDPA, CPA, CTDPA, UCPA, and other state privacy laws.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Scale className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Legal Compliance</h3>
                <p className="text-gray-700">
                  Full compliance with federal and state regulations across all 50 states and U.S. territories.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Eye className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Accessibility</h3>
                <p className="text-gray-700">
                  WCAG 2.1 Level AA compliance ensuring equal access for all users, including those with disabilities.
                </p>
              </div>
            </div>
          </div>

          {/* Important Notice */}
          <div className="bg-gradient-to-r from-amber-50 to-orange-50 border-l-4 border-amber-400 p-6 rounded-lg mb-8">
            <div className="flex">
              <AlertTriangle className="w-6 h-6 text-amber-600 mr-3 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-lg font-semibold text-amber-800 mb-2">Important Legal Notice</h3>
                <p className="text-amber-700 mb-4">
                  These legal documents constitute binding agreements between you and Fetchly Pet Services, LLC. 
                  By using our platform, you acknowledge that you have read, understood, and agree to be bound 
                  by these terms.
                </p>
                <p className="text-amber-700 text-sm">
                  <strong>Effective Date:</strong> All documents are effective as of January 1, 2025. 
                  We recommend reviewing these documents periodically as they may be updated to reflect 
                  changes in law or our business practices.
                </p>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="glass-card rounded-3xl p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Legal Questions?</h2>
            <p className="text-gray-700 mb-6">
              If you have any questions about our legal documents or need clarification on any terms, 
              please don't hesitate to contact our legal team.
            </p>
            
            <div className="bg-gray-100 rounded-xl p-6 max-w-md mx-auto">
              <h3 className="font-semibold text-gray-900 mb-3">Contact Us</h3>
              <div className="space-y-2 text-sm text-gray-700">
                <p><strong>Email:</strong> <EMAIL></p>
              </div>
              <p className="mt-3 text-sm text-gray-500">
                We typically respond within 1-2 business days.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
