import React from 'react';

export default function AccessibilityPage() {
  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-gray-50 via-primary-50 to-accent-50">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <div className="glass-card rounded-3xl p-8 md:p-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">
              Accessibility Statement
            </h1>
            
            <div className="prose prose-lg max-w-none text-gray-800">
              <p className="text-sm text-gray-600 mb-8">
                <strong>Effective Date:</strong> January 1, 2025<br />
                <strong>Last Updated:</strong> January 1, 2025
              </p>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">1. OUR COMMITMENT TO ACCESSIBILITY</h2>
                <p className="mb-4">
                  Fetchly Pet Services, LLC ("Fetchly," "we," "us," or "our") is committed to ensuring that 
                  our website, mobile applications, and digital services (collectively, the "Platform") are 
                  accessible to all users, including individuals with disabilities. We believe that everyone 
                  should have equal access to information and functionality, and we are dedicated to providing 
                  an inclusive digital experience.
                </p>
                <p className="mb-4">
                  This Accessibility Statement outlines our ongoing efforts to improve accessibility and 
                  usability for all users, in compliance with applicable accessibility standards and regulations.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">2. ACCESSIBILITY STANDARDS</h2>
                <p className="mb-4">
                  We strive to conform to the Web Content Accessibility Guidelines (WCAG) 2.1 Level AA, 
                  published by the World Wide Web Consortium (W3C). These guidelines provide a framework 
                  for making web content more accessible to people with disabilities, including:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Visual impairments (blindness, low vision, color blindness)</li>
                  <li>Hearing impairments (deafness, hard of hearing)</li>
                  <li>Motor impairments (limited fine motor control, paralysis)</li>
                  <li>Cognitive impairments (learning disabilities, memory impairments)</li>
                </ul>
                <p className="mb-4">
                  We also comply with applicable federal and state accessibility laws, including:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Americans with Disabilities Act (ADA)</li>
                  <li>Section 508 of the Rehabilitation Act</li>
                  <li>State accessibility laws and regulations</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">3. ACCESSIBILITY FEATURES</h2>
                <p className="mb-4">
                  Our Platform includes the following accessibility features:
                </p>
                
                <h3 className="text-xl font-semibold text-gray-800 mb-3">3.1 Keyboard Navigation</h3>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Full keyboard navigation support for all interactive elements</li>
                  <li>Logical tab order throughout the Platform</li>
                  <li>Visible focus indicators for keyboard users</li>
                  <li>Skip navigation links to bypass repetitive content</li>
                </ul>

                <h3 className="text-xl font-semibold text-gray-800 mb-3">3.2 Screen Reader Compatibility</h3>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Semantic HTML markup for proper content structure</li>
                  <li>Alternative text for images and graphics</li>
                  <li>Descriptive link text and button labels</li>
                  <li>ARIA labels and landmarks for enhanced navigation</li>
                  <li>Proper heading hierarchy (H1-H6)</li>
                </ul>

                <h3 className="text-xl font-semibold text-gray-800 mb-3">3.3 Visual Design</h3>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>High contrast color schemes meeting WCAG AA standards</li>
                  <li>Scalable text that can be enlarged up to 200% without loss of functionality</li>
                  <li>Clear visual hierarchy and consistent layout</li>
                  <li>Color is not the only means of conveying information</li>
                  <li>Sufficient spacing between interactive elements</li>
                </ul>

                <h3 className="text-xl font-semibold text-gray-800 mb-3">3.4 Forms and Input</h3>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Clear and descriptive form labels</li>
                  <li>Error messages that are clearly identified and explained</li>
                  <li>Required fields are clearly marked</li>
                  <li>Input format requirements are specified</li>
                  <li>Form validation with accessible error handling</li>
                </ul>

                <h3 className="text-xl font-semibold text-gray-800 mb-3">3.5 Multimedia Content</h3>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Captions for video content</li>
                  <li>Transcripts for audio content</li>
                  <li>Audio descriptions where appropriate</li>
                  <li>Controls for auto-playing media</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">4. ASSISTIVE TECHNOLOGY COMPATIBILITY</h2>
                <p className="mb-4">
                  Our Platform is designed to work with a variety of assistive technologies, including:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li><strong>Screen Readers:</strong> JAWS, NVDA, VoiceOver, TalkBack</li>
                  <li><strong>Voice Recognition Software:</strong> Dragon NaturallySpeaking</li>
                  <li><strong>Keyboard Alternatives:</strong> Switch devices, eye-tracking systems</li>
                  <li><strong>Browser Extensions:</strong> High contrast modes, text-to-speech tools</li>
                  <li><strong>Mobile Accessibility:</strong> iOS VoiceOver, Android TalkBack</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">5. BROWSER AND DEVICE SUPPORT</h2>
                <p className="mb-4">
                  Our Platform is optimized for accessibility across modern browsers and devices:
                </p>
                
                <h3 className="text-xl font-semibold text-gray-800 mb-3">5.1 Supported Browsers</h3>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Chrome (latest 2 versions)</li>
                  <li>Firefox (latest 2 versions)</li>
                  <li>Safari (latest 2 versions)</li>
                  <li>Microsoft Edge (latest 2 versions)</li>
                </ul>

                <h3 className="text-xl font-semibold text-gray-800 mb-3">5.2 Mobile Devices</h3>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>iOS devices with VoiceOver support</li>
                  <li>Android devices with TalkBack support</li>
                  <li>Responsive design for various screen sizes</li>
                  <li>Touch-friendly interface elements</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">6. ONGOING ACCESSIBILITY EFFORTS</h2>
                <p className="mb-4">
                  We are continuously working to improve the accessibility of our Platform through:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li><strong>Regular Audits:</strong> Conducting accessibility audits with automated tools and manual testing</li>
                  <li><strong>User Testing:</strong> Engaging users with disabilities in our testing process</li>
                  <li><strong>Staff Training:</strong> Providing accessibility training for our development and design teams</li>
                  <li><strong>Third-Party Assessment:</strong> Working with accessibility experts for independent evaluations</li>
                  <li><strong>Continuous Monitoring:</strong> Implementing automated accessibility testing in our development process</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">7. KNOWN LIMITATIONS</h2>
                <p className="mb-4">
                  While we strive for full accessibility, we acknowledge that some areas of our Platform 
                  may not yet meet all accessibility standards. We are actively working to address these 
                  limitations, which may include:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Some third-party content or widgets that may not be fully accessible</li>
                  <li>Certain interactive features that are being updated for better accessibility</li>
                  <li>Legacy content that is being progressively updated</li>
                </ul>
                <p className="mb-4">
                  If you encounter any accessibility barriers, please contact us so we can work to resolve 
                  the issue and improve your experience.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">8. ACCESSIBILITY TOOLS AND RESOURCES</h2>
                <p className="mb-4">
                  To enhance your experience on our Platform, you may find these tools and resources helpful:
                </p>
                
                <h3 className="text-xl font-semibold text-gray-800 mb-3">8.1 Browser Accessibility Features</h3>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li><strong>Zoom:</strong> Use Ctrl/Cmd + Plus (+) to increase text size</li>
                  <li><strong>High Contrast:</strong> Enable high contrast mode in your browser settings</li>
                  <li><strong>Text-to-Speech:</strong> Use built-in browser reading tools</li>
                  <li><strong>Keyboard Navigation:</strong> Use Tab to navigate, Enter to activate</li>
                </ul>

                <h3 className="text-xl font-semibold text-gray-800 mb-3">8.2 Operating System Features</h3>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li><strong>Windows:</strong> Narrator, Magnifier, High Contrast themes</li>
                  <li><strong>macOS:</strong> VoiceOver, Zoom, Increase Contrast</li>
                  <li><strong>iOS:</strong> VoiceOver, Zoom, AssistiveTouch</li>
                  <li><strong>Android:</strong> TalkBack, Magnification, High contrast text</li>
                </ul>

                <h3 className="text-xl font-semibold text-gray-800 mb-3">8.3 Third-Party Tools</h3>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Screen readers (JAWS, NVDA, VoiceOver)</li>
                  <li>Voice recognition software (Dragon NaturallySpeaking)</li>
                  <li>Browser extensions for accessibility enhancement</li>
                  <li>Alternative keyboards and input devices</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">9. FEEDBACK AND SUPPORT</h2>
                <p className="mb-4">
                  We welcome your feedback on the accessibility of our Platform. If you:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Encounter accessibility barriers while using our Platform</li>
                  <li>Have suggestions for improving accessibility</li>
                  <li>Need assistance accessing any content or functionality</li>
                  <li>Would like to request content in an alternative format</li>
                </ul>
                <p className="mb-4">
                  Please contact our accessibility team using the information provided below. We will 
                  respond to accessibility feedback within 5 business days and work to resolve any 
                  issues as quickly as possible.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">10. ALTERNATIVE ACCESS METHODS</h2>
                <p className="mb-4">
                  If you are unable to access certain features or content on our Platform due to 
                  accessibility barriers, we offer alternative methods to access our services:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li><strong>Phone Support:</strong> Call our customer service team for assistance with bookings and account management</li>
                  <li><strong>Email Support:</strong> Send us an email with your request, and we'll assist you directly</li>
                  <li><strong>Alternative Formats:</strong> We can provide information in alternative formats upon request</li>
                  <li><strong>Personal Assistance:</strong> Our team can help you complete tasks that may be difficult to access online</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">11. LEGAL COMPLIANCE</h2>
                <p className="mb-4">
                  This Accessibility Statement demonstrates our commitment to compliance with applicable 
                  accessibility laws and regulations, including:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li><strong>Americans with Disabilities Act (ADA):</strong> Title III requirements for public accommodations</li>
                  <li><strong>Section 508:</strong> Federal accessibility standards for electronic and information technology</li>
                  <li><strong>State Laws:</strong> Various state accessibility requirements and regulations</li>
                  <li><strong>International Standards:</strong> WCAG 2.1 Level AA compliance</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">12. UPDATES TO THIS STATEMENT</h2>
                <p className="mb-4">
                  We will update this Accessibility Statement as we make improvements to our Platform 
                  and as accessibility standards evolve. We encourage you to check this page periodically 
                  for updates on our accessibility efforts.
                </p>
                <p className="mb-4">
                  Major updates to this statement will be announced through our usual communication 
                  channels, and the "Last Updated" date at the top of this page will reflect any changes.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">13. CONTACT INFORMATION</h2>
                <p className="mb-4">
                  For accessibility-related questions, feedback, or to request assistance, please contact us:
                </p>
                <div className="bg-gray-100 p-4 rounded-lg">
                  <p><strong>Fetchly Pet Services, LLC</strong></p>
                  <p><strong>Accessibility Team</strong></p>
                  <p>Email: <EMAIL></p>
                  <p>Phone: 1-800-FETCHLY (TTY available)</p>
                  <p>Address: [Company Address]</p>
                  <p>Business Hours: Monday-Friday, 9 AM - 6 PM EST</p>
                </div>
                <p className="mt-4">
                  When contacting us about accessibility, please include:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>A description of the accessibility barrier you encountered</li>
                  <li>The web page or feature where you experienced the issue</li>
                  <li>Your browser and assistive technology information</li>
                  <li>Your contact information for follow-up</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">14. ACCESSIBILITY RESOURCES</h2>
                <p className="mb-4">
                  For more information about web accessibility and assistive technologies, visit:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li><a href="https://www.w3.org/WAI/" className="text-primary-600 hover:underline">Web Accessibility Initiative (WAI)</a></li>
                  <li><a href="https://www.ada.gov/" className="text-primary-600 hover:underline">Americans with Disabilities Act (ADA)</a></li>
                  <li><a href="https://webaim.org/" className="text-primary-600 hover:underline">WebAIM - Web Accessibility In Mind</a></li>
                  <li><a href="https://www.section508.gov/" className="text-primary-600 hover:underline">Section 508.gov</a></li>
                </ul>
              </section>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
