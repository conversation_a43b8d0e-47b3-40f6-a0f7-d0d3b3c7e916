'use client';

import { Moon, Sun } from 'lucide-react';
import { useDarkMode } from '@/contexts/DarkModeContext';

interface DarkModeToggleProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export default function DarkModeToggle({ className = '', size = 'md' }: DarkModeToggleProps) {
  const { isDarkMode, toggleDarkMode } = useDarkMode();

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  return (
    <button
      onClick={toggleDarkMode}
      className={`
        ${sizeClasses[size]}
        relative rounded-full transition-all duration-300 ease-in-out
        ${isDarkMode 
          ? 'bg-dark-navy border-2 border-dark-electric text-dark-electric hover:bg-dark-electric hover:text-dark-navy' 
          : 'bg-white border-2 border-gray-300 text-gray-600 hover:bg-gray-100 hover:border-gray-400'
        }
        flex items-center justify-center
        shadow-lg hover:shadow-xl
        transform hover:scale-110
        ${className}
      `}
      title={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      <div className="relative">
        {isDarkMode ? (
          <Sun className={`${iconSizes[size]} transition-all duration-300`} />
        ) : (
          <Moon className={`${iconSizes[size]} transition-all duration-300`} />
        )}
      </div>
    </button>
  );
}
