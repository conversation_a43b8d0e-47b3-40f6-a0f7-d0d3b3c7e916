'use client';

import { useCallback, useEffect, useState } from 'react';
import { usePlaidLink } from 'react-plaid-link';
import { createLinkToken, exchangePublicToken, BankAccount } from '@/lib/plaid';
import { CreditCard, Plus, AlertCircle } from 'lucide-react';
import toast from 'react-hot-toast';

interface PlaidLinkProps {
  userId: string;
  onSuccess: (accounts: BankAccount[]) => void;
  onError?: (error: any) => void;
  className?: string;
  children?: React.ReactNode;
}

export default function PlaidLink({ 
  userId, 
  onSuccess, 
  onError, 
  className = '',
  children 
}: PlaidLinkProps) {
  const [linkToken, setLinkToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create link token when component mounts
  useEffect(() => {
    const initializePlaid = async () => {
      try {
        setIsLoading(true);
        const tokenData = await createLinkToken(userId);
        setLinkToken(tokenData.link_token);
      } catch (err) {
        console.error('Error initializing Plaid:', err);
        setError('Failed to initialize bank connection');
        onError?.(err);
      } finally {
        setIsLoading(false);
      }
    };

    if (userId) {
      initializePlaid();
    }
  }, [userId, onError]);

  // Handle successful Plaid Link
  const onPlaidSuccess = useCallback(async (public_token: string, metadata: any) => {
    try {
      setIsLoading(true);
      toast.loading('Connecting your bank account...');
      
      const accounts = await exchangePublicToken(public_token, userId);
      
      toast.dismiss();
      toast.success('Bank account connected successfully!');
      onSuccess(accounts);
    } catch (err) {
      console.error('Error exchanging public token:', err);
      toast.dismiss();
      toast.error('Failed to connect bank account');
      onError?.(err);
    } finally {
      setIsLoading(false);
    }
  }, [userId, onSuccess, onError]);

  // Handle Plaid Link errors
  const onPlaidError = useCallback((error: any) => {
    console.error('Plaid Link error:', error);
    toast.error('Bank connection failed');
    onError?.(error);
  }, [onError]);

  // Handle Plaid Link exit
  const onPlaidExit = useCallback((err: any, metadata: any) => {
    if (err) {
      console.error('Plaid Link exit error:', err);
    }
  }, []);

  // Initialize Plaid Link
  const { open, ready } = usePlaidLink({
    token: linkToken,
    onSuccess: onPlaidSuccess,
    onError: onPlaidError,
    onExit: onPlaidExit,
  });

  // Handle button click
  const handleClick = useCallback(() => {
    if (ready && linkToken) {
      open();
    }
  }, [ready, linkToken, open]);

  if (error) {
    return (
      <div className={`flex items-center space-x-2 text-red-600 ${className}`}>
        <AlertCircle className="w-4 h-4" />
        <span className="text-sm">{error}</span>
      </div>
    );
  }

  return (
    <button
      onClick={handleClick}
      disabled={!ready || isLoading || !linkToken}
      className={`flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
    >
      {isLoading ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          <span>Connecting...</span>
        </>
      ) : children ? (
        children
      ) : (
        <>
          <CreditCard className="w-4 h-4" />
          <span>Connect Bank Account</span>
        </>
      )}
    </button>
  );
}

// Bank Account Display Component
interface BankAccountDisplayProps {
  account: BankAccount;
  onRemove?: (accountId: string) => void;
  onSetPrimary?: (accountId: string) => void;
  isPrimary?: boolean;
  className?: string;
}

export function BankAccountDisplay({ 
  account, 
  onRemove, 
  onSetPrimary, 
  isPrimary = false,
  className = '' 
}: BankAccountDisplayProps) {
  return (
    <div className={`bg-gray-50 rounded-lg p-4 border ${isPrimary ? 'border-blue-500 bg-blue-50' : 'border-gray-200'} ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <CreditCard className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <p className="font-medium text-gray-800">
              {account.name} ****{account.mask}
            </p>
            <p className="text-sm text-gray-600">
              {account.institution_name} • {account.subtype}
            </p>
            {isPrimary && (
              <span className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full mt-1">
                Primary Account
              </span>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {!isPrimary && onSetPrimary && (
            <button
              onClick={() => onSetPrimary(account.id)}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Set Primary
            </button>
          )}
          {onRemove && (
            <button
              onClick={() => onRemove(account.id)}
              className="text-red-600 hover:text-red-700 text-sm font-medium"
            >
              Remove
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

// Payout Form Component
interface PayoutFormProps {
  accounts: BankAccount[];
  availableBalance: number;
  onPayout: (accountId: string, amount: number) => void;
  isLoading?: boolean;
}

export function PayoutForm({ accounts, availableBalance, onPayout, isLoading = false }: PayoutFormProps) {
  const [selectedAccount, setSelectedAccount] = useState<string>('');
  const [amount, setAmount] = useState<number>(0);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedAccount && amount > 0 && amount <= availableBalance) {
      onPayout(selectedAccount, amount);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Bank Account
        </label>
        <select
          value={selectedAccount}
          onChange={(e) => setSelectedAccount(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          required
        >
          <option value="">Choose an account</option>
          {accounts.map((account) => (
            <option key={account.id} value={account.id}>
              {account.name} ****{account.mask} - {account.institution_name}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Amount (Available: ${availableBalance.toFixed(2)})
        </label>
        <input
          type="number"
          min="1"
          max={availableBalance}
          step="0.01"
          value={amount}
          onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="0.00"
          required
        />
      </div>

      <button
        type="submit"
        disabled={!selectedAccount || amount <= 0 || amount > availableBalance || isLoading}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? 'Processing...' : `Request Payout ($${amount.toFixed(2)})`}
      </button>
    </form>
  );
}
