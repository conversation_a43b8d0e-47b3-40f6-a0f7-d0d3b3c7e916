'use client';

import { useState, useMemo } from 'react';
import { useProvider } from '@/contexts/ProviderContext';
import { 
  Search, 
  Filter, 
  Users, 
  Phone, 
  Mail, 
  Calendar, 
  DollarSign,
  Star,
  MoreVertical,
  Eye,
  MessageCircle,
  User,
  Heart,
  Clock
} from 'lucide-react';
import { Booking } from '@/lib/firebase/providers';
import { Timestamp } from 'firebase/firestore';

interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  pets: Array<{
    name: string;
    type: string;
    breed?: string;
    age?: number;
  }>;
  totalBookings: number;
  totalSpent: number;
  lastBooking?: Date;
  averageRating?: number;
  status: 'active' | 'inactive';
  joinDate: Date;
}

export default function ClientsTab() {
  const { bookings, isLoading } = useProvider();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  // Process bookings to create client data
  const clients = useMemo(() => {
    if (!bookings) return [];

    const clientMap = new Map<string, Client>();

    bookings.forEach(booking => {
      const clientKey = booking.customerEmail;
      
      if (!clientMap.has(clientKey)) {
        clientMap.set(clientKey, {
          id: clientKey,
          name: booking.customerName,
          email: booking.customerEmail,
          phone: booking.customerPhone,
          pets: [],
          totalBookings: 0,
          totalSpent: 0,
          status: 'active',
          joinDate: booking.createdAt instanceof Timestamp ? booking.createdAt.toDate() : new Date(booking.createdAt)
        });
      }

      const client = clientMap.get(clientKey)!;
      client.totalBookings++;
      client.totalSpent += booking.totalAmount;

      // Add pet if not already exists
      const petExists = client.pets.some(pet => 
        pet.name === booking.petName && pet.type === booking.petType
      );
      
      if (!petExists) {
        client.pets.push({
          name: booking.petName,
          type: booking.petType,
          breed: booking.petBreed
        });
      }

      // Update last booking date
      const bookingDate = booking.date instanceof Timestamp ? booking.date.toDate() : new Date(booking.date);
      if (!client.lastBooking || bookingDate > client.lastBooking) {
        client.lastBooking = bookingDate;
      }
    });

    return Array.from(clientMap.values());
  }, [bookings]);

  // Filter clients
  const filteredClients = useMemo(() => {
    return clients.filter(client => {
      const searchMatch = searchTerm === '' || 
        client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.pets.some(pet => pet.name.toLowerCase().includes(searchTerm.toLowerCase()));

      const statusMatch = statusFilter === 'all' || client.status === statusFilter;

      return searchMatch && statusMatch;
    });
  }, [clients, searchTerm, statusFilter]);

  const getClientBookings = (clientEmail: string): Booking[] => {
    return bookings?.filter(booking => booking.customerEmail === clientEmail) || [];
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Filters */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Client Management</h2>
            <p className="text-gray-600">Manage your client relationships and history</p>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="text-sm text-gray-600">
              <span className="font-semibold">{filteredClients.length}</span> clients
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search clients by name, email, or pet name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Clients</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>

      {/* Client Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Clients</p>
              <p className="text-2xl font-bold text-gray-800">{clients.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Clients</p>
              <p className="text-2xl font-bold text-gray-800">
                {clients.filter(c => c.status === 'active').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <Heart className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Bookings</p>
              <p className="text-2xl font-bold text-gray-800">
                {clients.length > 0 ? Math.round(clients.reduce((sum, c) => sum + c.totalBookings, 0) / clients.length) : 0}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <Calendar className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-800">
                ${clients.reduce((sum, c) => sum + c.totalSpent, 0).toFixed(0)}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Clients List */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
        {filteredClients.length === 0 ? (
          <div className="text-center py-12">
            <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">No clients found</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your filters to see more results.'
                : 'Your clients will appear here once you start receiving bookings.'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredClients.map((client) => (
              <div key={client.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                      <User className="w-6 h-6 text-gray-600" />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-800">{client.name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(client.status)}`}>
                          {client.status}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                        <div className="flex items-center space-x-2">
                          <Mail className="w-4 h-4" />
                          <span>{client.email}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Phone className="w-4 h-4" />
                          <span>{client.phone}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4" />
                          <span>{client.totalBookings} bookings</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <DollarSign className="w-4 h-4" />
                          <span>${client.totalSpent.toFixed(0)} spent</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Pets: </span>
                          {client.pets.map((pet, index) => (
                            <span key={index}>
                              {pet.name} ({pet.type})
                              {index < client.pets.length - 1 ? ', ' : ''}
                            </span>
                          ))}
                        </div>
                        {client.lastBooking && (
                          <div>
                            <span className="font-medium">Last booking: </span>
                            {formatDate(client.lastBooking)}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                      <MessageCircle className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => setSelectedClient(client)}
                      className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      <Eye className="w-5 h-5" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                      <MoreVertical className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Client Details Modal */}
      {selectedClient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-800">Client Details</h3>
              <button
                onClick={() => setSelectedClient(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Client Info */}
              <div className="lg:col-span-1 space-y-6">
                <div className="bg-gray-50 rounded-xl p-4">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                      <User className="w-8 h-8 text-gray-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-800">{selectedClient.name}</h4>
                      <p className="text-sm text-gray-600">{selectedClient.email}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Phone:</span>
                      <span className="font-medium">{selectedClient.phone}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Bookings:</span>
                      <span className="font-medium">{selectedClient.totalBookings}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Spent:</span>
                      <span className="font-medium">${selectedClient.totalSpent.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Client Since:</span>
                      <span className="font-medium">{formatDate(selectedClient.joinDate)}</span>
                    </div>
                  </div>
                </div>
                
                {/* Pets */}
                <div>
                  <h5 className="font-medium text-gray-800 mb-3">Pets</h5>
                  <div className="space-y-2">
                    {selectedClient.pets.map((pet, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-3">
                        <p className="font-medium text-gray-800">{pet.name}</p>
                        <p className="text-sm text-gray-600">{pet.type} {pet.breed && `• ${pet.breed}`}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Booking History */}
              <div className="lg:col-span-2">
                <h5 className="font-medium text-gray-800 mb-4">Booking History</h5>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {getClientBookings(selectedClient.email).map((booking) => (
                    <div key={booking.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <p className="font-medium text-gray-800">{booking.petName} - {booking.petType}</p>
                          <p className="text-sm text-gray-600">
                            {booking.date instanceof Timestamp 
                              ? booking.date.toDate().toLocaleDateString() 
                              : new Date(booking.date).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-800">${booking.totalAmount}</p>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            booking.status === 'completed' ? 'bg-green-100 text-green-800' :
                            booking.status === 'confirmed' ? 'bg-blue-100 text-blue-800' :
                            booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {booking.status}
                          </span>
                        </div>
                      </div>
                      {booking.notes && (
                        <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">{booking.notes}</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
              <button
                onClick={() => setSelectedClient(null)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Close
              </button>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Send Message
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
