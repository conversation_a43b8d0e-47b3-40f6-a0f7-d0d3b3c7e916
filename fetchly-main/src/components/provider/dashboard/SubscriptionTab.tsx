"use client";

import { useState, useEffect } from 'react';
import { useProvider } from '@/contexts/ProviderContext';
import { toast } from 'react-hot-toast';
import { Crown, Lock, Shield, Check } from 'lucide-react';
import { SubscriptionService } from '@/lib/stripe/subscription-service';
import type { ProviderSubscription, SubscriptionTier } from '@/lib/stripe/client-config';
import { SUBSCRIPTION_TIERS } from '@/lib/stripe/client-config';

type MembershipTier = 'free' | 'pro' | 'premium';

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  features: string[];
  isPopular?: boolean;
  isCurrent?: boolean;
}

export default function SubscriptionTab() {
  const { provider } = useProvider();
  const [subscription, setSubscription] = useState<ProviderSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [isUpgrading, setIsUpgrading] = useState(false);
  
  // Get current subscription status with proper type assertion
  const currentTier: MembershipTier = (provider?.membershipTier as MembershipTier) || 'free';
  const isFreeTier = currentTier === 'free';
  const isProTier = currentTier === 'pro';
  const isPremiumTier = currentTier === 'premium';
  const isProMember = isProTier || isPremiumTier;

  // Subscription plans data - Everything is free for now
  const subscriptionPlans: SubscriptionPlan[] = [
    {
      id: 'free',
      name: 'Fetchly Free',
      price: 0,
      features: [
        'Unlimited bookings',
        'Full analytics dashboard',
        'Email & chat support',
        'Complete profile listing',
        'Calendar integration',
        'Payment processing',
        'Customer reviews',
        'All platform features'
      ],
      isCurrent: true
    }
  ];
  // Removed Pro and Premium plans - everything is free for now

  useEffect(() => {
    // Everything is free, so just set loading to false
    setLoading(false);
  }, [provider]);

  // No upgrade needed since everything is free
  const handleUpgrade = async () => {
    // Everything is free for now
    toast.success('All features are currently free! Enjoy unlimited access to the platform.');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const handleCancelSubscription = async () => {
    if (!provider?.id || !subscription) return;

    if (!confirm('Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.')) {
      return;
    }

    try {
      const result = await SubscriptionService.cancelSubscription(provider.id, true);
      if (result.success) {
        toast.success('Subscription cancelled. You will retain access until the end of your billing period.');
        // Since everything is free now, no need to reload subscription data
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast.error('Failed to cancel subscription. Please try again.');
    }
  };

  return (
    <div className="space-y-8">
      {/* Current Status */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 rounded-2xl flex items-center justify-center bg-gradient-to-br from-green-500 to-blue-600">
              <Crown className="w-8 h-8 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-800">
                Fetchly Free - All Features Included
              </h2>
              <p className="text-gray-600">
                Enjoy unlimited access to all platform features at no cost
              </p>
              <p className="text-sm text-green-600 mt-1 font-medium">
                ✨ Everything is free during our launch period!
              </p>
            </div>
          </div>
          <div className="bg-green-100 text-green-800 px-4 py-2 rounded-xl font-semibold">
            Active Plan
          </div>
        </div>
      </div>

      {/* Current Plan Features */}
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-2xl p-8 border-2 border-green-500 shadow-lg">
          <div className="absolute -top-3 left-6 bg-gradient-to-r from-green-600 to-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">
            Your Plan
          </div>

          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Crown className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-2">Fetchly Free</h3>
            <p className="text-gray-600 mb-4">
              All features included during our launch period
            </p>
            <div className="text-4xl font-bold text-green-600">
              FREE<span className="text-lg text-gray-500"></span>
            </div>
            <p className="text-xs text-gray-500 mt-2"></p>
          </div>

          <ul className="space-y-3 mb-8">
            {subscriptionPlans[0].features.map((feature, index) => (
              <li key={index} className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-600 flex-shrink-0" />
                <span className="text-gray-700">{feature}</span>
              </li>
            ))}
          </ul>

          <div className="bg-green-100 text-green-800 py-3 rounded-xl font-semibold text-center">
            ✨ All Features Active
          </div>
        </div>
      </div>

      {/* Launch Period Information */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8 border border-green-200">
        <h3 className="text-xl font-bold text-gray-800 mb-4 text-center">🚀 Launch Period Benefits</h3>
        <div className="text-center">
          <p className="text-gray-700 mb-4">
            During our launch period, all providers get access to premium features at no cost.
            This includes unlimited bookings, advanced analytics, priority support, and more!
          </p>
          <div className="bg-white rounded-xl p-4 inline-block">
            <p className="text-green-600 font-semibold">
              ✨ Save $29.99/month during launch period
            </p>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="bg-gray-50 rounded-2xl p-8">
        <h3 className="text-xl font-bold text-gray-800 mb-6">Frequently Asked Questions</h3>
        <div className="space-y-4">
          {[
            {
              q: "How long will the free period last?",
              a: "We're committed to keeping all features free during our launch period. We'll provide plenty of advance notice before introducing any paid plans."
            },
            {
              q: "Are there any hidden fees?",
              a: "No hidden fees! Everything is completely free during our launch period. Payment processing fees may apply for transactions."
            },
            {
              q: "What happens to my data?",
              a: "Your data is always safe and secure. All your bookings, analytics, and customer information will remain intact regardless of any future plan changes."
            },
            {
              q: "Will I lose features when paid plans are introduced?",
              a: "We'll always maintain a generous free tier. Early adopters like you will receive special benefits when we introduce paid plans."
            }
          ].map((faq, index) => (
            <div key={index} className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-2">{faq.q}</h4>
              <p className="text-gray-600 text-sm">{faq.a}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
