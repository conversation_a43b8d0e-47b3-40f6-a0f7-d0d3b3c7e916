'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { collection, query, where, orderBy, onSnapshot, doc, updateDoc, getDoc, addDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Calendar, Clock, User, DollarSign, Phone, Mail, MessageSquare, Check, X, PawPrint, HelpCircle } from 'lucide-react';
import { toast } from 'react-hot-toast';
import FeedbackModal from '@/components/modals/FeedbackModal';

interface Booking {
  id: string;
  providerId: string;
  userId: string;
  userName: string;
  userEmail: string;
  userPhone?: string;
  serviceName: string;
  petName: string;
  petType: string;
  scheduledDate: string;
  scheduledTime: string;
  duration: number;
  totalPrice: number;
  notes: string;
  status: 'pending_provider_approval' | 'accepted' | 'rejected' | 'completed' | 'cancelled';
  createdAt: any;
  updatedAt: any;
}

export default function BookingsTab() {
  const { user } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('all');
  const [respondingTo, setRespondingTo] = useState<string | null>(null);
  const [paymentAmounts, setPaymentAmounts] = useState<{[key: string]: string}>({});
  const [showPaymentInput, setShowPaymentInput] = useState<{[key: string]: boolean}>({});
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);

  // Load bookings from Firebase in real-time
  useEffect(() => {
    if (!user?.id) return;

    console.log('📋 Setting up bookings listener for provider:', user.id);

    // Query with proper ordering (indexes now deployed)
    const q = query(
      collection(db, 'bookings'),
      where('providerId', '==', user.id),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const bookingsData: Booking[] = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        const booking: Booking = {
          id: doc.id,
          providerId: data.providerId,
          userId: data.userId,
          userName: data.userName || 'Customer',
          userEmail: data.userEmail || '',
          userPhone: data.userPhone || '',
          serviceName: data.serviceName || 'Pet Service',
          petName: data.petName || 'Pet',
          petType: data.petType || 'dog',
          scheduledDate: data.scheduledDate || '',
          scheduledTime: data.scheduledTime || '',
          duration: data.duration || 60,
          totalPrice: data.totalPrice || 0,
          notes: data.notes || '',
          status: data.status || 'pending_provider_approval',
          createdAt: data.createdAt,
          updatedAt: data.updatedAt
        };
        bookingsData.push(booking);
      });

      console.log('📊 Loaded bookings for provider:', bookingsData.length);
      setBookings(bookingsData);
      setLoading(false);
    }, (error) => {
      console.error('❌ Error loading bookings:', error);
      toast.error('Failed to load bookings');
      setLoading(false);
    });

    return () => unsubscribe();
  }, [user?.id]);

  const handleBookingResponse = async (bookingId: string, action: 'accept' | 'reject') => {
    if (!user?.id) return;

    // For accept action, validate payment amount
    if (action === 'accept') {
      const paymentAmount = paymentAmounts[bookingId];
      if (!paymentAmount || parseFloat(paymentAmount) <= 0) {
        toast.error('Please enter a valid payment amount');
        return;
      }
    }

    setRespondingTo(bookingId);

    try {
      // Update booking directly using Firebase client
      const bookingRef = doc(db, 'bookings', bookingId);
      const bookingDoc = await getDoc(bookingRef);

      if (!bookingDoc.exists()) {
        throw new Error('Booking not found');
      }

      const booking = bookingDoc.data();

      // Verify the provider owns this booking
      if (booking.providerId !== user.id) {
        throw new Error('Unauthorized to modify this booking');
      }

      // Prepare update data
      const updateData: any = {
        status: action === 'accept' ? 'approved_awaiting_payment' : 'rejected',
        providerResponse: {
          action,
          message: action === 'accept' ? 'Your booking has been accepted! Payment link will be sent shortly.' : 'Sorry, this booking cannot be accommodated at this time.',
          respondedAt: new Date(),
          respondedBy: user.id
        },
        updatedAt: new Date()
      };

      // Add payment information for accepted bookings
      if (action === 'accept' && paymentAmounts[bookingId]) {
        const paymentAmount = parseFloat(paymentAmounts[bookingId]);
        updateData.paymentRequired = {
          amount: paymentAmount,
          currency: 'USD',
          status: 'pending',
          requestedAt: new Date()
        };

        // Add calendar entry for accepted bookings
        updateData.calendarEntry = {
          addedToCalendar: true,
          calendarEventId: `booking_${bookingId}_${Date.now()}`,
          addedAt: new Date()
        };
      }

      // Update the booking
      await updateDoc(bookingRef, updateData);

      // Create payment link for accepted bookings
      let paymentLink = null;
      if (action === 'accept' && paymentAmounts[bookingId]) {
        try {
          const paymentAmount = parseFloat(paymentAmounts[bookingId]);
          const response = await fetch('/api/payments/create-payment-link', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              bookingId,
              providerId: user.id,
              amount: paymentAmount,
              description: `${booking.serviceName} for ${booking.petName}`
            })
          });

          const result = await response.json();
          if (result.success) {
            paymentLink = result.paymentLink;
          } else {
            console.error('Failed to create payment link:', result.error);
            // Continue without payment link - user can still see the booking
          }
        } catch (error) {
          console.error('Error creating payment link:', error);
          // Continue without payment link
        }
      }

      // Send notification to customer
      if (booking.userId) {
        const notificationData = {
          userId: booking.userId,
          title: action === 'accept' ? 'Booking Accepted! 🎉' : 'Booking Update',
          message: action === 'accept'
            ? `Your ${booking.serviceName} booking has been accepted!${paymentLink ? ' Click the payment link to complete your booking.' : ' Payment details will be provided shortly.'}`
            : `Your ${booking.serviceName} booking request could not be accommodated.`,
          type: `booking_${action}ed`,
          data: {
            bookingId,
            paymentLink: paymentLink,
            paymentAmount: action === 'accept' ? paymentAmounts[bookingId] : null
          },
          read: false,
          createdAt: new Date()
        };

        await addDoc(collection(db, 'notifications'), notificationData);
      }

      toast.success(`Booking ${action}ed successfully!`);
      if (action === 'accept') {
        toast.success('Payment link sent to customer!');
      }

      // Clear payment amount after successful response
      setPaymentAmounts(prev => ({ ...prev, [bookingId]: '' }));
      setShowPaymentInput(prev => ({ ...prev, [bookingId]: false }));

    } catch (error: any) {
      console.error(`❌ Error ${action}ing booking:`, error);
      toast.error(error.message || `Failed to ${action} booking`);
    } finally {
      setRespondingTo(null);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      if (!dateString) return 'Invalid Date';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid Date';

      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  const formatTime = (timeString: string) => {
    try {
      if (!timeString) return 'Invalid Time';

      // Handle different time formats
      let formattedTime = timeString;
      if (!timeString.includes(':')) {
        return 'Invalid Time';
      }

      // Ensure we have HH:MM format
      if (timeString.length === 5) { // HH:MM
        formattedTime = timeString;
      } else if (timeString.length === 8) { // HH:MM:SS
        formattedTime = timeString.substring(0, 5);
      }

      const [hours, minutes] = formattedTime.split(':');
      const hour24 = parseInt(hours, 10);
      const min = parseInt(minutes, 10);

      if (isNaN(hour24) || isNaN(min) || hour24 < 0 || hour24 > 23 || min < 0 || min > 59) {
        return 'Invalid Time';
      }

      const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
      const ampm = hour24 >= 12 ? 'PM' : 'AM';
      const formattedMinutes = min.toString().padStart(2, '0');

      return `${hour12}:${formattedMinutes} ${ampm}`;
    } catch (error) {
      console.error('Error formatting time:', error);
      return 'Invalid Time';
    }
  };

  const filteredBookings = bookings.filter(booking => {
    if (filter === 'all') return true;
    if (filter === 'pending') return booking.status === 'pending_provider_approval';
    if (filter === 'accepted') return booking.status === 'accepted';
    if (filter === 'rejected') return booking.status === 'rejected';
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading bookings...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Bookings</h2>
          <p className="text-gray-600 mt-1">Manage your booking requests and appointments</p>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-4">
          {/* Feedback Button */}
          <button
            onClick={() => setShowFeedbackModal(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <HelpCircle className="w-4 h-4" />
            <span>Send Feedback</span>
          </button>

          {/* Filter Buttons */}
          <div className="flex space-x-2">
          {[
            { key: 'all', label: 'All' },
            { key: 'pending', label: 'Pending' },
            { key: 'accepted', label: 'Accepted' },
            { key: 'rejected', label: 'Rejected' }
          ].map((filterOption) => (
            <button
              key={filterOption.key}
              onClick={() => setFilter(filterOption.key)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                filter === filterOption.key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {filterOption.label}
            </button>
          ))}
          </div>
        </div>
      </div>

      {/* Bookings List */}
      {filteredBookings.length === 0 ? (
        <div className="bg-gray-50 rounded-lg p-8 text-center">
          <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">
            {filter === 'all' ? 'No bookings yet' : `No ${filter} bookings`}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredBookings.map((booking) => (
            <div key={booking.id} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {/* Customer Header */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <User className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <h4 className="text-lg font-bold text-gray-900">{booking.userName}</h4>
                        <p className="text-sm text-gray-600">wants to book {booking.serviceName}</p>
                        <div className="flex items-center space-x-4 mt-2">
                          <div className="flex items-center space-x-1 text-sm text-gray-500">
                            <Mail className="w-4 h-4" />
                            <span>{booking.userEmail}</span>
                          </div>
                          {booking.userPhone && (
                            <div className="flex items-center space-x-1 text-sm text-gray-500">
                              <Phone className="w-4 h-4" />
                              <span>{booking.userPhone}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <span className={`px-4 py-2 rounded-full text-sm font-semibold ${
                      booking.status === 'pending_provider_approval'
                        ? 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                        : booking.status === 'accepted'
                        ? 'bg-green-100 text-green-800 border border-green-200'
                        : 'bg-red-100 text-red-800 border border-red-200'
                    }`}>
                      {booking.status === 'pending_provider_approval' ? 'Pending Approval' :
                       booking.status === 'accepted' ? 'Accepted' : 'Rejected'}
                    </span>
                  </div>

                  {/* Booking Details */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <h5 className="font-semibold text-gray-900 mb-3">Booking Details</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Calendar className="w-4 h-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Date</p>
                          <p className="font-medium text-gray-900">{formatDate(booking.scheduledDate)}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                          <Clock className="w-4 h-4 text-green-600" />
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Time</p>
                          <p className="font-medium text-gray-900">{formatTime(booking.scheduledTime)}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                          <PawPrint className="w-4 h-4 text-purple-600" />
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Pet</p>
                          <p className="font-medium text-gray-900">{booking.petName} ({booking.petType})</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                          <DollarSign className="w-4 h-4 text-yellow-600" />
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Service Price</p>
                          <p className="font-medium text-gray-900">${booking.totalPrice}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Special Requests */}
                  {booking.notes && (
                    <div className="bg-blue-50 rounded-lg p-4 mb-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mt-0.5">
                          <MessageSquare className="w-4 h-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide mb-1">Special Requests</p>
                          <p className="text-sm text-gray-700">{booking.notes}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {booking.status === 'pending_provider_approval' && (
                  <div className="border-t border-gray-200 pt-4 mt-4">
                    <div className="flex flex-col space-y-4">
                      {/* Payment Amount Input */}
                      <div className="bg-green-50 rounded-lg p-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Payment Amount Required (USD)
                        </label>
                        <div className="flex items-center space-x-2">
                          <div className="relative flex-1">
                            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                            <input
                              type="number"
                              min="0"
                              step="0.01"
                              placeholder="0.00"
                              value={paymentAmounts[booking.id] || ''}
                              onChange={(e) => setPaymentAmounts(prev => ({
                                ...prev,
                                [booking.id]: e.target.value
                              }))}
                              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            />
                          </div>
                          <span className="text-sm text-gray-500">
                            (Default: ${booking.totalPrice})
                          </span>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Enter the amount you want the customer to pay upfront. A payment link will be sent to them.
                        </p>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex space-x-3">
                        <button
                          onClick={() => handleBookingResponse(booking.id, 'accept')}
                          disabled={respondingTo === booking.id}
                          className="flex-1 flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors disabled:opacity-50 font-medium"
                        >
                          <Check className="w-5 h-5" />
                          <span>Accept & Send Payment Link</span>
                        </button>
                        <button
                          onClick={() => handleBookingResponse(booking.id, 'reject')}
                          disabled={respondingTo === booking.id}
                          className="flex items-center justify-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg transition-colors disabled:opacity-50 font-medium"
                        >
                          <X className="w-5 h-5" />
                          <span>Decline</span>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Feedback Modal */}
      <FeedbackModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
      />
    </div>
  );
}
