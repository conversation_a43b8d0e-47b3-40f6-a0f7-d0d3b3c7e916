"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Eye, MousePointer, Calendar, CreditCard, Star, Target } from "lucide-react";
import { useProvider } from "@/contexts/ProviderContext";
import { BoostService, ProviderBoost } from "@/lib/monetization/boost-service";
import { BOOST_TYPES } from "@/lib/stripe/client-config";
import toast from 'react-hot-toast';

export default function BoostTab() {
  const { provider } = useProvider();
  const [activeBoosts, setActiveBoosts] = useState<ProviderBoost[]>([]);
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState<string | null>(null);

  useEffect(() => {
    loadBoostData();
  }, [provider]);

  const loadBoostData = async () => {
    if (!provider?.id) return;
    
    try {
      // Load active boosts
      const boosts = await BoostService.getActiveBoosts(provider.id);
      setActiveBoosts(boosts);

      // Load analytics
      const analyticsResult = await BoostService.getBoostAnalytics(provider.id);
      if (analyticsResult.success) {
        setAnalytics(analyticsResult.analytics);
      }
    } catch (error) {
      console.error('Error loading boost data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePurchaseBoost = async (boostType: string) => {
    if (!provider?.id || !provider.stripeCustomerId) {
      toast.error('Please complete your payment setup first');
      return;
    }

    setPurchasing(boostType);
    
    try {
      const result = await BoostService.createBoostPaymentIntent(
        provider.id,
        boostType,
        provider.stripeCustomerId
      );

      if (result.success && result.clientSecret) {
        // Here you would integrate with Stripe Elements for payment
        // For now, we'll simulate success
        toast.success('Boost purchased successfully!');
        loadBoostData();
      } else {
        throw new Error(result.error || 'Failed to create payment');
      }
    } catch (error) {
      console.error('Error purchasing boost:', error);
      toast.error('Failed to purchase boost. Please try again.');
    } finally {
      setPurchasing(null);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center">
            <TrendingUp className="w-8 h-8 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Boost Your Visibility</h2>
            <p className="text-gray-600">
              Get more bookings with premium placement and featured listings
            </p>
          </div>
        </div>
      </div>

      {/* Active Boosts */}
      {activeBoosts.length > 0 && (
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Active Boosts</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {activeBoosts.map((boost) => {
              const boostType = BOOST_TYPES[boost.boostType];
              const timeRemaining = new Date(boost.endDate).getTime() - new Date().getTime();
              const hoursRemaining = Math.max(0, Math.floor(timeRemaining / (1000 * 60 * 60)));
              
              return (
                <div key={boost.id} className="p-4 border border-green-200 bg-green-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-green-800">{boostType?.name}</h4>
                    <span className="text-xs bg-green-600 text-white px-2 py-1 rounded-full">
                      Active
                    </span>
                  </div>
                  <p className="text-sm text-green-700 mb-3">{boostType?.description}</p>
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div>
                      <p className="text-lg font-bold text-green-800">{hoursRemaining}h</p>
                      <p className="text-xs text-green-600">Remaining</p>
                    </div>
                    <div>
                      <p className="text-lg font-bold text-green-800">{boost.impressions}</p>
                      <p className="text-xs text-green-600">Views</p>
                    </div>
                    <div>
                      <p className="text-lg font-bold text-green-800">{boost.clicks}</p>
                      <p className="text-xs text-green-600">Clicks</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Analytics Overview */}
      {analytics && (
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Boost Performance</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                <CreditCard className="w-6 h-6 text-blue-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">${analytics.totalSpent.toFixed(2)}</p>
              <p className="text-sm text-gray-600">Total Spent</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                <Eye className="w-6 h-6 text-green-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{analytics.totalImpressions.toLocaleString()}</p>
              <p className="text-sm text-gray-600">Total Views</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                <MousePointer className="w-6 h-6 text-purple-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{analytics.totalClicks.toLocaleString()}</p>
              <p className="text-sm text-gray-600">Total Clicks</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                <TrendingUp className="w-6 h-6 text-yellow-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">
                {analytics.totalImpressions > 0 ? ((analytics.totalClicks / analytics.totalImpressions) * 100).toFixed(1) : 0}%
              </p>
              <p className="text-sm text-gray-600">Click Rate</p>
            </div>
          </div>
        </div>
      )}

      {/* Available Boosts */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <h3 className="text-xl font-bold text-gray-800 mb-6">Available Boosts</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {Object.values(BOOST_TYPES).map((boost) => {
            const isActive = activeBoosts.some(active => active.boostType === boost.id);
            const isPurchasing = purchasing === boost.id;
            
            return (
              <div key={boost.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                      boost.id === 'topInSearch' 
                        ? 'bg-gradient-to-br from-blue-500 to-purple-600' 
                        : 'bg-gradient-to-br from-orange-500 to-red-600'
                    }`}>
                      {boost.id === 'topInSearch' ? (
                        <Target className="w-6 h-6 text-white" />
                      ) : (
                        <Star className="w-6 h-6 text-white" />
                      )}
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-gray-900">{boost.name}</h4>
                      <p className="text-2xl font-bold text-gray-900">
                        ${boost.price}
                        <span className="text-sm font-normal text-gray-600">
                          /{boost.duration > 24 ? `${boost.duration / 24} days` : `${boost.duration}h`}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>

                <p className="text-gray-600 mb-4">{boost.description}</p>

                <ul className="space-y-2 mb-6">
                  {boost.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-2 text-sm">
                      <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button
                  onClick={() => handlePurchaseBoost(boost.id)}
                  disabled={isActive || isPurchasing}
                  className={`w-full py-3 rounded-xl font-semibold transition-all duration-200 ${
                    isActive
                      ? 'bg-green-100 text-green-700 cursor-not-allowed'
                      : isPurchasing
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : boost.id === 'topInSearch'
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 transform hover:scale-105'
                      : 'bg-gradient-to-r from-orange-600 to-red-600 text-white hover:from-orange-700 hover:to-red-700 transform hover:scale-105'
                  }`}
                >
                  {isActive ? (
                    'Currently Active'
                  ) : isPurchasing ? (
                    'Processing...'
                  ) : (
                    `Purchase ${boost.name}`
                  )}
                </button>
              </div>
            );
          })}
        </div>
      </div>

      {/* Boost Tips */}
      <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-6 border border-yellow-200">
        <h3 className="text-lg font-bold text-gray-800 mb-3">💡 Boost Tips</h3>
        <ul className="space-y-2 text-sm text-gray-700">
          <li>• <strong>Top in Search</strong> works best during peak booking hours (evenings and weekends)</li>
          <li>• <strong>Featured Today</strong> is perfect for last-minute availability or special promotions</li>
          <li>• Combine boosts with competitive pricing for maximum impact</li>
          <li>• Monitor your analytics to see which boosts work best for your services</li>
        </ul>
      </div>
    </div>
  );
}
