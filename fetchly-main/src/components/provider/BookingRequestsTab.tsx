'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { collection, query, where, orderBy, onSnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Calendar, Clock, User, DollarSign, MessageSquare, Check, X, RefreshCw, AlertCircle, CheckCircle, XCircle, MapPin, Phone, Mail } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface Booking {
  id: string;
  userId: string;
  providerId: string;
  customerName: string;
  userEmail: string;
  userPhone?: string;
  petName: string;
  petType: string;
  serviceName: string;
  scheduledDate: string;
  scheduledTime: string;
  duration: number;
  totalPrice: number;
  status: string;
  notes?: string;
  location?: string;
  createdAt: any;
  updatedAt?: any;
}

export default function BookingRequestsTab() {
  const { user } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingBooking, setProcessingBooking] = useState<string | null>(null);
  const [paymentAmounts, setPaymentAmounts] = useState<{[key: string]: string}>({});

  // Fetch bookings for this provider
  useEffect(() => {
    if (!user?.id) {
      console.log('❌ No provider ID found');
      setLoading(false);
      return;
    }

    setLoading(true);

    // ENHANCED: Query for bookings using the correct provider ID
    // This will find bookings with either the current user ID or any legacy IDs
    const bookingsQuery = query(
      collection(db, 'bookings'),
      where('providerId', '==', user.id),
      orderBy('createdAt', 'desc')
    );

    // Also check for bookings that might have the old provider ID format
    const legacyBookingsQuery = query(
      collection(db, 'bookings'),
      orderBy('createdAt', 'desc')
    );

    // Use a more comprehensive approach to find all bookings for this provider
    const unsubscribe = onSnapshot(
      legacyBookingsQuery, // Get all bookings and filter manually for now
      (snapshot) => {
        const myBookings: Booking[] = [];

        snapshot.forEach((doc) => {
          const data = doc.data();

          const booking: Booking = {
            id: doc.id,
            userId: data.userId || '',
            providerId: data.providerId || '',
            customerName: data.customerName || data.userName || 'Unknown Customer',
            userEmail: data.userEmail || data.email || '',
            userPhone: data.userPhone || data.phone || '',
            petName: data.petName || 'Unknown Pet',
            petType: data.petType || 'Pet',
            serviceName: data.serviceName || 'Unknown Service',
            scheduledDate: data.scheduledDate || '',
            scheduledTime: data.scheduledTime || '',
            duration: data.duration || 60,
            totalPrice: data.totalPrice || 0,
            status: data.status || 'pending',
            notes: data.notes || '',
            location: data.location || '',
            createdAt: data.createdAt,
            updatedAt: data.updatedAt
          };

          // Check if this booking belongs to the current provider
          if (data.providerId === user.id) {
            myBookings.push(booking);
          }
        });

        // Show provider's bookings
        setBookings(myBookings);
        setLoading(false);
      },
      (error) => {
        toast.error('Failed to load booking requests');
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [user?.id]);

  // Handle booking approval/rejection
  const handleBookingResponse = async (bookingId: string, action: 'accept' | 'reject') => {
    if (!user?.id) return;

    // Validate payment amount for acceptance
    if (action === 'accept') {
      const amount = paymentAmounts[bookingId];
      if (!amount || parseFloat(amount) <= 0) {
        toast.error('Please enter a valid payment amount');
        return;
      }
    }

    setProcessingBooking(bookingId);

    try {
      const response = await fetch(`/api/bookings/${bookingId}/respond`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          providerId: user.id,
          paymentAmount: action === 'accept' ? parseFloat(paymentAmounts[bookingId]) : undefined,
          message: action === 'accept' 
            ? 'Your booking has been approved! Payment link will be sent shortly.'
            : 'Sorry, this booking cannot be accommodated at this time.'
        })
      });

      const result = await response.json();

      if (result.success) {
        toast.success(action === 'accept' ? 'Booking approved successfully!' : 'Booking rejected');
        setPaymentAmounts(prev => ({ ...prev, [bookingId]: '' }));
      } else {
        throw new Error(result.error || `Failed to ${action} booking`);
      }
    } catch (error: any) {
      console.error(`❌ Error ${action}ing booking:`, error);
      toast.error(error.message || `Failed to ${action} booking`);
    } finally {
      setProcessingBooking(null);
    }
  };

  // Filter bookings by status
  const pendingBookings = bookings.filter(b => 
    b.status === 'pending_provider_approval' || b.status === 'pending'
  );
  const acceptedBookings = bookings.filter(b => 
    b.status === 'accepted' || b.status === 'confirmed'
  );
  const rejectedBookings = bookings.filter(b => 
    b.status === 'rejected' || b.status === 'cancelled'
  );

  // Get status badge
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'pending': { color: 'bg-yellow-100 text-yellow-800', icon: Clock, label: 'Pending' },
      'pending_provider_approval': { color: 'bg-orange-100 text-orange-800', icon: AlertCircle, label: 'Awaiting Response' },
      'accepted': { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Accepted' },
      'confirmed': { color: 'bg-blue-100 text-blue-800', icon: CheckCircle, label: 'Confirmed' },
      'rejected': { color: 'bg-red-100 text-red-800', icon: XCircle, label: 'Rejected' },
      'cancelled': { color: 'bg-gray-100 text-gray-800', icon: XCircle, label: 'Cancelled' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading booking requests...</p>
          <p className="text-sm text-gray-400">Provider ID: {user?.id}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Booking Requests</h2>
          <p className="text-gray-600">Manage your service booking requests</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            Total: {bookings.length} | Pending: {pendingBookings.length}
          </div>
          <button
            onClick={() => window.location.reload()}
            className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Refresh</span>
          </button>
          <button
            onClick={async () => {
              if (confirm('This will fix provider ID mismatches in your bookings. Continue?')) {
                try {
                  const response = await fetch('/api/admin/emergency-fix-provider-ids', {
                    method: 'POST'
                  });
                  const data = await response.json();
                  if (data.success) {
                    alert(`Fixed ${data.summary.bookingsFixed} bookings! Refreshing...`);
                    window.location.reload();
                  } else {
                    alert('Fix failed: ' + data.error);
                  }
                } catch (error) {
                  alert('Fix failed: ' + error);
                }
              }
            }}
            className="flex items-center space-x-2 px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            <AlertCircle className="w-4 h-4" />
            <span>Fix Provider IDs</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-orange-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-orange-800">Pending Requests</p>
              <p className="text-2xl font-bold text-orange-900">{pendingBookings.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-green-800">Accepted</p>
              <p className="text-2xl font-bold text-green-900">{acceptedBookings.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <XCircle className="w-5 h-5 text-red-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-red-800">Rejected</p>
              <p className="text-2xl font-bold text-red-900">{rejectedBookings.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Debug Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Debug Information</h4>
        <div className="text-sm text-blue-800 space-y-1">
          <p>Provider ID: {user?.id}</p>
          <p>Total Bookings Found: {bookings.length}</p>
          <p>Collection: bookings</p>
          <p>Query: where('providerId', '==', '{user?.id}')</p>
        </div>
      </div>

      {/* Bookings List */}
      {bookings.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Booking Requests</h3>
          <p className="text-gray-500">You don't have any booking requests yet.</p>
          <p className="text-sm text-gray-400 mt-2">
            Make sure pet owners are using your correct provider ID: {user?.id}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {bookings.map((booking) => (
            <BookingCard
              key={booking.id}
              booking={booking}
              onAccept={() => handleBookingResponse(booking.id, 'accept')}
              onReject={() => handleBookingResponse(booking.id, 'reject')}
              processing={processingBooking === booking.id}
              paymentAmount={paymentAmounts[booking.id] || ''}
              setPaymentAmount={(amount) => setPaymentAmounts(prev => ({ ...prev, [booking.id]: amount }))}
              getStatusBadge={getStatusBadge}
            />
          ))}
        </div>
      )}
    </div>
  );
}

// Booking Card Component
interface BookingCardProps {
  booking: Booking;
  onAccept: () => void;
  onReject: () => void;
  processing: boolean;
  paymentAmount: string;
  setPaymentAmount: (amount: string) => void;
  getStatusBadge: (status: string) => JSX.Element;
}

function BookingCard({
  booking,
  onAccept,
  onReject,
  processing,
  paymentAmount,
  setPaymentAmount,
  getStatusBadge
}: BookingCardProps) {
  const [showPaymentInput, setShowPaymentInput] = useState(false);
  const isPending = booking.status === 'pending_provider_approval' || booking.status === 'pending';

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <User className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h4 className="font-semibold text-gray-900">{booking.customerName}</h4>
            <p className="text-sm text-gray-500">wants to book {booking.serviceName}</p>
            <p className="text-xs text-gray-400">Booking ID: {booking.id}</p>
          </div>
        </div>
        <div className="text-right">
          {getStatusBadge(booking.status)}
          <p className="text-sm text-gray-500 mt-1">
            {booking.createdAt?.toDate?.()?.toLocaleDateString() || 'No date'}
          </p>
        </div>
      </div>

      {/* Booking Details Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div className="flex items-center space-x-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <div>
            <p className="text-xs text-gray-500">Date</p>
            <p className="text-sm font-medium">{booking.scheduledDate || 'Not set'}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Clock className="w-4 h-4 text-gray-400" />
          <div>
            <p className="text-xs text-gray-500">Time</p>
            <p className="text-sm font-medium">{booking.scheduledTime || 'Not set'}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <User className="w-4 h-4 text-gray-400" />
          <div>
            <p className="text-xs text-gray-500">Pet</p>
            <p className="text-sm font-medium">{booking.petName} ({booking.petType})</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <DollarSign className="w-4 h-4 text-gray-400" />
          <div>
            <p className="text-xs text-gray-500">Price</p>
            <p className="text-sm font-medium">${booking.totalPrice}</p>
          </div>
        </div>
      </div>

      {/* Contact Info */}
      <div className="flex items-center space-x-6 mb-4 text-sm text-gray-600">
        {booking.userEmail && (
          <div className="flex items-center space-x-1">
            <Mail className="w-4 h-4" />
            <span>{booking.userEmail}</span>
          </div>
        )}
        {booking.userPhone && (
          <div className="flex items-center space-x-1">
            <Phone className="w-4 h-4" />
            <span>{booking.userPhone}</span>
          </div>
        )}
        {booking.location && (
          <div className="flex items-center space-x-1">
            <MapPin className="w-4 h-4" />
            <span>{booking.location}</span>
          </div>
        )}
      </div>

      {/* Notes */}
      {booking.notes && (
        <div className="mb-4 p-3 bg-gray-50 rounded-md">
          <div className="flex items-start space-x-2">
            <MessageSquare className="w-4 h-4 text-gray-400 mt-0.5" />
            <div>
              <p className="text-xs text-gray-500 mb-1">Customer Notes:</p>
              <p className="text-sm text-gray-700">{booking.notes}</p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {isPending && (
        <div className="flex items-center space-x-3">
          {/* Accept Button */}
          <div className="flex items-center space-x-2">
            {showPaymentInput ? (
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  placeholder="Amount ($)"
                  value={paymentAmount}
                  onChange={(e) => setPaymentAmount(e.target.value)}
                  className="w-24 px-2 py-1 border border-gray-300 rounded text-sm"
                  min="0"
                  step="0.01"
                />
                <button
                  onClick={onAccept}
                  disabled={processing || !paymentAmount}
                  className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 text-sm"
                >
                  {processing ? (
                    <RefreshCw className="w-3 h-3 animate-spin" />
                  ) : (
                    <Check className="w-3 h-3" />
                  )}
                  <span>Confirm</span>
                </button>
                <button
                  onClick={() => setShowPaymentInput(false)}
                  className="px-2 py-1 text-gray-500 hover:text-gray-700 text-sm"
                >
                  Cancel
                </button>
              </div>
            ) : (
              <button
                onClick={() => setShowPaymentInput(true)}
                className="flex items-center space-x-1 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                <Check className="w-4 h-4" />
                <span>Accept</span>
              </button>
            )}
          </div>

          {/* Reject Button */}
          <button
            onClick={onReject}
            disabled={processing}
            className="flex items-center space-x-1 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
          >
            {processing ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <X className="w-4 h-4" />
            )}
            <span>Reject</span>
          </button>
        </div>
      )}
    </div>
  );
}
