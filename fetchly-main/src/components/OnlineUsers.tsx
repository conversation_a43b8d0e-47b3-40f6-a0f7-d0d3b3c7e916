'use client';

import OnlineUsersModule from './OnlineUsersModule';

interface OnlineUsersProps {
  onMessageUser?: (userId: string, userName: string) => void;
  onViewProfile?: (userId: string) => void;
  className?: string;
}

export default function OnlineUsers({
  onMessageUser,
  onViewProfile,
  className = ''
}: OnlineUsersProps) {
  // Simply render the new OnlineUsersModule
  return (
    <div className={className}>
      <OnlineUsersModule />
    </div>
  );
}
