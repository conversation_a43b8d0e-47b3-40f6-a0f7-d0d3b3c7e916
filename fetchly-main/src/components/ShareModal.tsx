'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { 
  X, 
  Copy, 
  Share2, 
  Facebook, 
  Twitter, 
  MessageCircle,
  Mail,
  Link as LinkIcon,
  User,
  Globe,
  Check
} from 'lucide-react';
import { addDoc, collection, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import toast from 'react-hot-toast';

interface ShareModalProps {
  post: {
    id: string;
    userId: string;
    userName: string;
    userAvatar: string;
    content: string;
    image?: string;
    timestamp: Date;
  };
  isOpen: boolean;
  onClose: () => void;
}

export default function ShareModal({ post, isOpen, onClose }: ShareModalProps) {
  const { user } = useAuth();
  const { createPost } = useData();
  const [shareComment, setShareComment] = useState('');
  const [isSharing, setIsSharing] = useState(false);
  const [copied, setCopied] = useState(false);

  if (!isOpen) return null;

  // Generate shareable link
  const shareUrl = `${window.location.origin}/community/post/${post.id}`;

  // Copy link to clipboard
  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      toast.success('Link copied to clipboard!');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
      toast.error('Failed to copy link');
    }
  };

  // Share to social media
  const handleSocialShare = (platform: string) => {
    const text = `Check out this post by ${post.userName}: ${post.content.substring(0, 100)}${post.content.length > 100 ? '...' : ''}`;
    const encodedText = encodeURIComponent(text);
    const encodedUrl = encodeURIComponent(shareUrl);

    let shareLink = '';
    
    switch (platform) {
      case 'facebook':
        shareLink = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`;
        break;
      case 'twitter':
        shareLink = `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`;
        break;
      case 'whatsapp':
        shareLink = `https://wa.me/?text=${encodedText}%20${encodedUrl}`;
        break;
      case 'email':
        shareLink = `mailto:?subject=Check out this post&body=${encodedText}%0A%0A${encodedUrl}`;
        break;
    }

    if (shareLink) {
      window.open(shareLink, '_blank', 'width=600,height=400');
    }
  };

  // Share to own profile
  const handleShareToProfile = async () => {
    if (!user || isSharing) return;

    try {
      setIsSharing(true);

      const shareContent = shareComment.trim() 
        ? `${shareComment}\n\n--- Shared from ${post.userName} ---\n${post.content}`
        : `--- Shared from ${post.userName} ---\n${post.content}`;

      // Create a new post with shared content
      await createPost(shareContent, post.image, true);

      // Track the share in database
      await addDoc(collection(db, 'postShares'), {
        originalPostId: post.id,
        originalUserId: post.userId,
        sharedByUserId: user.id,
        sharedByUserName: user.name || (user as any).displayName || 'Anonymous',
        shareComment: shareComment.trim(),
        timestamp: Timestamp.now()
      });

      toast.success('Post shared to your profile!');
      setShareComment('');
      onClose();
    } catch (error) {
      console.error('Error sharing post:', error);
      toast.error('Failed to share post');
    } finally {
      setIsSharing(false);
    }
  };

  // Native share (mobile)
  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${post.userName}'s post`,
          text: post.content,
          url: shareUrl
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      handleCopyLink();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800">Share Post</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Post Preview */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-3">
              <img
                src={post.userAvatar}
                alt={post.userName}
                className="w-8 h-8 rounded-full object-cover"
              />
              <div>
                <h4 className="font-medium text-gray-900">{post.userName}</h4>
                <p className="text-sm text-gray-500">
                  {post.timestamp.toLocaleDateString()}
                </p>
              </div>
            </div>
            <p className="text-gray-700 text-sm line-clamp-3">{post.content}</p>
            {post.image && (
              <img
                src={post.image}
                alt="Post content"
                className="mt-3 w-full h-32 object-cover rounded-lg"
              />
            )}
          </div>

          {/* Share Options */}
          <div className="space-y-4">
            {/* Copy Link */}
            <button
              onClick={handleCopyLink}
              className="w-full flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors"
            >
              {copied ? (
                <Check className="w-5 h-5 text-green-500" />
              ) : (
                <Copy className="w-5 h-5 text-gray-600" />
              )}
              <span className="font-medium text-gray-900">
                {copied ? 'Link Copied!' : 'Copy Link'}
              </span>
            </button>

            {/* Native Share (Mobile) */}
            {typeof navigator.share === 'function' && (
              <button
                onClick={handleNativeShare}
                className="w-full flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors"
              >
                <Share2 className="w-5 h-5 text-gray-600" />
                <span className="font-medium text-gray-900">Share</span>
              </button>
            )}

            {/* Social Media Sharing */}
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => handleSocialShare('facebook')}
                className="flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Facebook className="w-4 h-4" />
                <span className="text-sm font-medium">Facebook</span>
              </button>
              
              <button
                onClick={() => handleSocialShare('twitter')}
                className="flex items-center justify-center space-x-2 p-3 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors"
              >
                <Twitter className="w-4 h-4" />
                <span className="text-sm font-medium">Twitter</span>
              </button>
              
              <button
                onClick={() => handleSocialShare('whatsapp')}
                className="flex items-center justify-center space-x-2 p-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              >
                <MessageCircle className="w-4 h-4" />
                <span className="text-sm font-medium">WhatsApp</span>
              </button>
              
              <button
                onClick={() => handleSocialShare('email')}
                className="flex items-center justify-center space-x-2 p-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <Mail className="w-4 h-4" />
                <span className="text-sm font-medium">Email</span>
              </button>
            </div>
          </div>

          {/* Share to Profile */}
          {user && (
            <div className="border-t border-gray-200 pt-6">
              <h3 className="font-medium text-gray-900 mb-3">Share to Your Profile</h3>
              
              <div className="space-y-3">
                <textarea
                  value={shareComment}
                  onChange={(e) => setShareComment(e.target.value)}
                  placeholder="Add a comment (optional)..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  rows={3}
                />
                
                <button
                  onClick={handleShareToProfile}
                  disabled={isSharing}
                  className="w-full flex items-center justify-center space-x-2 p-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  {isSharing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Sharing...</span>
                    </>
                  ) : (
                    <>
                      <User className="w-4 h-4" />
                      <span>Share to My Profile</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          )}

          {/* Public Link Info */}
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Globe className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">Public Link</h4>
                <p className="text-sm text-blue-700 mt-1">
                  Anyone with this link can view the post, even without an account.
                </p>
                <p className="text-xs text-blue-600 mt-2 font-mono bg-white px-2 py-1 rounded break-all">
                  {shareUrl}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
