'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, BarChart3, MessageSquare, User } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface MobileNavigationProps {
  unreadCount?: number;
}

export function MobileNavigation({ unreadCount = 0 }: MobileNavigationProps) {
  const pathname = usePathname();
  const { user } = useAuth();
  const [showTooltip, setShowTooltip] = useState<string | null>(null);

  const navItems = [
    {
      name: 'Home',
      href: '/',
      icon: Home,
      active: pathname === '/',
    },
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: BarChart3,
      active: pathname?.startsWith('/dashboard'),
    },
    {
      name: 'Messages',
      href: '/messages',
      icon: MessageSquare,
      active: pathname?.startsWith('/messages'),
      badge: unreadCount > 0 ? unreadCount : undefined,
    },
    {
      name: 'Profile',
      href: user ? `/profile/${user.uid}` : '/login',
      icon: User,
      active: pathname?.startsWith('/profile'),
    },
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 flex justify-around py-2 z-50 lg:hidden shadow-lg">
      {navItems.map((item) => (
        <div key={item.name} className="relative">
          <Link
            href={item.href}
            className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
              item.active ? 'text-green-600' : 'text-gray-600 hover:text-green-600'
            }`}
            onMouseEnter={() => setShowTooltip(item.name)}
            onMouseLeave={() => setShowTooltip(null)}
            onTouchStart={() => setShowTooltip(item.name)}
            onTouchEnd={() => setShowTooltip(null)}
          >
            <div className="relative">
              <item.icon className="w-6 h-6" />
              {item.badge && item.badge > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {item.badge > 9 ? '9+' : item.badge}
                </span>
              )}
            </div>
            <span className="text-xs mt-1">{item.name}</span>
          </Link>
          
          {/* Tooltip */}
          {showTooltip === item.name && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 bg-gray-900 text-white text-xs rounded py-1 px-2 whitespace-nowrap">
              {item.name}
              <div className="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-l-transparent border-r-transparent border-t-4 border-t-gray-900"></div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
