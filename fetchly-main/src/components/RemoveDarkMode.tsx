'use client';

import { useEffect } from 'react';

export default function RemoveDarkMode() {
  useEffect(() => {
    // Remove dark mode from localStorage
    localStorage.removeItem('darkMode');
    localStorage.removeItem('theme');
    
    // Remove dark class from document
    document.documentElement.classList.remove('dark');
    document.body.classList.remove('dark');
    
    // Force light mode
    document.documentElement.setAttribute('data-theme', 'light');
    
    console.log('🌞 Dark mode completely removed');
  }, []);

  return null;
}
