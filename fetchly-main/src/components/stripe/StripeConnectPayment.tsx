'use client';

import { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api-client';
import { CreditCard, Lock, DollarSign } from 'lucide-react';
import toast from 'react-hot-toast';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface PaymentFormProps {
  bookingId: string;
  providerId: string;
  amount: number; // in cents
  description: string;
  onSuccess: () => void;
  onError: (error: string) => void;
}

function PaymentForm({ bookingId, providerId, amount, description, onSuccess, onError }: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const { user, getAuthToken } = useAuth();
  const [processing, setProcessing] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      return;
    }

    setProcessing(true);

    try {
      // Create payment method
      const { error: paymentMethodError, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
        billing_details: {
          name: user?.name || '',
          email: user?.email || '',
        },
      });

      if (paymentMethodError) {
        throw new Error(paymentMethodError.message);
      }

      // Process payment with platform fee
      const token = await getAuthToken();
      const response = await apiClient.post('/api/payments/charge', {
        bookingId,
        providerId,
        amount,
        paymentMethodId: paymentMethod.id,
        customerEmail: user?.email,
        description,
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.success) {
        throw new Error(response.error || 'Payment failed');
      }

      const { paymentIntent } = response;

      // Handle payment confirmation if needed
      if (paymentIntent.status === 'requires_action') {
        const { error: confirmError } = await stripe.confirmCardPayment(
          paymentIntent.client_secret
        );

        if (confirmError) {
          throw new Error(confirmError.message);
        }
      }

      toast.success('Payment successful!');
      onSuccess();

    } catch (error: any) {
      console.error('Payment error:', error);
      const errorMessage = error.message || 'Payment failed. Please try again.';
      toast.error(errorMessage);
      onError(errorMessage);
    } finally {
      setProcessing(false);
    }
  };

  const platformFee = Math.round(amount * 0.08); // 8% platform fee
  const providerAmount = amount - platformFee;

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Payment Breakdown */}
      <div className="bg-gray-50 rounded-lg p-4 space-y-2">
        <h4 className="font-medium text-gray-900 mb-3">Payment Breakdown</h4>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Service Total</span>
          <span className="font-medium">${(amount / 100).toFixed(2)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Platform Fee (8%)</span>
          <span className="font-medium">${(platformFee / 100).toFixed(2)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Provider Receives</span>
          <span className="font-medium text-green-600">${(providerAmount / 100).toFixed(2)}</span>
        </div>
        <hr className="my-2" />
        <div className="flex justify-between font-semibold">
          <span>Total Charge</span>
          <span>${(amount / 100).toFixed(2)}</span>
        </div>
      </div>

      {/* Card Input */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Card Information
        </label>
        <div className="border border-gray-300 rounded-lg p-3 bg-white">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
                invalid: {
                  color: '#9e2146',
                },
              },
            }}
          />
        </div>
      </div>

      {/* Security Notice */}
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <Lock className="w-4 h-4" />
        <span>Your payment information is secure and encrypted</span>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={!stripe || processing}
        className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
      >
        {processing ? (
          <>
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            Processing Payment...
          </>
        ) : (
          <>
            <CreditCard className="w-5 h-5" />
            Pay ${(amount / 100).toFixed(2)}
          </>
        )}
      </button>

      {/* Platform Fee Disclaimer */}
      <p className="text-xs text-gray-500 text-center">
        By completing this payment, you agree to pay the service provider through Fetchly. 
        An 8% platform fee is included to cover payment processing and platform services.
      </p>
    </form>
  );
}

interface StripeConnectPaymentProps {
  bookingId: string;
  providerId: string;
  amount: number; // in cents
  description: string;
  onSuccess: () => void;
  onError: (error: string) => void;
}

export default function StripeConnectPayment(props: StripeConnectPaymentProps) {
  return (
    <Elements stripe={stripePromise}>
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
            <DollarSign className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Complete Payment</h3>
            <p className="text-sm text-gray-600">{props.description}</p>
          </div>
        </div>
        
        <PaymentForm {...props} />
      </div>
    </Elements>
  );
}
