'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api-client';
import { 
  CreditCard, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  ExternalLink,
  DollarSign,
  Shield,
  Zap
} from 'lucide-react';
import toast from 'react-hot-toast';

interface StripeAccount {
  id: string | null;
  status: 'not_connected' | 'pending' | 'restricted' | 'active';
  payouts_enabled: boolean;
  charges_enabled: boolean;
  details_submitted: boolean;
  requirements: {
    currently_due: string[];
    eventually_due: string[];
    past_due: string[];
    disabled_reason?: string;
  };
  balance?: {
    available: Array<{ amount: number; currency: string }>;
    pending: Array<{ amount: number; currency: string }>;
  };
}

interface StripeConnectOnboardingProps {
  providerId: string;
}

export default function StripeConnectOnboarding({ providerId }: StripeConnectOnboardingProps) {
  const { user, getAuthToken } = useAuth();
  const [account, setAccount] = useState<StripeAccount | null>(null);
  const [loading, setLoading] = useState(true);
  const [onboarding, setOnboarding] = useState(false);

  useEffect(() => {
    fetchAccountStatus();
  }, [providerId]);

  const fetchAccountStatus = async () => {
    try {
      setLoading(true);
      const token = await getAuthToken();
      
      const response = await apiClient.get(`/api/connect/account/${providerId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.success) {
        setAccount(response.account);
      }
    } catch (error: any) {
      console.error('Error fetching account status:', error);
      toast.error('Failed to load account status');
    } finally {
      setLoading(false);
    }
  };

  const handleConnectStripe = async () => {
    try {
      setOnboarding(true);
      const token = await getAuthToken();
      
      const response = await apiClient.post('/api/connect/onboard', {
        providerId,
        refreshUrl: `${window.location.origin}/provider/dashboard?tab=wallet&refresh=true`,
        returnUrl: `${window.location.origin}/provider/dashboard?tab=wallet&success=true`,
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.success) {
        // Redirect to Stripe onboarding
        window.location.href = response.onboardingUrl;
      } else {
        throw new Error(response.error || 'Failed to create onboarding link');
      }
    } catch (error: any) {
      console.error('Error starting onboarding:', error);
      toast.error(error.message || 'Failed to start Stripe onboarding');
      setOnboarding(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50 border-green-200';
      case 'pending': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'restricted': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-5 h-5" />;
      case 'pending': return <Clock className="w-5 h-5" />;
      case 'restricted': return <AlertCircle className="w-5 h-5" />;
      default: return <CreditCard className="w-5 h-5" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Active - Ready to receive payments';
      case 'pending': return 'Pending - Complete your setup';
      case 'restricted': return 'Restricted - Action required';
      case 'not_connected': return 'Not connected';
      default: return 'Unknown status';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status Card */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Payment Setup</h3>
          {account?.status && (
            <div className={`flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium ${getStatusColor(account.status)}`}>
              {getStatusIcon(account.status)}
              {getStatusText(account.status)}
            </div>
          )}
        </div>

        {account?.status === 'not_connected' ? (
          <div className="text-center py-8">
            <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h4 className="text-xl font-semibold text-gray-900 mb-2">Connect with Stripe</h4>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Set up your payment account to start receiving payments from customers. 
              Stripe handles all the compliance, tax forms, and payouts automatically.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="text-center">
                <Shield className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                <h5 className="font-medium text-gray-900">Secure</h5>
                <p className="text-sm text-gray-600">Bank-level security</p>
              </div>
              <div className="text-center">
                <Zap className="w-8 h-8 text-green-500 mx-auto mb-2" />
                <h5 className="font-medium text-gray-900">Fast Setup</h5>
                <p className="text-sm text-gray-600">5-minute onboarding</p>
              </div>
              <div className="text-center">
                <DollarSign className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                <h5 className="font-medium text-gray-900">Auto Payouts</h5>
                <p className="text-sm text-gray-600">Daily to your bank</p>
              </div>
            </div>

            <button
              onClick={handleConnectStripe}
              disabled={onboarding}
              className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 mx-auto"
            >
              {onboarding ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Setting up...
                </>
              ) : (
                <>
                  <CreditCard className="w-5 h-5" />
                  Connect with Stripe
                  <ExternalLink className="w-4 h-4" />
                </>
              )}
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Account Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CreditCard className="w-5 h-5 text-gray-600" />
                  <span className="font-medium text-gray-900">Charges</span>
                </div>
                <span className={`text-sm font-medium ${account?.charges_enabled ? 'text-green-600' : 'text-red-600'}`}>
                  {account?.charges_enabled ? 'Enabled' : 'Disabled'}
                </span>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="w-5 h-5 text-gray-600" />
                  <span className="font-medium text-gray-900">Payouts</span>
                </div>
                <span className={`text-sm font-medium ${account?.payouts_enabled ? 'text-green-600' : 'text-red-600'}`}>
                  {account?.payouts_enabled ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>

            {/* Balance Display */}
            {account?.balance && (
              <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Account Balance</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm text-gray-600">Available</span>
                    <div className="text-lg font-semibold text-green-600">
                      ${(account.balance.available[0]?.amount || 0) / 100}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600">Pending</span>
                    <div className="text-lg font-semibold text-yellow-600">
                      ${(account.balance.pending[0]?.amount || 0) / 100}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Requirements */}
            {account?.requirements && (account.requirements.currently_due.length > 0 || account.requirements.past_due.length > 0) && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="w-5 h-5 text-red-600" />
                  <span className="font-medium text-red-900">Action Required</span>
                </div>
                <p className="text-sm text-red-700 mb-3">
                  Complete the following requirements to activate your account:
                </p>
                <ul className="text-sm text-red-700 space-y-1">
                  {[...account.requirements.currently_due, ...account.requirements.past_due].map((requirement, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-red-600 rounded-full"></div>
                      {requirement.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </li>
                  ))}
                </ul>
                <button
                  onClick={handleConnectStripe}
                  className="mt-3 bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm"
                >
                  Complete Setup
                </button>
              </div>
            )}

            {/* Success State */}
            {account?.status === 'active' && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-900">Ready to Accept Payments</span>
                </div>
                <p className="text-sm text-green-700">
                  Your account is fully set up! You can now receive payments from customers. 
                  Funds will be automatically transferred to your bank account daily.
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Platform Fee Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Platform Fee Information</h4>
        <p className="text-sm text-blue-700">
          Fetchly charges an 8% platform fee on all transactions. This covers payment processing, 
          platform maintenance, customer support, and marketing to bring you more customers.
        </p>
      </div>
    </div>
  );
}
