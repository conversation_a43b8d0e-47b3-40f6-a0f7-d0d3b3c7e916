'use client';

import React, { useState, useEffect } from 'react';
import { X, Search, MessageCircle, Check } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { useChatStore } from '@/stores/chatStore';
import type { ChatUser } from '@/types/chat';

interface CreateChatModalProps {
  onClose: () => void;
  onChatCreated: (chatId: string) => void;
}

export default function CreateChatModal({ onClose, onChatCreated }: CreateChatModalProps) {
  const { user } = useAuth();
  const { users } = useData();
  const { createChat } = useChatStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<ChatUser[]>([]);
  const [isGroupChat, setIsGroupChat] = useState(false);
  const [groupName, setGroupName] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Filter available users
  const userId = user?.id;
  const availableUsers = users
    .filter((u) => u.id !== userId) // Exclude current user
    .filter((u) => {
      if (!searchQuery) return true;
      const query = searchQuery.toLowerCase();
      return (
        u.name?.toLowerCase().includes(query) ||
        u.email?.toLowerCase().includes(query) ||
        u.role?.toLowerCase().includes(query)
      );
    })
    .map((u) => ({
      uid: u.id,
      displayName: u.name || 'Unknown User',
      userType: u.role as 'pet_owner' | 'provider' | 'support' | 'admin',
      avatarUrl: u.avatar,
      email: u.email,
      isOnline: u.isOnline || false,
    }));

  // Handle user selection - immediately create chat for one-on-one
  const handleUserSelect = async (selectedUser: ChatUser) => {
    // For one-on-one chats, immediately create and open the chat
    if (selectedUsers.length === 0) {
      await handleDirectChat(selectedUser);
    } else {
      // For group chats, toggle selection
      if (selectedUsers.find((u) => u.uid === selectedUser.uid)) {
        setSelectedUsers(selectedUsers.filter((u) => u.uid !== selectedUser.uid));
      } else {
        setSelectedUsers([...selectedUsers, selectedUser]);
      }
    }
  };

  // Handle direct one-on-one chat creation
  const handleDirectChat = async (selectedUser: ChatUser) => {
    const currentUserId = user?.id;
    if (!currentUserId) {
      console.error('No user ID found');
      alert('Please sign in to start a chat');
      return;
    }

    setIsLoading(true);
    try {
      const chatId = await createChat({
        participantIds: [currentUserId, selectedUser.uid],
        isGroupChat: false,
        chatName: selectedUser.displayName,
        initialMessage: `Started a conversation with ${selectedUser.displayName}`,
      });

      onClose();
      onChatCreated(chatId);
    } catch (error: unknown) {
      console.error('Error in direct chat:', error);
      const errorMessage = error instanceof Error ? error.message : 'Please try again later';
      alert(`Error starting chat: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle group chat creation
  const handleCreateGroup = async () => {
    if (selectedUsers.length === 0) {
      console.log('No users selected');
      return;
    }

    const currentUserId = user?.id;
    if (!currentUserId) {
      console.log('No user ID found');
      alert('Please sign in to create a group chat');
      return;
    }

    if (!groupName.trim()) {
      alert('Please enter a group name');
      return;
    }

    setIsLoading(true);
    try {
      const participantIds = [currentUserId, ...selectedUsers.map((u) => u.uid)];
      const chatId = await createChat({
        participantIds,
        isGroupChat: true,
        chatName: groupName.trim(),
        initialMessage: `Created group "${groupName.trim()}"`,
      });

      onClose();
      onChatCreated(chatId);
    } catch (error: unknown) {
      console.error('Error creating group chat:', error);
      const errorMessage = error instanceof Error ? error.message : 'Please try again';
      alert(`Error creating group chat: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-enable group chat mode when multiple users selected
  useEffect(() => {
    setIsGroupChat(selectedUsers.length > 1);
  }, [selectedUsers.length]);

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md max-h-[90vh] bg-white rounded-2xl shadow-2xl overflow-hidden overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-xl font-bold text-gray-800">New Chat</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 transition-colors"
              disabled={isLoading}
              aria-label="Close modal"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search users..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              disabled={isLoading}
              aria-label="Search users"
            />
          </div>
        </div>

        {/* Group Chat Options */}
        {selectedUsers.length > 0 && (
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center mb-3">
              <input
                type="checkbox"
                id="group-chat"
                className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                checked={isGroupChat}
                onChange={(e) => setIsGroupChat(e.target.checked)}
                disabled={selectedUsers.length <= 1}
                aria-label="Create as group chat"
              />
              <label htmlFor="group-chat" className="ml-2 text-sm font-medium text-gray-700">
                Create as group chat
              </label>
            </div>

            {isGroupChat && (
              <div className="mt-2">
                <label htmlFor="group-name" className="block text-sm font-medium text-gray-700 mb-1">
                  Group Name
                </label>
                <input
                  type="text"
                  id="group-name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter group name"
                  value={groupName}
                  onChange={(e) => setGroupName(e.target.value)}
                  disabled={isLoading}
                  aria-label="Group name"
                />
              </div>
            )}
          </div>
        )}

        {/* Selected Users */}
        {selectedUsers.length > 0 && (
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Selected</h3>
            <div className="flex flex-wrap gap-2">
              {selectedUsers.map((selectedUser) => (
                <div
                  key={selectedUser.uid}
                  className="flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium"
                >
                  <span>{selectedUser.displayName}</span>
                  <button
                    type="button"
                    className="ml-2 text-blue-500 hover:text-blue-700"
                    onClick={() =>
                      setSelectedUsers(selectedUsers.filter((u) => u.uid !== selectedUser.uid))
                    }
                    disabled={isLoading}
                    aria-label={`Remove ${selectedUser.displayName}`}
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* User List */}
        <div className="overflow-y-auto max-h-[40vh]">
          {availableUsers.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              {searchQuery ? 'No users found' : 'No users available'}
            </div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {availableUsers.map((availableUser) => {
                const isSelected = selectedUsers.some((u) => u.uid === availableUser.uid);
                return (
                  <li key={availableUser.uid}>
                    <button
                      type="button"
                      className={`w-full px-6 py-4 flex items-center hover:bg-gray-50 transition-colors ${
                        isSelected ? 'bg-blue-50' : ''
                      }`}
                      onClick={() => handleUserSelect(availableUser)}
                      disabled={isLoading}
                      aria-label={`Select ${availableUser.displayName}`}
                    >
                      <div className="flex-shrink-0 relative">
                        <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                          {availableUser.avatarUrl ? (
                            <img
                              src={availableUser.avatarUrl}
                              alt={availableUser.displayName}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <span className="text-gray-500 font-medium">
                              {availableUser.displayName.charAt(0).toUpperCase()}
                            </span>
                          )}
                        </div>
                        {availableUser.isOnline && (
                          <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                        )}
                      </div>
                      <div className="ml-4 text-left flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {availableUser.displayName}
                        </p>
                        <p className="text-xs text-gray-500 truncate">{availableUser.email}</p>
                      </div>
                      {isSelected && (
                        <div className="ml-2 text-blue-600">
                          <Check className="w-5 h-5" />
                        </div>
                      )}
                    </button>
                  </li>
                );
              })}
            </ul>
          )}
        </div>

        {/* Action Buttons */}
        <div className="p-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="button"
            className={`px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
              (isGroupChat ? !groupName.trim() : selectedUsers.length === 0) || isLoading
                ? 'bg-blue-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
            onClick={isGroupChat ? handleCreateGroup : () => selectedUsers[0] && handleUserSelect(selectedUsers[0])}
            disabled={(isGroupChat ? !groupName.trim() : selectedUsers.length === 0) || isLoading}
            aria-label={isGroupChat ? 'Create group chat' : 'Start chat'}
          >
            {isLoading ? (
              'Creating...'
            ) : isGroupChat ? (
              'Create Group Chat'
            ) : (
              'Start Chat'
            )}
          </button>

          {/* Group Chat Button */}
          {selectedUsers.length > 0 && (
            <button
              onClick={handleCreateGroup}
              disabled={isLoading}
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                selectedUsers.length > 0 && !isLoading
                  ? 'bg-gradient-to-r from-green-500 to-blue-500 text-white hover:from-green-600 hover:to-blue-600 shadow-lg'
                  : 'bg-gray-200 text-gray-400 cursor-not-allowed'
              }`}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Creating...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <MessageCircle className="w-4 h-4" />
                  <span>Start Group Chat</span>
                </div>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
