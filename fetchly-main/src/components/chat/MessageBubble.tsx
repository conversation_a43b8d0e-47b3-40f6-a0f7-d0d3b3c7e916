'use client';

import React, { useState } from 'react';
import { 
  Check, 
  CheckCheck, 
  MoreV<PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Co<PERSON>, 
  Trash2,
  Download,
  <PERSON>,
  Heart,
  Smile
} from 'lucide-react';
import { Message } from '@/types/chat';
import { formatDistanceToNow, format } from 'date-fns';
import { useAuth } from '@/contexts/AuthContext';

interface MessageBubbleProps {
  message: Message;
  isOwn: boolean;
  showAvatar: boolean;
}

export default function MessageBubble({ message, isOwn, showAvatar }: MessageBubbleProps) {
  const { user } = useAuth();
  const userId = user?.uid || user?.id;
  const [showMenu, setShowMenu] = useState(false);
  const [showReactions, setShowReactions] = useState(false);

  // Format timestamp
  const formatTime = (date: Date) => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return format(date, 'HH:mm');
    } else if (diffInHours < 168) { // 7 days
      return format(date, 'EEE HH:mm');
    } else {
      return format(date, 'MMM dd, HH:mm');
    }
  };

  // Get read status
  const getReadStatus = () => {
    if (!isOwn) return null;
    
    const readCount = message.readBy.length;
    if (readCount <= 1) return <Check className="w-3 h-3" />;
    return <CheckCheck className="w-3 h-3 text-blue-500" />;
  };

  // Handle copy message
  const handleCopy = () => {
    if (message.text) {
      navigator.clipboard.writeText(message.text);
      setShowMenu(false);
    }
  };

  // Handle download media
  const handleDownload = () => {
    if (message.mediaUrl) {
      const link = document.createElement('a');
      link.href = message.mediaUrl;
      link.download = message.fileName || 'download';
      link.click();
    }
  };

  // Render media content
  const renderMedia = () => {
    if (!message.mediaUrl) return null;

    switch (message.mediaType) {
      case 'image':
        return (
          <div className="relative group">
            <img
              src={message.mediaUrl}
              alt="Shared image"
              className="max-w-xs max-h-64 rounded-lg object-cover cursor-pointer hover:opacity-90 transition-opacity"
              onClick={() => window.open(message.mediaUrl, '_blank')}
            />
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors flex items-center justify-center">
              <Eye className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
          </div>
        );
      
      case 'video':
        return (
          <video
            src={message.mediaUrl}
            controls
            className="max-w-xs max-h-64 rounded-lg"
          />
        );
      
      default:
        return (
          <div className="flex items-center space-x-3 p-3 bg-gray-100 rounded-lg max-w-xs">
            <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
              <Download className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-gray-900 truncate">
                {message.fileName || 'File'}
              </p>
              <p className="text-sm text-gray-500">
                {message.fileSize ? `${(message.fileSize / 1024 / 1024).toFixed(2)} MB` : 'Unknown size'}
              </p>
            </div>
            <button
              onClick={handleDownload}
              className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-200 transition-colors"
            >
              <Download className="w-4 h-4 text-gray-600" />
            </button>
          </div>
        );
    }
  };

  // Render reactions
  const renderReactions = () => {
    if (!message.reactions || Object.keys(message.reactions).length === 0) return null;

    return (
      <div className="flex items-center space-x-1 mt-1">
        {Object.entries(message.reactions).map(([emoji, userIds]) => (
          <button
            key={emoji}
            className="flex items-center space-x-1 px-2 py-1 bg-gray-100 rounded-full text-xs hover:bg-gray-200 transition-colors"
          >
            <span>{emoji}</span>
            <span className="text-gray-600">{userIds.length}</span>
          </button>
        ))}
      </div>
    );
  };

  if (message.isDeleted) {
    return (
      <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'} mb-2`}>
        <div className="max-w-xs lg:max-w-md">
          <div className={`px-4 py-2 rounded-2xl ${
            isOwn 
              ? 'bg-gray-200 text-gray-500' 
              : 'bg-gray-100 text-gray-500'
          }`}>
            <p className="text-sm italic">This message was deleted</p>
            <div className="flex items-center justify-between mt-1">
              <span className="text-xs text-gray-400">
                {formatTime(message.timestamp)}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'} mb-2 group`}>
      <div className="max-w-xs lg:max-w-md flex items-end space-x-2">
        {/* Avatar */}
        {!isOwn && showAvatar && (
          <img
            src={message.senderAvatar || '/default-avatar.png'}
            alt={message.senderName}
            className="w-8 h-8 rounded-full object-cover border-2 border-white shadow-sm"
            onError={(e) => {
              e.currentTarget.src = '/default-avatar.png';
            }}
          />
        )}
        {!isOwn && !showAvatar && <div className="w-8" />}

        <div className="flex-1">
          {/* Sender name for group chats */}
          {!isOwn && showAvatar && (
            <p className="text-xs text-gray-500 mb-1 ml-3">{message.senderName}</p>
          )}

          {/* Reply indicator */}
          {message.replyTo && (
            <div className="mb-2 ml-3">
              <div className="flex items-center space-x-2 text-xs text-gray-500">
                <Reply className="w-3 h-3" />
                <span>Replying to message</span>
              </div>
            </div>
          )}

          {/* Message bubble */}
          <div className="relative">
            <div className={`px-4 py-2 rounded-2xl ${
              isOwn 
                ? 'bg-gradient-to-r from-green-500 to-blue-500 text-white' 
                : 'bg-white border border-gray-200 text-gray-900 shadow-sm'
            } ${
              isOwn ? 'rounded-br-md' : 'rounded-bl-md'
            }`}>
              {/* Media content */}
              {renderMedia()}
              
              {/* Text content */}
              {message.text && (
                <p className="text-sm whitespace-pre-wrap break-words">
                  {message.text}
                </p>
              )}

              {/* Message info */}
              <div className="flex items-center justify-between mt-1 space-x-2">
                <span className={`text-xs ${
                  isOwn ? 'text-white/70' : 'text-gray-400'
                }`}>
                  {formatTime(message.timestamp)}
                  {message.editedAt && (
                    <span className="ml-1">(edited)</span>
                  )}
                </span>
                
                <div className="flex items-center space-x-1">
                  {getReadStatus()}
                </div>
              </div>
            </div>

            {/* Message menu */}
            <div className={`absolute top-0 ${isOwn ? 'left-0' : 'right-0'} transform ${
              isOwn ? '-translate-x-full' : 'translate-x-full'
            } opacity-0 group-hover:opacity-100 transition-opacity`}>
              <div className="flex items-center space-x-1 p-1">
                <button
                  onClick={() => setShowReactions(!showReactions)}
                  className="w-6 h-6 flex items-center justify-center rounded-full bg-white border border-gray-200 hover:bg-gray-50 transition-colors"
                >
                  <Smile className="w-3 h-3 text-gray-600" />
                </button>
                <button
                  onClick={() => setShowMenu(!showMenu)}
                  className="w-6 h-6 flex items-center justify-center rounded-full bg-white border border-gray-200 hover:bg-gray-50 transition-colors"
                >
                  <MoreVertical className="w-3 h-3 text-gray-600" />
                </button>
              </div>
            </div>

            {/* Context menu */}
            {showMenu && (
              <div className={`absolute top-8 ${isOwn ? 'left-0' : 'right-0'} w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10`}>
                <button
                  onClick={() => {/* Handle reply */}}
                  className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                >
                  <Reply className="w-4 h-4" />
                  Reply
                </button>
                {message.text && (
                  <button
                    onClick={handleCopy}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                  >
                    <Copy className="w-4 h-4" />
                    Copy Text
                  </button>
                )}
                {message.mediaUrl && (
                  <button
                    onClick={handleDownload}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Download
                  </button>
                )}
                {isOwn && (
                  <button
                    onClick={() => {/* Handle delete */}}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2"
                  >
                    <Trash2 className="w-4 h-4" />
                    Delete
                  </button>
                )}
              </div>
            )}

            {/* Reaction picker */}
            {showReactions && (
              <div className={`absolute top-8 ${isOwn ? 'left-0' : 'right-0'} bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-10`}>
                <div className="flex items-center space-x-2">
                  {['❤️', '😂', '😮', '😢', '😡', '👍', '👎'].map((emoji) => (
                    <button
                      key={emoji}
                      onClick={() => {/* Handle reaction */}}
                      className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors text-lg"
                    >
                      {emoji}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Reactions */}
          {renderReactions()}
        </div>
      </div>
    </div>
  );
}
