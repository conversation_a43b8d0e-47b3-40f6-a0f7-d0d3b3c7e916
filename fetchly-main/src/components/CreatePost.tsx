'use client';

import { useState, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { uploadImage, generateStoragePath } from '@/lib/storage';
import toast from 'react-hot-toast';
import {
  Camera, Globe, Lock, X, Smile,
  Send
} from 'lucide-react';

interface CreatePostProps {
  placeholder?: string;
  defaultPublic?: boolean;
  onPostCreated?: () => void;
}

export default function CreatePost({ 
  placeholder = "What's happening with your pets today?",
  defaultPublic = true,
  onPostCreated
}: CreatePostProps) {
  const { user } = useAuth();
  const { createPost } = useData();
  const [content, setContent] = useState('');
  const [isPublic, setIsPublic] = useState(defaultPublic);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isPosting, setIsPosting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setUploadProgress(0);
  };

  // Common emojis for quick access
  const commonEmojis = ['😀', '😂', '🥰', '😍', '🤔', '😢', '😡', '👍', '❤️', '🐶', '🐱', '🐾', '🎉', '🔥', '💯'];

  const handleEmojiClick = (emoji: string) => {
    const textarea = textareaRef.current;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = content.slice(0, start) + emoji + content.slice(end);
      setContent(newContent);

      // Set cursor position after emoji
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + emoji.length, start + emoji.length);
      }, 0);
    } else {
      setContent(prev => prev + emoji);
    }
    setShowEmojiPicker(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim() && !selectedImage) return;
    if (!user?.id) return;

    try {
      setIsPosting(true);
      let imageUrl = '';

      // Upload image if selected
      if (selectedImage) {
        const path = generateStoragePath(
          user.id,
          'posts',
          selectedImage.name
        );
        
        const result = await uploadImage(selectedImage, path, (progress) => {
          setUploadProgress(progress.progress);
        });
        
        if (result.success && result.url) {
          imageUrl = result.url;
        } else {
          throw new Error('Failed to upload image');
        }
      }

      // Create post using unified function
      await createPost(content, imageUrl, isPublic, false); // false = not a story

      toast.success('Post created successfully!');

      // Reset form
      setContent('');
      setSelectedImage(null);
      setImagePreview(null);
      setUploadProgress(0);
      setIsPublic(defaultPublic);

      // Callback
      onPostCreated?.();

    } catch (error) {
      console.error('Error creating content:', error);
      toast.error('Failed to create post. Please try again.');
    } finally {
      setIsPosting(false);
    }
  };

  const canPost = (content.trim() || selectedImage) && !isPosting;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* User Info */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <img
              src={user?.avatar || 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face'}
              alt={user?.name || 'User'}
              className="w-10 h-10 rounded-full object-cover"
            />
            <div>
              <p className="font-medium text-gray-900">{user?.name || 'Pet Owner'}</p>
              <div className="flex items-center space-x-1">
                <button
                  type="button"
                  onClick={() => setIsPublic(!isPublic)}
                  className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium transition-colors ${
                    isPublic
                      ? 'bg-green-100 text-green-700 hover:bg-green-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {isPublic ? (
                    <>
                      <Globe className="w-3 h-3" />
                      <span>Online</span>
                    </>
                  ) : (
                    <>
                      <Lock className="w-3 h-3" />
                      <span>Private</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

        </div>

        {/* Content Input */}
        <div className="space-y-3">
          <textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder={placeholder}
            className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            rows={3}
            maxLength={2000}
          />
          
          {/* Character Count */}
          <div className="flex justify-between items-center text-sm text-gray-500">
            <span>{content.length}/2000 characters</span>
          </div>
        </div>

        {/* Image Preview */}
        {imagePreview && (
          <div className="relative">
            <img
              src={imagePreview}
              alt="Preview"
              className="w-full max-h-64 object-cover rounded-lg"
            />
            <button
              type="button"
              onClick={removeImage}
              className="absolute top-2 right-2 p-1 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70"
            >
              <X className="w-4 h-4" />
            </button>
            
            {/* Upload Progress */}
            {uploadProgress > 0 && uploadProgress < 100 && (
              <div className="absolute bottom-2 left-2 right-2">
                <div className="bg-black bg-opacity-50 rounded-full p-2">
                  <div className="w-full bg-gray-300 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-white text-xs text-center mt-1">{uploadProgress}% uploaded</p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center space-x-4">
            {/* Image Upload */}
            <label className="cursor-pointer text-blue-600 hover:text-blue-700 transition-colors p-2 rounded-full hover:bg-blue-50">
              <Camera className="w-5 h-5" />
              <input
                type="file"
                accept="image/*"
                onChange={handleImageSelect}
                className="hidden"
                disabled={isPosting}
              />
            </label>

            {/* Emoji Picker */}
            <div className="relative">
              <button
                type="button"
                onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                className="text-yellow-600 hover:text-yellow-700 transition-colors p-2 rounded-full hover:bg-yellow-50"
              >
                <Smile className="w-5 h-5" />
              </button>

              {/* Emoji Picker Dropdown */}
              {showEmojiPicker && (
                <div className="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-10">
                  <div className="grid grid-cols-5 gap-2">
                    {commonEmojis.map((emoji, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => handleEmojiClick(emoji)}
                        className="text-xl hover:bg-gray-100 rounded p-1 transition-colors"
                      >
                        {emoji}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Post Button */}
          <button
            type="submit"
            disabled={!canPost}
            className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 ${
              canPost
                ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm hover:shadow-md'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isPosting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Posting...</span>
              </>
            ) : (
              <>
                <Send className="w-4 h-4" />
                <span>Post</span>
              </>
            )}
          </button>
        </div>
      </form>

      {/* Click outside to close emoji picker */}
      {showEmojiPicker && (
        <div
          className="fixed inset-0 z-5"
          onClick={() => setShowEmojiPicker(false)}
        />
      )}
    </div>
  );
}
