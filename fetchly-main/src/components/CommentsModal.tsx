'use client';

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { X, Heart, MessageCircle, Send, Reply, Trash2, Image as ImageIcon, Video, Smile, MoreHorizontal, EyeOff, Edit2 } from 'lucide-react';
import dynamic from 'next/dynamic';

// Types
type AppUser = {
  uid: string;
  displayName: string | null;
  photoURL: string | null;
  email: string | null;
  name?: string;
  avatar?: string;
  role?: 'provider' | 'petowner';
};
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  Timestamp,
  increment
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import toast from 'react-hot-toast';
import NoSSR from './NoSSR';
import { translateSpanishToEnglish, needsTranslation } from '@/lib/translation';

interface MediaItem {
  type: 'image' | 'video' | 'gif';
  url: string;
  alt?: string;
  width?: number;
  height?: number;
  file?: File; // Add file reference for uploaded files
}

interface Comment {
  id: string;
  postId: string;
  userId: string;
  userName: string;
  userAvatar: string;
  userRole: 'provider' | 'petowner';
  content: string;
  media?: MediaItem[];
  timestamp: any; // Firestore timestamp or Date
  likes: number;
  likedBy: string[];
  replies?: Comment[];
  parentId?: string;
}

interface CommentsModalProps {
  postId: string;
  postAuthor: string;
  isOpen: boolean;
  onClose: () => void;
}

export default function CommentsModal({ postId, isOpen, onClose }: CommentsModalProps) {
  const { user } = useAuth() as { user: AppUser | null };
  const router = useRouter();
  const [comments, setComments] = useState<Comment[]>([]);
  // Comment state with proper type
  const [comment, setComment] = useState<string>('');
  // Helper function to safely get comment text
  const getCommentText = (comment: string | Comment | null | undefined): string => {
    if (!comment) return '';
    if (typeof comment === 'string') return comment;
    if (typeof comment === 'object' && 'content' in comment) return comment.content;
    return '';
  };
  
  // Helper function to safely trim comment text
  const trimComment = (text: string | null | undefined): string => {
    if (!text) return '';
    return text.trim();
  };
  const handleEmojiClick = (emojiData: any) => {
    const emoji = emojiData.emoji || '';
    setComment(prev => {
      const currentText = getCommentText(prev);
      return currentText + emoji;
    });
    setShowEmojiPicker(false);
  };
  const [replyingTo, setReplyingTo] = useState<Comment | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [media, setMedia] = useState<File[]>([]);
  const [showCommentMenu, setShowCommentMenu] = useState<string | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  // GIPHY picker component - using a simple button for now
  const GifPicker = () => (
    <div className="p-4 text-sm text-gray-500">
      GIF picker integration coming soon
    </div>
  );
  const [showGifPicker, setShowGifPicker] = useState(false);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [translatedComments, setTranslatedComments] = useState<{ [key: string]: string }>({});
  const [showTranslation, setShowTranslation] = useState<{ [key: string]: boolean }>({});
  const [hiddenComments, setHiddenComments] = useState<Set<string>>(new Set());
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editText, setEditText] = useState('');

  // Load comments
  useEffect(() => {
    if (!isOpen || !postId) return;

    // If user is not authenticated, don't load comments
    if (!user) {
      setLoading(false);
      return;
    }

    setLoading(true);
    const commentsQuery = query(
      collection(db, 'comments'),
      where('postId', '==', postId),
      orderBy('timestamp', 'desc')
    );

    const unsubscribe = onSnapshot(commentsQuery, (snapshot) => {
      // Use a Set to track processed comment IDs to prevent duplicates
      const processedIds = new Set();
      const commentsData: Comment[] = [];
      
      snapshot.docChanges().forEach((change) => {
        // Only process added or modified comments
        if (change.type === 'added' || change.type === 'modified') {
          const commentId = change.doc.id;
          if (!processedIds.has(commentId)) {
            processedIds.add(commentId);
            commentsData.push({
              id: commentId,
              ...change.doc.data(),
              timestamp: change.doc.data().timestamp?.toDate() || new Date()
            } as Comment);
          }
        }
      });

      // Organize comments and replies
      const topLevelComments = commentsData
        .filter(comment => !comment.parentId)
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
        
      const repliesMap = commentsData
        .filter(comment => comment.parentId)
        .reduce((acc, reply) => {
          if (!acc[reply.parentId!]) {
            acc[reply.parentId!] = [];
          }
          acc[reply.parentId!].push(reply);
          return acc;
        }, {} as Record<string, Comment[]>);

      // Sort replies by timestamp (newest first)
      Object.values(repliesMap).forEach(replies => {
        replies.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      });

      const commentsWithReplies = topLevelComments.map(comment => ({
        ...comment,
        replies: repliesMap[comment.id] || []
      }));

      setComments(commentsWithReplies);
      setLoading(false);
    }, (error) => {
      console.error('Error loading comments:', error);
      toast.error('Failed to load comments');
      setLoading(false);
    });

    return () => unsubscribe();
  }, [isOpen, postId]);

  // Submit new comment
  const handleMediaUpload = async (files: File[]): Promise<MediaItem[]> => {
    const uploadedMedia: MediaItem[] = [];
    
    for (const file of files) {
      const fileType = file.type.split('/')[0];
      
      // Ensure we only process images and videos
      if (fileType !== 'image' && fileType !== 'video') continue;
      
      // For GIFs, we might have a specific type
      const isGif = file.type === 'image/gif';
      
      uploadedMedia.push({
        type: isGif ? 'gif' : fileType as 'image' | 'video',
        url: URL.createObjectURL(file),
        alt: file.name,
        file: file // Keep reference to the original file
      });
    }
    
    return uploadedMedia;
  };

  const handleGifSelect = (gif: any) => {
    if (!gif) return;
    
    // Create a media item for the selected GIF
    const gifMedia: MediaItem = {
      type: 'gif',
      url: gif.images.original.url,
      alt: gif.title || 'GIF'
    };
    
    // In a real app, you would upload the GIF to your storage
    // For now, we'll just use the GIPHY URL directly
    setMedia(prev => [...prev, new File([], 'gif.gif', { type: 'image/gif' })]);
    setShowGifPicker(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const commentText = typeof comment === 'string' ? comment : '';
    if ((!trimComment(commentText) && media.length === 0) || !user) return;

    try {
      setIsSubmitting(true);
      
      // Upload media files if any
      const uploadedMedia = media.length > 0 ? await handleMediaUpload(media) : undefined;
      
      const commentData: Omit<Comment, 'id' | 'timestamp' | 'likes' | 'likedBy' | 'replies'> = {
        postId,
        userId: user.uid,
        userName: user.displayName || user.name || 'Anonymous',
        userAvatar: user.photoURL || user.avatar || '',
        userRole: user.role || 'petowner',
        content: comment,
        ...(uploadedMedia && { media: uploadedMedia }),
        ...(replyingTo && { parentId: replyingTo.id })
      };

      await addDoc(collection(db, 'comments'), {
        ...commentData,
        timestamp: new Date(),
        likes: 0,
        likedBy: []
      });

      setComment('');
      setMedia([]);
      setReplyingTo(null);
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error('Failed to add comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Submit reply
  const handleSubmitReply = async (parentId: string) => {
    const commentText = typeof comment === 'string' ? comment : '';
    if (!trimComment(commentText) || !user || isSubmitting) return;

    try {
      setIsSubmitting(true);
      
      const replyData: Omit<Comment, 'id' | 'timestamp' | 'likes' | 'likedBy' | 'replies'> = {
        postId,
        userId: user.uid,
        userName: user.displayName || 'Anonymous',
        userAvatar: user.photoURL || '',
        userRole: 'petowner', // Default role, update as needed
        content: comment,
        parentId
      };

      await addDoc(collection(db, 'comments'), {
        ...replyData,
        timestamp: new Date(),
        likes: 0,
        likedBy: []
      });

      setComment('');
      setReplyingTo(null);
    } catch (error) {
      console.error('Error adding reply:', error);
      toast.error('Failed to add reply');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Like comment
  const handleLikeComment = async (commentId: string) => {
    if (!user) {
      router.push('/login');
      return;
    }

    try {
      const comment = comments.find(c => c.id === commentId);
      if (!comment) return;

      const isLiked = comment.likedBy.includes(user.uid);
      const newLikedBy = isLiked 
        ? comment.likedBy.filter(id => id !== user.uid)
        : [...comment.likedBy, user.uid];

      const commentRef = doc(db, 'comments', commentId);
      await updateDoc(commentRef, {
        likes: isLiked ? comment.likes - 1 : comment.likes + 1,
        likedBy: newLikedBy
      });
    } catch (error) {
      console.error('Error toggling like:', error);
      toast.error('Failed to update like');
    }
  };

  // Delete comment
  const handleDeleteComment = async (commentId: string) => {
    if (!user) return;

    try {
      await deleteDoc(doc(db, 'comments', commentId));

      // Update post comment count
      const postRef = doc(db, 'posts', postId);
      await updateDoc(postRef, {
        comments: increment(-1)
      });

      toast.success('Comment deleted');
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast.error('Failed to delete comment');
    }
  };

  const handleHideComment = (commentId: string) => {
    setHiddenComments(prev => new Set([...prev, commentId]));
    setShowCommentMenu(null);
  };

  const handleEditComment = (commentId: string, currentText: string) => {
    setEditingComment(commentId);
    setEditText(currentText);
    setShowCommentMenu(null);
  };

  const handleSaveEdit = async (commentId: string) => {
    if (!editText.trim()) return;

    try {
      // Update comment in Firebase
      await updateDoc(doc(db, 'comments', commentId), {
        content: editText.trim(),
        updatedAt: new Date()
      });

      // Update local state
      setComments(prev => prev.map(comment =>
        comment.id === commentId
          ? { ...comment, content: editText.trim() }
          : comment
      ));

      setEditingComment(null);
      setEditText('');
      toast.success('Comment updated');
    } catch (error) {
      console.error('Error updating comment:', error);
      toast.error('Failed to update comment');
    }
  };

  const handleCancelEdit = () => {
    setEditingComment(null);
    setEditText('');
  };

  const formatTimeAgo = (timestamp: any) => {
    try {
      // Handle Firebase Timestamp or Date object
      let date: Date;

      if (!timestamp) {
        return 'Just now';
      }

      // If it's a Firebase Timestamp
      if (timestamp.toDate && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      }
      // If it's already a Date object
      else if (timestamp instanceof Date) {
        date = timestamp;
      }
      // If it's a timestamp number
      else if (typeof timestamp === 'number') {
        date = new Date(timestamp);
      }
      // If it's a string
      else if (typeof timestamp === 'string') {
        date = new Date(timestamp);
      }
      // Fallback
      else {
        return 'Just now';
      }

      // Validate the date
      if (isNaN(date.getTime())) {
        return 'Just now';
      }

      const now = new Date();
      const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

      if (diffInSeconds < 60) return 'Just now';
      if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
      if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
      if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting time:', error, 'timestamp:', timestamp);
      return 'Just now';
    }
  };

  // Toggle translation for a comment
  const toggleTranslation = (commentId: string, text: string) => {
    if (showTranslation[commentId]) {
      // Hide translation, show original
      setShowTranslation(prev => ({ ...prev, [commentId]: false }));
    } else {
      // Show translation
      if (!translatedComments[commentId]) {
        // Translate and cache
        const translated = translateSpanishToEnglish(text);
        setTranslatedComments(prev => ({ ...prev, [commentId]: translated }));
      }
      setShowTranslation(prev => ({ ...prev, [commentId]: true }));
    }
  };

  // Handle username click to navigate to profile
  const handleUsernameClick = (userId: string, userName: string, userRole?: string) => {
    if (!userId) return;

    // Determine if user is a provider based on role or username
    const isProvider = userRole === 'provider' ||
                      userName?.toLowerCase().includes('provider') ||
                      userName?.toLowerCase().includes('vet') ||
                      userName?.toLowerCase().includes('groomer') ||
                      userName?.toLowerCase().includes('trainer');

    if (isProvider) {
      console.log('🏢 Navigating to provider profile:', `/provider/public/${userId}`);
      router.push(`/provider/public/${userId}`);
    } else {
      console.log('🐕 Navigating to pet owner profile:', `/profile?id=${userId}`);
      router.push(`/profile?id=${userId}`);
    }
  };

  if (!isOpen) return null;

  return typeof window !== 'undefined' ? createPortal(
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex items-center justify-center p-4">
      <div className="bg-white rounded-3xl shadow-2xl w-full max-w-md h-[600px] flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800">
            Comments ({comments.reduce((total, comment) => total + 1 + (comment.replies?.length || 0), 0)})
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Comments List */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {!user ? (
            <div className="text-center py-8">
              <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Sign in to view comments</h3>
              <p className="text-gray-500 mb-6">Join the community to see what others are saying!</p>
              <div className="flex justify-center space-x-4">
                <Link
                  href={`/auth/signin?redirect=${encodeURIComponent(window.location.pathname)}`}
                  className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                >
                  Sign In
                </Link>
                <Link
                  href="/auth/signup"
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition-colors"
                >
                  Sign Up
                </Link>
              </div>
            </div>
          ) : loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading comments...</p>
            </div>
          ) : comments.length > 0 ? (
            comments.filter(comment => !hiddenComments.has(comment.id)).map((comment) => (
              <div key={comment.id} className="space-y-4">
                {/* Main Comment */}
                <div className="flex space-x-3">
                  <img
                    src={comment.userAvatar}
                    alt={comment.userName}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleUsernameClick(comment.userId, comment.userName, comment.userRole)}
                            className="font-semibold text-gray-900 hover:text-blue-600 transition-colors cursor-pointer"
                          >
                            {comment.userName}
                          </button>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            comment.userRole === 'provider' 
                              ? 'bg-blue-100 text-blue-700' 
                              : 'bg-green-100 text-green-700'
                          }`}>
                            {comment.userRole === 'provider' ? 'Provider' : 'Pet Owner'}
                          </span>
                        </div>
                        <span className="text-sm text-gray-500">
                          <NoSSR fallback="Just now">
                            {formatTimeAgo(comment.timestamp)}
                          </NoSSR>
                        </span>
                      </div>

                      {/* Comment Content with Translation and Edit */}
                      <div className="space-y-2">
                        {editingComment === comment.id ? (
                          /* Edit Mode */
                          <div className="space-y-2">
                            <textarea
                              value={editText}
                              onChange={(e) => setEditText(e.target.value)}
                              className="w-full p-2 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              rows={3}
                              placeholder="Edit your comment..."
                            />
                            <div className="flex space-x-2">
                              <button
                                onClick={() => handleSaveEdit(comment.id)}
                                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700"
                              >
                                Save
                              </button>
                              <button
                                onClick={handleCancelEdit}
                                className="px-3 py-1 bg-gray-300 text-gray-700 text-sm rounded-lg hover:bg-gray-400"
                              >
                                Cancel
                              </button>
                            </div>
                          </div>
                        ) : (
                          /* Display Mode */
                          <>
                            <p className="text-gray-700">
                              {showTranslation[comment.id]
                                ? translatedComments[comment.id] || comment.content
                                : comment.content
                              }
                            </p>

                            {/* Translation Button */}
                            {needsTranslation(comment.content) && (
                              <button
                                onClick={() => toggleTranslation(comment.id, comment.content)}
                                className="text-xs text-blue-600 hover:text-blue-700 flex items-center space-x-1"
                              >
                                <span>🌐</span>
                                <span>
                                  {showTranslation[comment.id] ? 'Show Original' : 'Translate to English'}
                                </span>
                              </button>
                            )}
                          </>
                        )}
                      </div>

                      {/* Media */}
                      {comment.media && (
                        <div className="mt-2">
                          {comment.media.map((mediaItem, index) => (
                            <div key={index} className="mb-2">
                              {mediaItem.type === 'image' ? (
                                <img
                                  src={mediaItem.url}
                                  alt={mediaItem.alt}
                                  className="w-full h-40 object-cover rounded-lg"
                                />
                              ) : (
                                <div className="relative w-full h-40 rounded-lg overflow-hidden">
                                  <video
                                    src={mediaItem.url}
                                    className="w-full h-full object-cover"
                                    controls
                                    title={mediaItem.alt}
                                  >
                                    <source src={mediaItem.url} type={`video/${mediaItem.url.split('.').pop()}`} />
                                    Your browser does not support the video tag.
                                  </video>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Comment Actions */}
                    <div className="flex items-center space-x-4 mt-2">
                      <button
                        onClick={() => handleLikeComment(comment.id)}
                        className={`flex items-center space-x-1 text-sm transition-colors ${
                          comment.likedBy.includes(user?.uid || '')
                            ? 'text-red-500 hover:text-red-600'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                        title={comment.likes > 0 ? `${comment.likes} ${comment.likes === 1 ? 'like' : 'likes'}` : 'Like'}
                      >
                        <Heart className={`w-4 h-4 ${comment.likedBy.includes(user?.uid || '') ? 'fill-current' : ''}`} />
                        {comment.likes > 0 && <span className="text-xs">{comment.likes}</span>}
                      </button>
                      
                      <button
                        onClick={() => setReplyingTo(comment)}
                        className="text-gray-500 hover:text-gray-700"
                        title="Reply"
                      >
                        <Reply className="w-4 h-4" />
                      </button>
                      
                      <div className="relative">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowCommentMenu(showCommentMenu === comment.id ? null : comment.id);
                          }}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <MoreHorizontal className="w-4 h-4" />
                        </button>
                        
                        {showCommentMenu === comment.id && (
                          <div 
                            className="absolute right-0 mt-1 w-40 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <button
                              onClick={() => handleHideComment(comment.id)}
                              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <EyeOff className="w-4 h-4 mr-2" />
                              Hide
                            </button>
                            
                            {user?.uid === comment.userId && (
                              <>
                                <button
                                  onClick={() => handleEditComment(comment.id, comment.content)}
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  <Edit2 className="w-4 h-4 mr-2" />
                                  Edit
                                </button>
                                
                                <button
                                  onClick={() => {
                                    if (confirm('Are you sure you want to delete this comment?')) {
                                      handleDeleteComment(comment.id);
                                    }
                                    setShowCommentMenu(null);
                                  }}
                                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                                >
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Delete
                                </button>
                              </>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Reply Form */}
                    {replyingTo === comment && user && (
                      <form
                        onSubmit={(e) => {
                          e.preventDefault();
                          handleSubmitReply(comment.id);
                        }}
                        className="mt-3 flex space-x-3"
                      >
                        <img
                          src={user.avatar || '/favicon.png'}
                          alt={user.name}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                        <div className="flex-1 flex space-x-2">
                          <input
                            type="text"
                            value={getCommentText(comment)}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                              setComment(e.target.value)
                            }
                            placeholder={`Reply to ${comment.userName}...`}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          <button
                            type="submit"
                            disabled={!comment.content.trim() || isSubmitting}
                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <Send className="w-4 h-4" />
                          </button>
                        </div>
                      </form>
                    )}

                    {/* Replies */}
                    {comment.replies && comment.replies.length > 0 && (
                      <div className="mt-4 ml-6 space-y-3">
                        {comment.replies.map((reply) => (
                          <div key={reply.id} className="flex space-x-3">
                            <img
                              src={reply.userAvatar}
                              alt={reply.userName}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                            <div className="flex-1">
                              <div className="bg-gray-100 rounded-lg p-3">
                                <div className="flex items-center justify-between mb-1">
                                  <div className="flex items-center space-x-2">
                                    <button
                                      onClick={() => handleUsernameClick(reply.userId, reply.userName, reply.userRole)}
                                      className="font-medium text-gray-900 text-sm hover:text-blue-600 transition-colors cursor-pointer"
                                    >
                                      {reply.userName}
                                    </button>
                                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                                      reply.userRole === 'provider' 
                                        ? 'bg-blue-100 text-blue-700' 
                                        : 'bg-green-100 text-green-700'
                                    }`}>
                                      {reply.userRole === 'provider' ? 'Provider' : 'Pet Owner'}
                                    </span>
                                  </div>
                                  <span className="text-xs text-gray-500">
                                    <NoSSR fallback="Just now">
                                      {formatTimeAgo(reply.timestamp)}
                                    </NoSSR>
                                  </span>
                                </div>

                                {/* Reply Content with Translation */}
                                <div className="space-y-1">
                                  <p className="text-gray-700 text-sm">
                                    {showTranslation[reply.id]
                                      ? translatedComments[reply.id] || reply.content
                                      : reply.content
                                    }
                                  </p>

                                  {/* Translation Button for Reply */}
                                  {needsTranslation(reply.content) && (
                                    <button
                                      onClick={() => toggleTranslation(reply.id, reply.content)}
                                      className="text-xs text-blue-600 hover:text-blue-700 flex items-center space-x-1"
                                    >
                                      <span>🌐</span>
                                      <span>
                                        {showTranslation[reply.id] ? 'Show Original' : 'Translate'}
                                      </span>
                                    </button>
                                  )}
                                </div>
                              </div>
                              
                              <div className="flex items-center space-x-3 mt-1">
                                <button
                                  onClick={() => handleLikeComment(reply.id)}
                                  className={`flex items-center space-x-1 text-xs transition-colors ${
                                    reply.likedBy.includes(user?.uid || '')
                                      ? 'text-red-500 hover:text-red-600'
                                      : 'text-gray-500 hover:text-gray-700'
                                  }`}
                                  title={reply.likes > 0 ? `${reply.likes} ${reply.likes === 1 ? 'like' : 'likes'}` : 'Like'}
                                >
                                  <Heart className={`w-3 h-3 ${reply.likedBy.includes(user?.uid || '') ? 'fill-current' : ''}`} />
                                  {reply.likes > 0 && <span className="text-xs">{reply.likes}</span>}
                                </button>
                                
                                {user?.uid === reply.userId && (
                                  <button
                                    onClick={() => handleDeleteComment(reply.id)}
                                    className="text-red-500 hover:text-red-600"
                                    title="Delete"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No comments yet</h3>
              <p className="text-gray-500">Be the first to comment on this post!</p>
            </div>
          )}
        </div>

        {/* Comment Form */}
        <div className="border-t border-gray-200 p-6">
          {user ? (
            <form onSubmit={handleSubmit} className="flex space-x-3">
              <img
                src={user.avatar || '/favicon.png'}
                alt={user.name}
                className="w-10 h-10 rounded-full object-cover"
              />
              <div className="flex-1 flex space-x-3">
                <input
                  type="text"
                  value={getCommentText(comment)}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                    setComment(e.target.value)
                  }
                  placeholder="Add a comment..."
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  type="submit"
                  disabled={!comment.trim() || isSubmitting}
                  className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </form>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-600 mb-4">Sign in to leave a comment</p>
              <div className="flex justify-center space-x-4">
                <Link
                  href={`/auth/signin?redirect=${encodeURIComponent(window.location.pathname)}`}
                  className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                >
                  Sign In
                </Link>
                <Link
                  href="/auth/signup"
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition-colors"
                >
                  Sign Up
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>,
    document.body
  ) : null;
}
