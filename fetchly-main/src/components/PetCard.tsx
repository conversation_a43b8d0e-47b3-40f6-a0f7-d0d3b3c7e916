'use client';

import { useState } from 'react';
import { Edit, PawPrint, AlertTriangle } from 'lucide-react';
import { Pet, Vaccination } from '@/lib/pets';
import { VaccinationService, PetHelpers } from '@/lib/pets';

interface PetCardProps {
  pet: Pet;
  onUpdate: () => void;
}

export default function PetCard({ pet, onUpdate }: PetCardProps) {
  const [showVaccinations, setShowVaccinations] = useState(false);
  const [vaccinations, setVaccinations] = useState<Vaccination[]>([]);

  // Load vaccinations for this pet
  const loadVaccinations = async () => {
    try {
      const petVaccinations = await VaccinationService.getVaccinationsByPet(pet.id);
      setVaccinations(petVaccinations);
    } catch (error) {
      console.error('Error loading vaccinations:', error);
    }
  };

  const upcomingVaccinations = vaccinations.filter(v =>
    VaccinationService.isVaccinationDueSoon(v) || VaccinationService.isVaccinationOverdue(v)
  );

  const handleShowVaccinations = () => {
    if (!showVaccinations) {
      loadVaccinations();
    }
    setShowVaccinations(!showVaccinations);
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
            {pet.profileImage ? (
              <img
                src={pet.profileImage}
                alt={pet.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <PawPrint className="h-6 w-6 text-gray-400" />
            )}
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{pet.name}</h3>
            <p className="text-sm text-gray-600 capitalize">{pet.type} • {pet.breed}</p>
          </div>
        </div>
        <button
          onClick={() => {/* TODO: Add edit functionality */}}
          className="text-gray-400 hover:text-gray-600"
        >
          <Edit className="h-4 w-4" />
        </button>
      </div>

      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Gender:</span>
          <span className="capitalize">{pet.gender}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Size:</span>
          <span className="capitalize">{pet.size.replace('_', ' ')}</span>
        </div>
        {pet.dateOfBirth && (
          <div className="flex justify-between">
            <span className="text-gray-600">Age:</span>
            <span>{PetHelpers.formatAge(pet.dateOfBirth)}</span>
          </div>
        )}
        {pet.weight && (
          <div className="flex justify-between">
            <span className="text-gray-600">Weight:</span>
            <span>{pet.weight} lbs</span>
          </div>
        )}
      </div>

      {upcomingVaccinations.length > 0 && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-800">
              {upcomingVaccinations.length} vaccination(s) due soon
            </span>
          </div>
        </div>
      )}

      <div className="mt-4 flex space-x-2">
        <button
          onClick={handleShowVaccinations}
          className="flex-1 text-sm bg-gray-100 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-200"
        >
          {showVaccinations ? 'Hide' : 'Show'} Vaccinations ({vaccinations.length})
        </button>
      </div>

      {showVaccinations && (
        <div className="mt-4 space-y-2">
          {vaccinations.length === 0 ? (
            <p className="text-sm text-gray-600">No vaccination records yet.</p>
          ) : (
            vaccinations.map((vaccination) => (
              <div key={vaccination.id} className="text-sm border-l-4 border-blue-200 pl-3 py-1">
                <div className="font-medium">{vaccination.vaccineName}</div>
                <div className="text-gray-600">
                  Given: {new Date(vaccination.dateAdministered).toLocaleDateString()}
                  {vaccination.expirationDate && (
                    <span className="ml-2">
                      • Expires: {new Date(vaccination.expirationDate).toLocaleDateString()}
                    </span>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
}
