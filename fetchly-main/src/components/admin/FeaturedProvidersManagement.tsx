'use client';

import { useState, useEffect } from 'react';
import { Star, Search, Eye, Plus, X, Check, TrendingUp } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface Provider {
  id: string;
  businessName: string;
  email: string;
  rating: number;
  totalBookings: number;
  services: string[];
  isFeatured: boolean;
  featuredOrder?: number;
  profileImage?: string;
  location?: string;
}

export default function FeaturedProvidersManagement() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [featuredProviders, setFeaturedProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchProviders();
  }, []);

  const fetchProviders = async () => {
    try {
      const response = await fetch('/api/admin/providers');
      if (response.ok) {
        const data = await response.json();
        const allProviders = data.providers || [];
        setProviders(allProviders);
        setFeaturedProviders(allProviders.filter((p: Provider) => p.isFeatured).sort((a: Provider, b: Provider) => (a.featuredOrder || 0) - (b.featuredOrder || 0)));
      }
    } catch (error) {
      console.error('Failed to fetch providers:', error);
      toast.error('Failed to load providers');
    } finally {
      setLoading(false);
    }
  };

  const toggleFeatured = async (providerId: string, isFeatured: boolean) => {
    try {
      const response = await fetch(`/api/admin/providers/${providerId}/featured`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          isFeatured: !isFeatured,
          featuredOrder: !isFeatured ? featuredProviders.length + 1 : null
        })
      });

      if (response.ok) {
        toast.success(!isFeatured ? 'Provider added to featured' : 'Provider removed from featured');
        fetchProviders();
      } else {
        throw new Error('Failed to update featured status');
      }
    } catch (error) {
      toast.error('Failed to update featured status');
    }
  };

  const updateFeaturedOrder = async (providerId: string, newOrder: number) => {
    try {
      const response = await fetch(`/api/admin/providers/${providerId}/featured-order`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ featuredOrder: newOrder })
      });

      if (response.ok) {
        toast.success('Featured order updated');
        fetchProviders();
      } else {
        throw new Error('Failed to update order');
      }
    } catch (error) {
      toast.error('Failed to update featured order');
    }
  };

  const filteredProviders = providers.filter(provider =>
    provider.businessName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    provider.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded w-1/4 animate-pulse"></div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Featured Providers Management</h1>
          <p className="text-gray-600">Manage homepage featured providers and their display order</p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Featured: {featuredProviders.length}</span>
        </div>
      </div>

      {/* Featured Providers Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-2 mb-4">
          <TrendingUp className="w-5 h-5 text-orange-600" />
          <h3 className="text-lg font-semibold text-gray-900">Currently Featured ({featuredProviders.length})</h3>
        </div>
        
        {featuredProviders.length === 0 ? (
          <div className="text-center py-8">
            <Star className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No featured providers yet</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {featuredProviders.map((provider, index) => (
              <div key={provider.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h4 className="font-semibold text-gray-900">{provider.businessName}</h4>
                    <p className="text-sm text-gray-500">{provider.location || 'No location'}</p>
                    <div className="flex items-center space-x-1 mt-1">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="text-sm text-gray-600">{provider.rating?.toFixed(1) || 'N/A'}</span>
                      <span className="text-sm text-gray-400">({provider.totalBookings || 0} bookings)</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded">
                      #{index + 1}
                    </span>
                    <button
                      onClick={() => toggleFeatured(provider.id, true)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    value={provider.featuredOrder || index + 1}
                    onChange={(e) => updateFeaturedOrder(provider.id, parseInt(e.target.value))}
                    className="w-16 px-2 py-1 border border-gray-300 rounded text-sm"
                    min="1"
                  />
                  <span className="text-xs text-gray-500">Display order</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* All Providers Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">All Providers</h3>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search providers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Provider
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rating
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bookings
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProviders.map((provider) => (
                <tr key={provider.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {provider.businessName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {provider.email}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-500 fill-current mr-1" />
                      <span className="text-sm text-gray-900">
                        {provider.rating?.toFixed(1) || 'N/A'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {provider.totalBookings || 0}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {provider.isFeatured ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        <Star className="w-3 h-3 mr-1" />
                        Featured #{provider.featuredOrder}
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Not Featured
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button className="text-blue-600 hover:text-blue-900">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => toggleFeatured(provider.id, provider.isFeatured)}
                        className={`${
                          provider.isFeatured 
                            ? 'text-red-600 hover:text-red-900' 
                            : 'text-green-600 hover:text-green-900'
                        }`}
                      >
                        {provider.isFeatured ? (
                          <X className="w-4 h-4" />
                        ) : (
                          <Plus className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredProviders.length === 0 && (
          <div className="text-center py-12">
            <Star className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No providers found matching your criteria</p>
          </div>
        )}
      </div>
    </div>
  );
}
