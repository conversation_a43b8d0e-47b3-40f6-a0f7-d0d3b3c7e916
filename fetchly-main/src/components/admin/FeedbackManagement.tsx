'use client';

import { useState, useEffect } from 'react';
import { MessageSquare, Search, Clock, CheckCircle, AlertTriangle, User, Send, Filter } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface Feedback {
  id: string;
  userId: string | null;
  userEmail: string;
  userName: string;
  userType: string;
  subject: string;
  message: string;
  category: string;
  priority: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  adminResponse: string | null;
  adminResponseAt: string | null;
}

export default function FeedbackManagement() {
  const [feedback, setFeedback] = useState<Feedback[]>([]);
  const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [adminResponse, setAdminResponse] = useState('');
  const [sendingResponse, setSendingResponse] = useState(false);

  useEffect(() => {
    fetchFeedback();
  }, []);

  const fetchFeedback = async () => {
    try {
      setLoading(true);
      
      // Create basic auth header for admin access
      const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>';
      const adminPassword = process.env.NEXT_PUBLIC_ADMIN_PASSWORD || 'admin123';
      const credentials = btoa(`${adminEmail}:${adminPassword}`);
      
      const response = await fetch('/api/admin/feedback', {
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setFeedback(data.feedback || []);
      } else {
        throw new Error('Failed to fetch feedback');
      }
    } catch (error) {
      console.error('Failed to fetch feedback:', error);
      toast.error('Failed to load feedback');
    } finally {
      setLoading(false);
    }
  };

  const updateFeedbackStatus = async (feedbackId: string, status: string) => {
    try {
      const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>';
      const adminPassword = process.env.NEXT_PUBLIC_ADMIN_PASSWORD || 'admin123';
      const credentials = btoa(`${adminEmail}:${adminPassword}`);
      
      const response = await fetch('/api/admin/feedback', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`
        },
        body: JSON.stringify({ feedbackId, status })
      });

      if (response.ok) {
        toast.success('Status updated successfully');
        fetchFeedback();
        if (selectedFeedback?.id === feedbackId) {
          setSelectedFeedback({ ...selectedFeedback, status });
        }
      } else {
        throw new Error('Failed to update status');
      }
    } catch (error) {
      toast.error('Failed to update status');
    }
  };

  const sendResponse = async () => {
    if (!adminResponse.trim() || !selectedFeedback) return;

    setSendingResponse(true);
    try {
      const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>';
      const adminPassword = process.env.NEXT_PUBLIC_ADMIN_PASSWORD || 'admin123';
      const credentials = btoa(`${adminEmail}:${adminPassword}`);
      
      const response = await fetch('/api/admin/feedback', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`
        },
        body: JSON.stringify({
          feedbackId: selectedFeedback.id,
          adminResponse: adminResponse,
          status: 'responded'
        })
      });

      if (response.ok) {
        setAdminResponse('');
        toast.success('Response sent successfully');
        fetchFeedback();
        // Update selected feedback
        setSelectedFeedback({
          ...selectedFeedback,
          adminResponse,
          adminResponseAt: new Date().toISOString(),
          status: 'responded'
        });
      } else {
        throw new Error('Failed to send response');
      }
    } catch (error) {
      toast.error('Failed to send response');
    } finally {
      setSendingResponse(false);
    }
  };

  const filteredFeedback = feedback.filter(item => {
    const matchesSearch = 
      item.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.message.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || item.priority === priorityFilter;
    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesPriority && matchesCategory;
  });

  const getStatusBadge = (status: string) => {
    const colors = {
      new: 'bg-blue-100 text-blue-800',
      in_progress: 'bg-yellow-100 text-yellow-800',
      responded: 'bg-green-100 text-green-800',
      resolved: 'bg-gray-100 text-gray-800'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[status as keyof typeof colors] || colors.new}`}>
        {status.replace('_', ' ').toUpperCase()}
      </span>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const colors = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[priority as keyof typeof colors] || colors.medium}`}>
        {priority.toUpperCase()}
      </span>
    );
  };

  const getCategoryBadge = (category: string) => {
    const colors = {
      general: 'bg-gray-100 text-gray-800',
      bug: 'bg-red-100 text-red-800',
      feature: 'bg-purple-100 text-purple-800',
      improvement: 'bg-green-100 text-green-800',
      support: 'bg-blue-100 text-blue-800'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[category as keyof typeof colors] || colors.general}`}>
        {category.toUpperCase()}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded w-1/4 animate-pulse"></div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  const newFeedback = feedback.filter(f => f.status === 'new');
  const inProgressFeedback = feedback.filter(f => f.status === 'in_progress');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Feedback Management</h1>
          <p className="text-gray-600">Manage user feedback and support requests</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            New: {newFeedback.length} | In Progress: {inProgressFeedback.length}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Feedback List */}
        <div className="lg:col-span-2 space-y-4">
          {/* Filters */}
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search feedback..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Statuses</option>
                <option value="new">New</option>
                <option value="in_progress">In Progress</option>
                <option value="responded">Responded</option>
                <option value="resolved">Resolved</option>
              </select>

              <select
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Priorities</option>
                <option value="urgent">Urgent</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>

              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Categories</option>
                <option value="general">General</option>
                <option value="bug">Bug Report</option>
                <option value="feature">Feature Request</option>
                <option value="improvement">Improvement</option>
                <option value="support">Support</option>
              </select>
            </div>
          </div>

          {/* Feedback Items */}
          <div className="space-y-3">
            {filteredFeedback.map((item) => (
              <div
                key={item.id}
                onClick={() => setSelectedFeedback(item)}
                className={`bg-white border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedFeedback?.id === item.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h4 className="font-semibold text-gray-900">{item.subject}</h4>
                    <p className="text-sm text-gray-600">{item.userName} ({item.userType})</p>
                    <p className="text-sm text-gray-500">{item.userEmail}</p>
                  </div>
                  <div className="flex flex-col items-end space-y-1">
                    {getPriorityBadge(item.priority)}
                    {getCategoryBadge(item.category)}
                    {getStatusBadge(item.status)}
                  </div>
                </div>
                <p className="text-sm text-gray-500 mb-2 line-clamp-2">{item.message}</p>
                <div className="flex items-center justify-between text-xs text-gray-400">
                  <span>{new Date(item.createdAt).toLocaleDateString()}</span>
                  {item.adminResponse && (
                    <span className="text-green-600">✓ Responded</span>
                  )}
                </div>
              </div>
            ))}
          </div>

          {filteredFeedback.length === 0 && (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No feedback found</p>
            </div>
          )}
        </div>

        {/* Feedback Details */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {selectedFeedback ? (
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{selectedFeedback.subject}</h3>
                  <p className="text-sm text-gray-600">{selectedFeedback.userName}</p>
                  <p className="text-sm text-gray-500">{selectedFeedback.userEmail}</p>
                </div>
                <div className="flex flex-col space-y-2">
                  {getPriorityBadge(selectedFeedback.priority)}
                  {getCategoryBadge(selectedFeedback.category)}
                  {getStatusBadge(selectedFeedback.status)}
                </div>
              </div>

              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2">Message:</h4>
                <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded">{selectedFeedback.message}</p>
              </div>

              {/* Status Actions */}
              <div className="mb-4 flex flex-wrap gap-2">
                <button
                  onClick={() => updateFeedbackStatus(selectedFeedback.id, 'in_progress')}
                  className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
                >
                  In Progress
                </button>
                <button
                  onClick={() => updateFeedbackStatus(selectedFeedback.id, 'resolved')}
                  className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                >
                  Resolve
                </button>
              </div>

              {/* Admin Response */}
              {selectedFeedback.adminResponse && (
                <div className="mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">Admin Response:</h4>
                  <p className="text-sm text-gray-700 bg-green-50 p-3 rounded border border-green-200">
                    {selectedFeedback.adminResponse}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Responded on {new Date(selectedFeedback.adminResponseAt!).toLocaleString()}
                  </p>
                </div>
              )}

              {/* Send Response */}
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Send Response:</h4>
                <textarea
                  value={adminResponse}
                  onChange={(e) => setAdminResponse(e.target.value)}
                  placeholder="Type your response to the user..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={4}
                />
                <button
                  onClick={sendResponse}
                  disabled={sendingResponse || !adminResponse.trim()}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                >
                  <Send className="w-4 h-4" />
                  <span>{sendingResponse ? 'Sending...' : 'Send Response'}</span>
                </button>
              </div>
            </div>
          ) : (
            <div className="p-6 text-center">
              <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Select feedback to view details</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
