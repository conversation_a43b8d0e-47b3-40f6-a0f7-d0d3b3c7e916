'use client';

import { useState } from 'react';
import { AlertTriangle, CheckCircle, RefreshCw, Database, Zap, Eye } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface AnalysisResult {
  summary: {
    totalProviderUsers: number;
    totalProviderDocs: number;
    totalBookings: number;
    uniqueProviderIdsInBookings: number;
    totalInconsistencies: number;
    needsFix: boolean;
  };
  analysis: any;
  recommendations: any[];
}

export default function ProviderIdManagement() {
  const [analysis, setAnalysis] = useState<AnalysisResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [fixing, setFixing] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const runAnalysis = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/analyze-provider-ids');
      if (response.ok) {
        const data = await response.json();
        setAnalysis(data);
        toast.success('Analysis completed');
      } else {
        throw new Error('Failed to run analysis');
      }
    } catch (error) {
      toast.error('Failed to analyze provider IDs');
      console.error('Analysis error:', error);
    } finally {
      setLoading(false);
    }
  };

  const fixProviderIds = async () => {
    if (!analysis?.summary.needsFix) {
      toast.info('No fixes needed');
      return;
    }

    setFixing(true);
    try {
      const response = await fetch('/api/admin/fix-provider-ids', {
        method: 'POST'
      });
      
      if (response.ok) {
        const data = await response.json();
        toast.success(`Fixed ${data.summary.bookingsUpdated} bookings and ${data.summary.providersUpdated} providers`);
        // Re-run analysis to see updated state
        await runAnalysis();
      } else {
        throw new Error('Failed to fix provider IDs');
      }
    } catch (error) {
      toast.error('Failed to fix provider IDs');
      console.error('Fix error:', error);
    } finally {
      setFixing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Provider ID Management</h1>
        <p className="text-gray-600">Analyze and fix provider ID inconsistencies across the database</p>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center space-x-4">
        <button
          onClick={runAnalysis}
          disabled={loading}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? (
            <RefreshCw className="w-4 h-4 animate-spin" />
          ) : (
            <Database className="w-4 h-4" />
          )}
          <span>Run Analysis</span>
        </button>

        {analysis?.summary.needsFix && (
          <button
            onClick={fixProviderIds}
            disabled={fixing}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            {fixing ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Zap className="w-4 h-4" />
            )}
            <span>Fix All Issues</span>
          </button>
        )}

        {analysis && (
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            <Eye className="w-4 h-4" />
            <span>{showDetails ? 'Hide' : 'Show'} Details</span>
          </button>
        )}
      </div>

      {/* Analysis Results */}
      {analysis && (
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <Database className="w-5 h-5 text-blue-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-blue-800">Provider Users</p>
                  <p className="text-2xl font-bold text-blue-900">{analysis.summary.totalProviderUsers}</p>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <Database className="w-5 h-5 text-green-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-green-800">Provider Docs</p>
                  <p className="text-2xl font-bold text-green-900">{analysis.summary.totalProviderDocs}</p>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center">
                <Database className="w-5 h-5 text-purple-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-purple-800">Total Bookings</p>
                  <p className="text-2xl font-bold text-purple-900">{analysis.summary.totalBookings}</p>
                </div>
              </div>
            </div>

            <div className={`border rounded-lg p-4 ${
              analysis.summary.totalInconsistencies > 0 
                ? 'bg-red-50 border-red-200' 
                : 'bg-green-50 border-green-200'
            }`}>
              <div className="flex items-center">
                {analysis.summary.totalInconsistencies > 0 ? (
                  <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
                ) : (
                  <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                )}
                <div>
                  <p className={`text-sm font-medium ${
                    analysis.summary.totalInconsistencies > 0 ? 'text-red-800' : 'text-green-800'
                  }`}>
                    Issues Found
                  </p>
                  <p className={`text-2xl font-bold ${
                    analysis.summary.totalInconsistencies > 0 ? 'text-red-900' : 'text-green-900'
                  }`}>
                    {analysis.summary.totalInconsistencies}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Status */}
          <div className={`p-4 rounded-lg border ${
            analysis.summary.needsFix 
              ? 'bg-red-50 border-red-200' 
              : 'bg-green-50 border-green-200'
          }`}>
            <div className="flex items-center space-x-2">
              {analysis.summary.needsFix ? (
                <AlertTriangle className="w-5 h-5 text-red-600" />
              ) : (
                <CheckCircle className="w-5 h-5 text-green-600" />
              )}
              <h3 className={`font-semibold ${
                analysis.summary.needsFix ? 'text-red-900' : 'text-green-900'
              }`}>
                {analysis.summary.needsFix 
                  ? 'Issues Found - Action Required' 
                  : 'All Provider IDs Are Consistent'
                }
              </h3>
            </div>
            {analysis.summary.needsFix && (
              <p className="text-red-700 mt-2">
                Found {analysis.summary.totalInconsistencies} inconsistencies that need to be fixed.
              </p>
            )}
          </div>

          {/* Recommendations */}
          {analysis.recommendations.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recommendations</h3>
              <div className="space-y-3">
                {analysis.recommendations.map((rec, index) => (
                  <div key={index} className={`p-3 rounded-md border ${
                    rec.severity === 'high' 
                      ? 'bg-red-50 border-red-200' 
                      : 'bg-yellow-50 border-yellow-200'
                  }`}>
                    <div className="flex items-start space-x-2">
                      <AlertTriangle className={`w-4 h-4 mt-0.5 ${
                        rec.severity === 'high' ? 'text-red-600' : 'text-yellow-600'
                      }`} />
                      <div>
                        <p className={`font-medium ${
                          rec.severity === 'high' ? 'text-red-900' : 'text-yellow-900'
                        }`}>
                          {rec.message}
                        </p>
                        <p className={`text-sm ${
                          rec.severity === 'high' ? 'text-red-700' : 'text-yellow-700'
                        }`}>
                          Action: {rec.action}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Detailed Analysis */}
          {showDetails && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Detailed Analysis</h3>
              <div className="space-y-4">
                
                {/* Inconsistencies */}
                {analysis.analysis.inconsistencies.orphanedBookings.length > 0 && (
                  <div>
                    <h4 className="font-medium text-red-900 mb-2">Orphaned Bookings</h4>
                    <div className="bg-red-50 border border-red-200 rounded p-3">
                      <p className="text-sm text-red-700 mb-2">
                        Bookings with provider IDs that don't match any user:
                      </p>
                      <div className="space-y-1">
                        {analysis.analysis.inconsistencies.orphanedBookings.map((booking: any) => (
                          <div key={booking.bookingId} className="text-xs text-red-600">
                            Booking {booking.bookingId}: Provider ID {booking.providerId} (Customer: {booking.customer})
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {analysis.analysis.inconsistencies.mismatchedProviders.length > 0 && (
                  <div>
                    <h4 className="font-medium text-red-900 mb-2">Mismatched Provider Documents</h4>
                    <div className="bg-red-50 border border-red-200 rounded p-3">
                      <p className="text-sm text-red-700 mb-2">
                        Provider documents with incorrect user IDs:
                      </p>
                      <div className="space-y-1">
                        {analysis.analysis.inconsistencies.mismatchedProviders.map((provider: any) => (
                          <div key={provider.documentId} className="text-xs text-red-600">
                            {provider.businessName} ({provider.email}): {provider.currentUserId} → {provider.correctUserId}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Booking Distribution */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Bookings by Provider ID</h4>
                  <div className="bg-gray-50 border border-gray-200 rounded p-3">
                    <div className="space-y-1">
                      {Object.entries(analysis.analysis.bookings.bookingsByProvider).map(([providerId, bookings]: [string, any]) => (
                        <div key={providerId} className="text-xs text-gray-600">
                          Provider {providerId}: {bookings.length} bookings
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">How to Use</h3>
        <div className="text-sm text-blue-700 space-y-1">
          <p>1. Click "Run Analysis" to check for provider ID inconsistencies</p>
          <p>2. Review the results and recommendations</p>
          <p>3. Click "Fix All Issues" to automatically correct any problems</p>
          <p>4. Re-run analysis to verify all issues are resolved</p>
        </div>
      </div>
    </div>
  );
}
