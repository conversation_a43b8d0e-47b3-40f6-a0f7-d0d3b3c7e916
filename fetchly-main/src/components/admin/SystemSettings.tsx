'use client';

import { Settings, Shield, Database, Mail } from 'lucide-react';

export default function SystemSettings() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
        <p className="text-gray-600">Configure platform settings and system preferences</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center space-x-3 mb-4">
            <Shield className="w-6 h-6 text-red-600" />
            <h3 className="text-lg font-semibold text-gray-900">Security Settings</h3>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Two-Factor Authentication</span>
              <button className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">Enabled</button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Session Timeout</span>
              <span className="text-sm text-gray-900">24 hours</span>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center space-x-3 mb-4">
            <Database className="w-6 h-6 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Database Settings</h3>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Backup Frequency</span>
              <span className="text-sm text-gray-900">Daily</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Data Retention</span>
              <span className="text-sm text-gray-900">2 years</span>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center space-x-3 mb-4">
            <Mail className="w-6 h-6 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">Email Settings</h3>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">SMTP Server</span>
              <span className="text-sm text-gray-900">Configured</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Email Templates</span>
              <span className="text-sm text-gray-900">Active</span>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center space-x-3 mb-4">
            <Settings className="w-6 h-6 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">Platform Settings</h3>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Maintenance Mode</span>
              <button className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">Disabled</button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">API Rate Limiting</span>
              <span className="text-sm text-gray-900">1000/hour</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
