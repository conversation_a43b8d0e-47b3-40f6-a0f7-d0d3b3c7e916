'use client';

import { useAdmin } from '@/contexts/AdminContext';
import {
  LayoutDashboard,
  Calendar,
  Users,
  UserCheck,
  CreditCard,
  BarChart3,
  Settings,
  Shield,
  X,
  Star,
  MessageSquare,
  HelpCircle,
  Database,
  MessageCircle
} from 'lucide-react';

interface AdminSidebarProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

const menuItems = [
  { id: 'overview', label: 'Dashboard Overview', icon: LayoutDashboard, permission: 'view_analytics' },
  { id: 'bookings', label: 'Bookings Management', icon: Calendar, permission: 'view_bookings' },
  { id: 'providers', label: 'Providers Management', icon: UserCheck, permission: 'view_providers' },
  { id: 'users', label: 'Users Management', icon: Users, permission: 'view_users' },
  { id: 'featured', label: 'Featured Providers', icon: Star, permission: 'manage_featured' },
  { id: 'reviews', label: 'Reviews Management', icon: MessageSquare, permission: 'manage_reviews' },
  { id: 'support', label: 'Support Tickets', icon: HelpCircle, permission: 'manage_support_tickets' },
  { id: 'feedback', label: 'Feedback Management', icon: MessageCircle, permission: 'manage_feedback' },
  { id: 'provider-ids', label: 'Fix Provider IDs', icon: Database, permission: 'system_admin' },
  { id: 'payments', label: 'Payments & Revenue', icon: CreditCard, permission: 'view_payments' },
  { id: 'analytics', label: 'Analytics Dashboard', icon: BarChart3, permission: 'view_analytics' },
  { id: 'settings', label: 'System Settings', icon: Settings, permission: 'manage_settings' },
];

export default function AdminSidebar({ 
  activeTab, 
  setActiveTab, 
  sidebarOpen, 
  setSidebarOpen 
}: AdminSidebarProps) {
  const { admin, hasPermission } = useAdmin();

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    setSidebarOpen(false); // Close mobile sidebar
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex flex-col w-64">
          <div className="flex flex-col flex-grow bg-gray-900 pt-5 pb-4 overflow-y-auto">
            {/* Logo */}
            <div className="flex items-center flex-shrink-0 px-4 mb-8">
              <div className="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center mr-3">
                <Shield className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-white text-lg font-bold">FetchlyPR</h1>
                <p className="text-gray-400 text-xs">Admin Dashboard</p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="mt-5 flex-1 px-2 space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = activeTab === item.id;
                const hasAccess = hasPermission(item.permission);

                if (!hasAccess) return null;

                return (
                  <button
                    key={item.id}
                    onClick={() => handleTabChange(item.id)}
                    className={`${
                      isActive
                        ? 'bg-red-600 text-white'
                        : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    } group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full text-left transition-colors`}
                  >
                    <Icon className="mr-3 h-5 w-5 flex-shrink-0" />
                    {item.label}
                  </button>
                );
              })}
            </nav>

            {/* Admin Info */}
            <div className="flex-shrink-0 px-4 py-4 border-t border-gray-700">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {admin?.email.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-white truncate">
                    {admin?.email}
                  </p>
                  <p className="text-xs text-gray-400 capitalize">
                    {admin?.role}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <div className={`lg:hidden fixed inset-y-0 left-0 z-50 w-64 bg-gray-900 transform transition-transform duration-300 ease-in-out ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center mr-3">
                <Shield className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-white text-lg font-bold">FetchlyPR</h1>
                <p className="text-gray-400 text-xs">Admin Dashboard</p>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeTab === item.id;
              const hasAccess = hasPermission(item.permission);

              if (!hasAccess) return null;

              return (
                <button
                  key={item.id}
                  onClick={() => handleTabChange(item.id)}
                  className={`${
                    isActive
                      ? 'bg-red-600 text-white'
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  } group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full text-left transition-colors`}
                >
                  <Icon className="mr-3 h-5 w-5 flex-shrink-0" />
                  {item.label}
                </button>
              );
            })}
          </nav>

          {/* Admin Info */}
          <div className="px-4 py-4 border-t border-gray-700">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {admin?.email.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-white truncate">
                  {admin?.email}
                </p>
                <p className="text-xs text-gray-400 capitalize">
                  {admin?.role}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
