'use client';

import { useState, useRef, useEffect } from 'react';
import { Bell, X, Clock, CheckCircle, AlertCircle, MessageCircle, CreditCard, Star } from 'lucide-react';
import { useNotifications } from '@/contexts/NotificationContext';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useChatModal } from '@/contexts/ChatModalContext';
import { formatDistanceToNow } from 'date-fns';

export function NotificationDropdown() {
  const [isOpen, setIsOpen] = useState(false);
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications();
  const { user } = useAuth();
  const router = useRouter();
  const { openChat } = useChatModal();
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      // Chat & Messaging
      case 'new_message':
      case 'new_chat':
      case 'support_message':
        return <MessageCircle className="w-4 h-4 text-blue-500" />;

      // Bookings
      case 'booking_request':
      case 'booking_confirmed':
      case 'booking_cancelled':
      case 'booking_completed':
      case 'booking':
        return <Clock className="w-4 h-4 text-blue-500" />;

      // Payments
      case 'payment_received':
      case 'payout_processed':
      case 'subscription_renewed':
      case 'subscription_cancelled':
      case 'payment':
        return <CreditCard className="w-4 h-4 text-green-500" />;

      // Social & Community
      case 'post_liked':
      case 'post_commented':
      case 'post_shared':
      case 'new_follower':
      case 'friend_request':
      case 'community':
        return <MessageCircle className="w-4 h-4 text-purple-500" />;

      // Reviews
      case 'new_review':
      case 'review_response':
      case 'rating_received':
      case 'review':
        return <Star className="w-4 h-4 text-yellow-500" />;

      // System
      case 'account_verified':
      case 'profile_approved':
      case 'system_update':
      case 'maintenance_notice':
      case 'system':
        return <CheckCircle className="w-4 h-4 text-green-500" />;

      // Emergency
      case 'emergency_alert':
      case 'service_reminder':
      case 'appointment_reminder':
      case 'emergency':
        return <AlertCircle className="w-4 h-4 text-red-500" />;

      default:
        return <Bell className="w-4 h-4 text-gray-500" />;
    }
  };

  const handleNotificationClick = async (notification: any) => {
    // Mark as read
    if (!notification.isRead && !notification.read) {
      await markAsRead(notification.id);
    }

    // Handle booking request notifications - navigate to provider dashboard booking requests
    if (notification.type === 'booking_request' && user?.role === 'provider') {
      router.push('/provider/dashboard?tab=booking-requests');
    }
    // Navigate to action URL (check both new and old schema)
    else if (notification.data?.actionUrl) {
      router.push(notification.data.actionUrl);
    } else if (notification.actionUrl) {
      router.push(notification.actionUrl);
    }

    setIsOpen(false);
  };

  const handleSeeMore = () => {
    const dashboardUrl = user?.role === 'provider' ? '/provider/dashboard' : '/dashboard';
    router.push(`${dashboardUrl}?tab=notifications`);
    setIsOpen(false);
  };

  const handleBellClick = () => {
    // Toggle notifications dropdown
    setIsOpen(!isOpen);
  };

  const recentNotifications = notifications.slice(0, 4);

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Notification Bell */}
      <button
        onClick={handleBellClick}
        className="relative p-2 text-text-secondary hover:text-primary-500 transition-all duration-300 hover:scale-110"
        title="View notifications"
      >
        <Bell className="w-5 h-5" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold rounded-full flex items-center justify-center animate-pulse">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notification Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="p-4 border-b border-white/20 bg-gradient-to-r from-green-600 to-blue-600">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">Notifications</h3>
              <div className="flex items-center space-x-2">
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="text-xs text-green-100 hover:text-white transition-colors"
                  >
                    Mark all read
                  </button>
                )}
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-white/20 rounded-full transition-colors"
                >
                  <X className="w-4 h-4 text-white" />
                </button>
              </div>
            </div>
          </div>

          {/* Notifications List */}
          <div className="max-h-64 overflow-y-auto">
            {recentNotifications.length > 0 ? (
              recentNotifications.map((notification) => (
                <div
                  key={notification.id}
                  onClick={() => handleNotificationClick(notification)}
                  className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50 transition-all duration-300 ${
                    (!notification.isRead && !notification.read) ? 'bg-blue-50/50' : ''
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {notification.title}
                        </p>
                        {(!notification.isRead && !notification.read) && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 ml-2"></div>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-400 mt-2">
                        {formatDistanceToNow(notification.createdAt.toDate(), { addSuffix: true })}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-8 text-center">
                <Bell className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No notifications yet</p>
                <p className="text-sm text-gray-400 mt-1">
                  We'll notify you when something important happens
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          {recentNotifications.length > 0 && (
            <div className="p-4 border-t border-white/20 bg-gray-50/50">
              <button
                onClick={handleSeeMore}
                className="w-full text-center text-sm font-medium text-green-600 hover:text-blue-600 transition-colors duration-300"
              >
                See all notifications
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
