'use client';

import { useEffect, useRef, useState } from 'react';
import { StoryMediaProps, StoryMediaDisplayMode } from '@/types/stories';
import { Play, Pause, Volume2, VolumeX } from 'lucide-react';

export default function StoryMedia({ 
  story, 
  isActive, 
  onLoadComplete,
  onError,
  displayMode = 'fill',
  className = ''
}: StoryMediaProps & { displayMode?: StoryMediaDisplayMode; className?: string }) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  useEffect(() => {
    if (story.type === 'video' && videoRef.current) {
      const video = videoRef.current;
      
      if (isActive) {
        video.currentTime = 0;
        video.play().then(() => {
          setIsVideoPlaying(true);
        }).catch((error) => {
          console.error('Error playing video:', error);
          onError?.(error);
        });
      } else {
        video.pause();
        setIsVideoPlaying(false);
      }
    }
  }, [isActive, story.type, onError]);

  const handleImageLoad = () => {
    setIsLoading(false);
    onLoadComplete?.();
  };

  const handleImageError = (error: any) => {
    setHasError(true);
    setIsLoading(false);
    onError?.(new Error('Failed to load image'));
  };

  const handleVideoLoad = () => {
    setIsLoading(false);
    onLoadComplete?.();
  };

  const handleVideoError = (error: any) => {
    setHasError(true);
    setIsLoading(false);
    onError?.(new Error('Failed to load video'));
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted;
      setIsMuted(videoRef.current.muted);
    }
  };

  const togglePlayPause = () => {
    if (videoRef.current) {
      if (isVideoPlaying) {
        videoRef.current.pause();
        setIsVideoPlaying(false);
      } else {
        videoRef.current.play();
        setIsVideoPlaying(true);
      }
    }
  };

  if (hasError) {
    const mediaContainerClass = `relative w-full h-full overflow-hidden ${
      displayMode === 'center' ? 'flex items-center justify-center' : ''
    } ${className}`;

    const mediaClass = `w-full h-full ${
      displayMode === 'center' ? 'object-contain' : 'object-cover'
    }`;

    return (
      <div className={mediaContainerClass}>
        <div className="text-center text-white">
          <div className="text-4xl mb-4">⚠️</div>
          <p className="text-lg">Failed to load media</p>
          <p className="text-sm text-gray-400 mt-2">Please try again later</p>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute inset-0 bg-black">
      {/* Loading indicator */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        </div>
      )}

      {/* Media content */}
      {story.type === 'image' ? (
        <div className={`relative w-full h-full overflow-hidden ${
          displayMode === 'center' ? 'flex items-center justify-center' : ''
        } ${className}`}>
          <img
            src={story.mediaUrl}
            alt="Story content"
            className={`w-full h-full ${
              displayMode === 'center' ? 'object-contain' : 'object-cover'
            }`}
            onLoad={handleImageLoad}
            onError={handleImageError}
            style={{ display: isLoading ? 'none' : 'block' }}
          />
        </div>
      ) : (
        <div className="relative w-full h-full">
          <video
            ref={videoRef}
            src={story.mediaUrl}
            className={`w-full h-full ${
              displayMode === 'center' ? 'object-contain' : 'object-cover'
            } ${isLoading ? 'opacity-0' : 'opacity-100'}`}
            muted={isMuted}
            playsInline
            preload="metadata"
            onLoadedData={handleVideoLoad}
            onError={handleVideoError}
            onPlay={() => setIsVideoPlaying(true)}
            onPause={() => setIsVideoPlaying(false)}
            style={{ display: isLoading ? 'none' : 'block' }}
          />
          
          {/* Video controls */}
          {!isLoading && (
            <div className="absolute bottom-4 right-4 flex space-x-2">
              <button
                onClick={togglePlayPause}
                className="bg-black/50 text-white p-2 rounded-full backdrop-blur-sm hover:bg-black/70 transition-colors"
              >
                {isVideoPlaying ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
              </button>
              
              <button
                onClick={toggleMute}
                className="bg-black/50 text-white p-2 rounded-full backdrop-blur-sm hover:bg-black/70 transition-colors"
              >
                {isMuted ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </button>
            </div>
          )}
        </div>
      )}

      {/* Text content overlay */}
      {story.content && !isLoading && (
        <div className="absolute bottom-20 left-4 right-4">
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-4">
            <p className="text-white text-lg leading-relaxed">
              {story.content}
            </p>
          </div>
        </div>
      )}

      {/* Gradient overlays for better text readability */}
      <div className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-black/50 to-transparent pointer-events-none" />
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/50 to-transparent pointer-events-none" />
    </div>
  );
}
