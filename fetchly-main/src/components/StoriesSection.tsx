'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Play, Pause, X, Upload, Plus, Camera } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

import { toast } from 'react-hot-toast';
import NoSSR from './NoSSR';
import { UserStories, Story } from '@/types/stories';
import { StoriesService } from '@/lib/services/stories-service';
import StoryViewer from './stories/StoryViewer';

// Define the User type that matches the AuthContext
type User = {
  id: string;
  email: string;
  name: string;
  role: 'pet_owner' | 'provider' | 'admin';
  avatar?: string;
  banner?: string;
  city?: string;
  phone?: string;
  location?: string;
  verified?: boolean;
  joinedDate?: string;
  fetchlyBalance?: number;
  rewardPoints?: number;
};

// Using Story and UserStories interfaces from @/types/stories

interface StoriesSectionProps {
  onCreateStory?: () => void;
}

export default function StoriesSection({ onCreateStory }: StoriesSectionProps) {
  const { user } = useAuth() as { user: User | null };
  
  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // User stories state
  const [userStories, setUserStories] = useState<UserStories[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  // Modal state
  const [showModal, setShowModal] = useState<boolean>(false);
  const [selectedUserStories, setSelectedUserStories] = useState<UserStories | null>(null);
  const [currentUserIndex, setCurrentUserIndex] = useState<number>(0);
  const [currentStoryIndex, setCurrentStoryIndex] = useState<number>(0);
  const [progress, setProgress] = useState<number>(0);
  const [isPaused, setIsPaused] = useState<boolean>(false);

  // Story creation states
  const [showCreateStory, setShowCreateStory] = useState<boolean>(false);
  const [storyContent, setStoryContent] = useState<string>('');
  const [storyImage, setStoryImage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Timer states
  const [autoProgressEnabled] = useState(true);
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);
  const [progressTimer, setProgressTimer] = useState<NodeJS.Timeout | null>(null);


  // New story viewer states
  const [showStoryViewer, setShowStoryViewer] = useState<boolean>(false);
  const [storyViewerData, setStoryViewerData] = useState<UserStories[]>([]);
  const [initialUserIndex, setInitialUserIndex] = useState<number>(0);
  const [initialStoryIndex, setInitialStoryIndex] = useState<number>(0);
  const [isLoadingStories, setIsLoadingStories] = useState<boolean>(true);

  // Helper function to get time remaining
  const getTimeRemaining = (expiresAt: Date) => {
    const now = new Date();
    const timeLeft = expiresAt.getTime() - now.getTime();
    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    return { hours: Math.max(0, hours), minutes: Math.max(0, minutes) };
  };

  // Navigation functions
  const goToNextStory = useCallback(() => {
    if (!selectedUserStories) return;

    if (currentStoryIndex < selectedUserStories.stories.length - 1) {
      setCurrentStoryIndex(prev => prev + 1);
      setProgress(0);
    } else if (currentUserIndex < userStories.length - 1) {
      // Move to next user
      const nextUserIndex = currentUserIndex + 1;
      setCurrentUserIndex(nextUserIndex);
      setSelectedUserStories(userStories[nextUserIndex]);
      setCurrentStoryIndex(0);
      setProgress(0);
    } else {
      // All stories finished
      closeModal();
    }
  }, [selectedUserStories, currentStoryIndex, currentUserIndex, userStories]);

  const goToPreviousStory = useCallback(() => {
    if (!selectedUserStories) return;

    if (currentStoryIndex > 0) {
      setCurrentStoryIndex(prev => prev - 1);
      setProgress(0);
    } else if (currentUserIndex > 0) {
      // Move to previous user's last story
      const prevUserIndex = currentUserIndex - 1;
      const prevUserStories = userStories[prevUserIndex];
      setCurrentUserIndex(prevUserIndex);
      setSelectedUserStories(prevUserStories);
      setCurrentStoryIndex(prevUserStories.stories.length - 1);
      setProgress(0);
    }
  }, [selectedUserStories, currentStoryIndex, currentUserIndex, userStories]);

  const togglePause = useCallback(() => {
    setIsPaused(prev => !prev);
  }, []);

  // Close modal and cleanup
  const closeModal = useCallback(() => {
    setShowModal(false);
    setSelectedUserStories(null);
    setCurrentStoryIndex(0);
    setCurrentUserIndex(0);
    setProgress(0);
    setIsPaused(false);
    clearTimers();
  }, []);

  // Story timer functions
  const startStoryTimer = useCallback(() => {
    if (!autoProgressEnabled || isPaused) return;
    
    const duration = 5000; // 5 seconds per story
    const interval = 50;
    const increment = (interval / duration) * 100;
    
    const timer = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + increment;
        if (newProgress >= 100) {
          goToNextStory();
          return 0;
        }
        return newProgress;
      });
    }, interval);
    
    setProgressTimer(timer);
  }, [autoProgressEnabled, isPaused, goToNextStory]);

  const clearTimers = useCallback(() => {
    if (progressTimer) {
      clearInterval(progressTimer);
      setProgressTimer(null);
    }
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  }, [progressTimer, longPressTimer]);



  // Handle story click - use new story viewer
  const handleStoryClick = async (userStory: UserStories) => {
    try {
      // Fetch all active stories for the new viewer
      const activeStories = await StoriesService.getActiveStories();
      const userIndex = activeStories.findIndex(us => us.userId === userStory.userId);

      setStoryViewerData(activeStories);
      setInitialUserIndex(userIndex >= 0 ? userIndex : 0);
      setInitialStoryIndex(0);
      setShowStoryViewer(true);
    } catch (error) {
      console.error('Error loading stories:', error);
      toast.error('Failed to load stories');
    }
  };

  // Handle story view tracking
  const handleStoryView = async (storyId: string, userId: string) => {
    if (!user) return;

    try {
      await fetch(`/api/stories/${storyId}/view`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ viewerId: user.id })
      });
    } catch (error) {
      console.error('Error tracking story view:', error);
    }
  };

  // Load stories from database
  const loadStories = useCallback(async () => {
    try {
      setIsLoadingStories(true);
      const activeStories = await StoriesService.getActiveStories();
      setUserStories(activeStories);
    } catch (error) {
      console.error('Error loading stories:', error);
      toast.error('Failed to load stories');
    } finally {
      setIsLoadingStories(false);
    }
  }, []);

  // Load stories on component mount and after creating a story
  useEffect(() => {
    loadStories();
  }, [loadStories]);

  // Handle image upload
  const handleImageUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      setStoryImage(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  }, []);

  // Handle story creation
  const handleCreateStory = useCallback(async () => {
    if (!user) {
      toast.error('Please sign in to create a story');
      return;
    }

    if (!storyContent.trim() && !storyImage) {
      toast.error('Please add some content to your story');
      return;
    }

    // Validate user ID - AuthContext uses 'id' property
    const userId = user.id;
    if (!userId) {
      console.error('User ID is undefined:', user);
      toast.error('Unable to create story. Please try signing in again.');
      return;
    }

    setIsSubmitting(true);
    try {
      // Use the new StoriesService
      await StoriesService.createStory({
        userId: userId,
        userName: user.name || 'Anonymous',
        userAvatar: user.avatar || '/favicon.png',
        mediaUrl: storyImage || '', // For now, we'll use image as mediaUrl
        type: 'image', // For now, only supporting images
        content: storyContent || '',
        isPublic: true
      });
      
      setShowCreateStory(false);
      setStoryContent('');
      setStoryImage(null);
      toast.success('Story posted successfully!');
      
      if (onCreateStory) {
        onCreateStory();
      }
    } catch (error) {
      console.error('Error creating story:', error);
      toast.error('Failed to post story');
    } finally {
      setIsSubmitting(false);
    }
  }, [user, storyContent, storyImage, onCreateStory]);

  // Auto-advance effect
  useEffect(() => {
    if (showModal && selectedUserStories && !isPaused) {
      startStoryTimer();
    } else {
      clearTimers();
    }

    return () => clearTimers();
  }, [showModal, selectedUserStories, isPaused, startStoryTimer, clearTimers]);

  // Video pause/resume effect
  useEffect(() => {
    if (videoRef.current) {
      if (isPaused) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
    }
  }, [isPaused]);

  // Load stories from Firebase
  useEffect(() => {
    const loadStories = async () => {
      try {
        setLoading(true);
        console.log('🔍 Loading stories...');
        const stories = await StoriesService.getActiveStories();
        console.log('📱 Stories loaded:', stories.length);
        setUserStories(stories);
      } catch (error) {
        console.error('❌ Error loading stories:', error);
        setUserStories([]);
      } finally {
        setLoading(false);
      }
    };

    loadStories();
  }, []);

  if (loading) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-20 h-20 bg-gray-200 rounded-2xl animate-pulse"></div>
          <div className="w-20 h-20 bg-gray-200 rounded-2xl animate-pulse"></div>
          <div className="w-20 h-20 bg-gray-200 rounded-2xl animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <>
    {/* Stories Section - No Container Background */}
    <div className="mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4 px-2">
        <h3 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
          Stories
        </h3>
      </div>

      {/* Stories Container - Horizontal Scroll */}
      <div className="flex items-center space-x-4 overflow-x-auto pb-2 scrollbar-hide px-2">
        {/* Add Story Button */}
        {user && (
          <div className="flex-shrink-0 text-center">
            <button
              onClick={() => setShowCreateStory(true)}
              className="relative w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-green-500 border-3 border-white shadow-lg hover:scale-105 transition-all duration-300 flex items-center justify-center group"
            >
              <Plus className="w-8 h-8 text-white" />
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-blue-500 rounded-full border-2 border-white flex items-center justify-center">
                <Plus className="w-3 h-3 text-white" />
              </div>
            </button>
            <p className="text-xs text-gray-700 mt-2 font-semibold">Your Story</p>
          </div>
        )}

        {/* Stories */}
        {userStories.map((userStory) => (
          <div key={userStory.userId} className="flex-shrink-0 text-center">
            <button
              className="relative group"
              onClick={() => handleStoryClick(userStory)}
            >
              {/* Story Container - Circular like Instagram */}
              <div className={`w-16 h-16 rounded-full p-0.5 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 ${
                userStory.hasUnviewed
                  ? 'bg-gradient-to-tr from-pink-500 via-red-500 to-yellow-500'
                  : 'bg-gray-300'
              }`}>
                <div className="w-full h-full rounded-full bg-white p-0.5">
                  <img
                    src={userStory.latestStory.mediaUrl || userStory.latestStory.userAvatar || '/favicon.png'}
                    alt={userStory.latestStory.userName}
                    className="w-full h-full rounded-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = '/fetchlylogo.png';
                    }}
                  />
                </div>
              </div>

              {/* Multiple Stories Indicator */}
              {userStory.stories.length > 1 && (
                <div className="absolute -top-2 -right-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold shadow-lg border-2 border-white">
                  {userStory.stories.length}
                </div>
              )}

              {/* Time Remaining */}
              <div className="absolute -bottom-2 -right-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-xs px-2 py-1 rounded-full shadow-lg border-2 border-white font-medium">
                <NoSSR fallback="24h">
                  {getTimeRemaining(userStory.latestStory.expiresAt).hours}h {getTimeRemaining(userStory.latestStory.expiresAt).minutes}m
                </NoSSR>
              </div>
            </button>

            <p className="text-xs text-gray-700 mt-2 font-medium truncate w-20">
              {userStory.userId === user?.id ? 'You' : userStory.latestStory.userName}
            </p>
          </div>
        ))}

        {/* No Stories Message */}
        {userStories.length === 0 && !user && (
          <div className="flex-1 text-center py-12">
            <div className="text-gray-500">
              <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                <Play className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-lg font-semibold text-gray-700 mb-2">No Stories Yet</p>
              <p className="text-sm text-gray-500">Stories from your community will appear here</p>
            </div>
          </div>
        )}


      </div>

      {/* Story Viewing Modal - Full Screen Outside All Containers */}
      {showModal && selectedUserStories && (
        <div
          className="fixed inset-0 z-[9999] bg-black"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 9999,
            width: '100vw',
            height: '100vh'
          }}
        >
          {/* Full Screen Story Container */}
          <div className="relative w-full h-full flex flex-col overflow-hidden">
            {/* Progress Bars - Full Width */}
            <div className="absolute top-4 left-4 right-4 z-30 flex space-x-2">
              {selectedUserStories.stories.map((_, index: number) => (
                <div key={index} className="flex-1 h-1 bg-white bg-opacity-30 rounded-full overflow-hidden">
                  <div
                    className={`h-full bg-white transition-all duration-300 ${
                      index < currentStoryIndex ? 'w-full' :
                      index === currentStoryIndex ? 'progress-bar-anim' : 'w-0'
                    }`}
                    style={index === currentStoryIndex ? {
                      width: `${progress}%`,
                      transition: isPaused ? 'none' : 'width 50ms linear',
                    } : index < currentStoryIndex ? { width: '100%' } : { width: '0%' }}
                  />
                </div>
              ))}
            </div>

            {/* Story Controls - Top Right */}
            <div className="absolute top-4 right-4 z-30 flex space-x-3">
              {/* Pause/Play Button */}
              <button
                onClick={togglePause}
                className="w-12 h-12 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-opacity-80 transition-all duration-200 border border-white border-opacity-20"
              >
                {isPaused ? <Play className="w-6 h-6 ml-0.5" /> : <Pause className="w-6 h-6" />}
              </button>

              {/* Close Button */}
              <button
                onClick={closeModal}
                className="w-12 h-12 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-opacity-80 transition-all duration-200 border border-white border-opacity-20"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Story Content - Full Screen */}
            <div className="flex-1 flex flex-col relative">
              {/* User Header - Overlay Style */}
              <div className="absolute top-16 left-4 right-4 z-20 flex items-center p-4 bg-black bg-opacity-50 backdrop-blur-sm rounded-2xl">
                <img
                  src={selectedUserStories.stories[currentStoryIndex].userAvatar || '/favicon.png'}
                  alt={selectedUserStories.stories[currentStoryIndex].userName}
                  className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
                />
                <div className="flex-1 ml-4">
                  <p className="font-semibold text-white">{selectedUserStories.stories[currentStoryIndex].userName}</p>
                  <p className="text-sm text-gray-300">
                    <NoSSR fallback="24h remaining">
                      {getTimeRemaining(selectedUserStories.stories[currentStoryIndex].expiresAt).hours}h {getTimeRemaining(selectedUserStories.stories[currentStoryIndex].expiresAt).minutes}m remaining
                    </NoSSR>
                  </p>
                </div>
                <div className="text-sm text-white bg-white bg-opacity-20 px-3 py-1 rounded-full border border-white border-opacity-30">
                  {currentStoryIndex + 1} / {selectedUserStories.stories.length}
                </div>
              </div>

              {/* Story Image - Full Screen Background */}
              {selectedUserStories.stories[currentStoryIndex].mediaUrl && (
                <div className="absolute inset-0 flex items-center justify-center bg-black">
                  <img
                    src={selectedUserStories.stories[currentStoryIndex].mediaUrl}
                    alt={`${selectedUserStories.stories[currentStoryIndex].userName}'s story`}
                    className="max-w-full max-h-full object-contain"
                    style={{
                      width: 'auto',
                      height: 'auto',
                      maxWidth: '100vw',
                      maxHeight: '100vh'
                    }}
                  />
                </div>
              )}

              {/* Story Text - Overlay Style */}
              {selectedUserStories.stories[currentStoryIndex].content && (
                <div className="absolute bottom-20 left-4 right-4 z-20 p-6 bg-black bg-opacity-50 backdrop-blur-sm rounded-2xl">
                  <p className="text-white text-center leading-relaxed text-lg">{selectedUserStories.stories[currentStoryIndex].content}</p>
                </div>
              )}

              {/* Pause Overlay - Full Screen */}
              {isPaused && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30">
                  <div className="bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl p-8 flex flex-col items-center space-y-4 shadow-xl border border-gray-200">
                    <div className="flex items-center space-x-3">
                      <Pause className="w-8 h-8 text-gray-700" />
                      <span className="text-gray-700 font-semibold text-xl">Paused</span>
                    </div>
                    <div className="text-center text-sm text-gray-600">
                      <p>Tap to resume • Swipe to navigate</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Navigation Areas - Left/Right tap zones */}
              <div className="absolute inset-0 flex">
                {/* Previous Story Area */}
                <div
                  className="flex-1 cursor-pointer"
                  onClick={goToPreviousStory}
                />
                {/* Next Story Area */}
                <div
                  className="flex-1 cursor-pointer"
                  onClick={goToNextStory}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>

    {/* Create Story Modal - MOVED OUTSIDE MAIN CONTAINER */}
    {showCreateStory && (
      <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4" style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}>
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
          onClick={() => {
            setShowCreateStory(false);
            setStoryContent('');
            setStoryImage(null);
          }}
        />

        {/* Modal Content */}
        <div className="relative bg-gradient-to-br from-green-50 to-blue-50 rounded-3xl p-8 w-full max-w-md mx-auto shadow-2xl border border-white/20 backdrop-blur-sm z-[10000]">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-1">Create Story</h3>
              <p className="text-gray-600 text-sm">Share a moment with your community</p>
            </div>
            <button
              onClick={() => {
                setShowCreateStory(false);
                setStoryContent('');
                setStoryImage(null);
              }}
              className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-white/50 rounded-xl"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Form Content */}
            <div className="space-y-6">
              {/* Image Upload */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                  <Camera className="w-4 h-4 text-blue-600" />
                  <span>Add Photo (Optional)</span>
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-2xl p-6 text-center bg-gray-50 hover:border-blue-400 hover:bg-blue-50 transition-all duration-300 group">
                  {storyImage ? (
                    <div className="relative">
                      <img
                        src={storyImage}
                        alt="Story preview"
                        className="w-full h-48 object-cover rounded-xl shadow-sm border border-gray-200"
                      />
                      <button
                        onClick={() => setStoryImage(null)}
                        className="absolute top-3 right-3 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm hover:bg-red-600 transition-colors shadow-lg"
                      >
                        ×
                      </button>
                    </div>
                  ) : (
                    <>
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-105 transition-transform duration-300">
                        <Camera className="w-8 h-8 text-white" />
                      </div>
                      <p className="text-gray-700 mb-4 font-medium">Add a photo to your story</p>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        id="story-image-upload"
                        ref={fileInputRef}
                      />
                      <label
                        htmlFor="story-image-upload"
                        className="inline-block bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-indigo-700 cursor-pointer transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 font-semibold"
                      >
                        Choose Photo
                      </label>
                    </>
                  )}
                </div>
              </div>

              {/* Text Input */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                  <span className="text-lg">✍️</span>
                  <span>Add Text (Optional)</span>
                </label>
                <textarea
                  value={storyContent}
                  onChange={(e) => setStoryContent(e.target.value)}
                  placeholder="What's happening with your pets today? 🐕🐱"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none bg-white transition-all duration-300 placeholder-gray-500 text-gray-800 shadow-sm hover:border-gray-400"
                  rows={4}
                  maxLength={200}
                />
                <div className="flex justify-between items-center mt-2">
                  <p className="text-xs text-gray-500">
                    {storyContent.length}/200 characters
                  </p>
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <span>✨</span>
                    <span>Express yourself</span>
                  </div>
                </div>
              </div>

              {/* Info Box */}
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-2xl">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full animate-pulse"></div>
                  <p className="text-blue-800 font-semibold text-sm">
                    Your story will be visible for 24 hours
                  </p>
                </div>
                <p className="text-blue-700 mt-1 ml-6 text-xs">
                  Share moments that matter with your pet community
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4 pt-2">
                <button
                  onClick={() => {
                    setShowCreateStory(false);
                    setStoryContent('');
                    setStoryImage(null);
                  }}
                  className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 font-semibold"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateStory}
                  disabled={isSubmitting || (!storyContent.trim() && !storyImage)}
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      <span>Sharing...</span>
                    </>
                  ) : (
                    <>
                      <Upload className="w-5 h-5" />
                      <span>Share Story</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* New Story Viewer - Full Screen Outside All Containers */}
      {showStoryViewer && storyViewerData.length > 0 && (
        <div style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, zIndex: 9999 }}>
          <StoryViewer
            userStories={storyViewerData}
            initialUserIndex={initialUserIndex}
            initialStoryIndex={initialStoryIndex}
            onClose={() => setShowStoryViewer(false)}
            onStoryView={handleStoryView}
          />
        </div>
      )}
    </>
  );
}
