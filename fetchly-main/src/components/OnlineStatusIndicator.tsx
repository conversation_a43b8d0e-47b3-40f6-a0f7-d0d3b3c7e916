'use client';

import { useState, useEffect } from 'react';
import { onlineStatusService } from '@/lib/services/online-status-service';

interface OnlineStatusIndicatorProps {
  userId: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showText?: boolean;
}

export default function OnlineStatusIndicator({ 
  userId, 
  size = 'md', 
  className = '',
  showText = false 
}: OnlineStatusIndicatorProps) {
  const [isOnline, setIsOnline] = useState(false);

  useEffect(() => {
    if (!userId) return;

    const unsubscribe = onlineStatusService.getUserOnlineStatus(userId, (online) => {
      setIsOnline(online);
    });

    return () => {
      unsubscribe();
    };
  }, [userId]);

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-2 h-2';
      case 'lg':
        return 'w-4 h-4';
      default:
        return 'w-3 h-3';
    }
  };

  const getTextSize = () => {
    switch (size) {
      case 'sm':
        return 'text-xs';
      case 'lg':
        return 'text-sm';
      default:
        return 'text-xs';
    }
  };

  if (!isOnline && !showText) {
    return null; // Don't show anything if user is offline and no text requested
  }

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      <div 
        className={`${getSizeClasses()} rounded-full border-2 border-white ${
          isOnline 
            ? 'bg-green-500 animate-pulse' 
            : 'bg-gray-400'
        }`}
      />
      {showText && (
        <span className={`${getTextSize()} font-medium ${
          isOnline ? 'text-green-600' : 'text-gray-500'
        }`}>
          {isOnline ? 'Online' : 'Offline'}
        </span>
      )}
    </div>
  );
}
