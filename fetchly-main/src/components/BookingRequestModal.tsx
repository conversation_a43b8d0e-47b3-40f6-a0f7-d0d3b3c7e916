'use client';

import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  X,
  Calendar,
  Clock,
  User,
  Phone,
  Mail,
  MessageSquare,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Plus,
  PawPrint
} from 'lucide-react';
import { apiClient } from '@/lib/api-client';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import toast from 'react-hot-toast';

interface BookingRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  providerId: string;
  providerName: string;
  serviceId?: string;
  serviceName?: string;
  servicePrice?: number;
}

// Pet interface
interface Pet {
  id: string;
  name: string;
  type: string;
  breed?: string;
  age?: number;
  weight?: number;
  imageUrl?: string;
}

// Form data interface
interface BookingFormData {
  date: string;
  time: string;
  duration: number;
  petId: string;
  petName: string;
  petType: string;
  notes: string;
  contactPhone: string;
  contactEmail: string;
}

export default function BookingRequestModal({
  isOpen,
  onClose,
  providerId,
  providerName,
  serviceId,
  serviceName,
  servicePrice
}: BookingRequestModalProps) {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Partial<BookingFormData>>({});
  const [pets, setPets] = useState<Pet[]>([]);
  const [isLoadingPets, setIsLoadingPets] = useState(true);
  const [showAddPet, setShowAddPet] = useState(false);

  // Initialize form data with proper defaults
  const [formData, setFormData] = useState<BookingFormData>({
    date: '',
    time: '',
    duration: 60,
    petId: '',
    petName: '',
    petType: '',
    notes: '',
    contactPhone: user?.phone || '',
    contactEmail: user?.email || ''
  });

  // Update form data when user changes
  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        contactPhone: user.phone || '',
        contactEmail: user.email || ''
      }));
    }
  }, [user]);

  // Fetch user's pets
  const fetchPets = useCallback(async () => {
    if (!user?.id) return;

    try {
      setIsLoadingPets(true);
      console.log('🐕 Fetching pets for user:', user.id);

      const petsRef = collection(db, 'pets');
      const q = query(petsRef, where('ownerId', '==', user.id));

      try {
        const querySnapshot = await getDocs(q);

        const userPets: Pet[] = [];
        querySnapshot.forEach((doc) => {
          const petData = doc.data();
          userPets.push({
            id: doc.id,
            name: petData.name,
            type: petData.type,
            breed: petData.breed,
            age: petData.age,
            weight: petData.weight,
            imageUrl: petData.imageUrl
          });
        });

        console.log('🐕 Found pets:', userPets.length);
        setPets(userPets);

        // Auto-select first pet if available
        if (userPets.length > 0 && !formData.petId) {
          const firstPet = userPets[0];
          setFormData(prev => ({
            ...prev,
            petId: firstPet.id,
            petName: firstPet.name,
            petType: firstPet.type
          }));
        }
      } catch (queryError: any) {
        console.error('❌ Error in pet query:', queryError);
        if (queryError.message?.includes('index')) {
          console.warn('🔥 Pet query may need an index. This is normal for first-time use.');
        }
        toast.error('Failed to load your pets. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error fetching pets:', error);
      toast.error('Failed to load your pets');
    } finally {
      setIsLoadingPets(false);
    }
  }, [user?.id, formData.petId]);

  // Load pets when modal opens
  useEffect(() => {
    if (isOpen && user?.id) {
      fetchPets();
    }
  }, [isOpen, user?.id, fetchPets]);

  // Handle pet selection
  const handlePetSelection = useCallback((pet: Pet) => {
    setFormData(prev => ({
      ...prev,
      petId: pet.id,
      petName: pet.name,
      petType: pet.type
    }));
    setErrors(prev => ({ ...prev, petName: undefined, petType: undefined }));
  }, []);

  // Handle input changes with proper typing
  const handleInputChange = useCallback((field: keyof BookingFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  }, [errors]);

  // Validate form data
  const validateForm = useCallback((): boolean => {
    const newErrors: Partial<BookingFormData> = {};

    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.time) newErrors.time = 'Time is required';
    if (!formData.petId) newErrors.petName = 'Please select a pet';
    if (!formData.contactPhone.trim()) newErrors.contactPhone = 'Phone number is required';
    if (!formData.contactEmail.trim()) newErrors.contactEmail = 'Email is required';

    // Validate date is not in the past
    const selectedDate = new Date(`${formData.date} ${formData.time}`);
    if (selectedDate <= new Date()) {
      newErrors.date = 'Please select a future date and time';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast.error('Please log in to book a service');
      return;
    }

    if (!validateForm()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('🔔 Submitting booking request:', {
        providerId,
        serviceName: serviceName || 'General Service',
        date: formData.date,
        time: formData.time
      });

      const bookingData = {
        providerId,
        providerName,
        serviceId: serviceId || 'general',
        serviceName: serviceName || 'General Service',
        userId: user.id,
        petId: formData.petId,
        petName: formData.petName.trim(),
        petType: formData.petType,
        scheduledDate: new Date(`${formData.date} ${formData.time}`).toISOString(),
        scheduledTime: formData.time,
        duration: formData.duration,
        totalPrice: servicePrice || 0,
        notes: formData.notes.trim(),
        contactPhone: formData.contactPhone.trim(),
        contactEmail: formData.contactEmail.trim()
      };

      const result = await apiClient.post('/api/bookings', bookingData);

      console.log('✅ Booking created successfully:', result);

      toast.success('🎉 Booking request sent! Provider will confirm and send invoice - no payment charged yet.');

      // Reset form and close modal
      setFormData({
        date: '',
        time: '',
        duration: 60,
        petId: '',
        petName: '',
        petType: '',
        notes: '',
        contactPhone: user?.phone || '',
        contactEmail: user?.email || ''
      });
      setErrors({});
      onClose();

    } catch (error: any) {
      console.error('❌ Booking submission error:', error);
      toast.error(error.message || 'Failed to send booking request');
    } finally {
      setIsSubmitting(false);
    }
  }, [user, formData, validateForm, providerId, providerName, serviceId, serviceName, servicePrice, onClose]);

  const calculateEndTime = (startTime: string, duration: number) => {
    const [hours, minutes] = startTime.split(':').map(Number);
    const startMinutes = hours * 60 + minutes;
    const endMinutes = startMinutes + duration;
    const endHours = Math.floor(endMinutes / 60);
    const endMins = endMinutes % 60;
    return `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-gray-800">Book Service</h2>
            <p className="text-sm text-gray-600">Request appointment with {providerName}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Service Info */}
          {serviceName && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-800">{serviceName}</h3>
              {servicePrice && (
                <p className="text-sm text-blue-600">Starting at ${servicePrice}</p>
              )}
            </div>
          )}

          {/* Date and Time */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="w-4 h-4 inline mr-1" />
                Preferred Date
              </label>
              <input
                type="date"
                required
                min={new Date().toISOString().split('T')[0]}
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.date ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                style={{ colorScheme: 'light' }}
                placeholder="mm/dd/yyyy"
              />
              {errors.date && (
                <p className="text-xs text-red-600 mt-1 flex items-center">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  {errors.date}
                </p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Clock className="w-4 h-4 inline mr-1" />
                Preferred Time *
              </label>
              <input
                type="time"
                required
                value={formData.time}
                onChange={(e) => handleInputChange('time', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.time ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
              />
              {errors.time && (
                <p className="text-xs text-red-600 mt-1 flex items-center">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  {errors.time}
                </p>
              )}
            </div>
          </div>

          {/* Duration */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (minutes)
            </label>
            <select
              value={formData.duration}
              onChange={(e) => handleInputChange('duration', Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={30}>30 minutes</option>
              <option value={60}>1 hour</option>
              <option value={90}>1.5 hours</option>
              <option value={120}>2 hours</option>
              <option value={180}>3 hours</option>
            </select>
          </div>

          {/* Pet Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <PawPrint className="w-4 h-4 inline mr-1" />
              Select Pet *
            </label>

            {isLoadingPets ? (
              <div className="flex items-center justify-center py-8 border border-gray-300 rounded-lg">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">Loading your pets...</span>
              </div>
            ) : pets.length === 0 ? (
              <div className="border border-gray-300 rounded-lg p-4 text-center">
                <PawPrint className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600 mb-3">No pets found.</p>
                <button
                  type="button"
                  onClick={() => window.open('/profile?tab=pets', '_blank')}
                  className="text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 mx-auto"
                >
                  <Plus className="w-4 h-4" />
                  Add a pet
                </button>
              </div>
            ) : (
              <div className="space-y-2">
                {pets.map((pet) => (
                  <div
                    key={pet.id}
                    onClick={() => handlePetSelection(pet)}
                    className={`p-3 border rounded-lg cursor-pointer transition-all ${
                      formData.petId === pet.id
                        ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                        : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      {pet.imageUrl ? (
                        <img
                          src={pet.imageUrl}
                          alt={pet.name}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                          <PawPrint className="w-5 h-5 text-gray-500" />
                        </div>
                      )}
                      <div className="flex-1">
                        <p className="font-medium text-gray-800">{pet.name}</p>
                        <p className="text-sm text-gray-600 capitalize">
                          {pet.type} {pet.breed && `• ${pet.breed}`}
                        </p>
                      </div>
                      {formData.petId === pet.id && (
                        <CheckCircle className="w-5 h-5 text-blue-600" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {errors.petName && (
              <p className="text-xs text-red-600 mt-1 flex items-center">
                <AlertCircle className="w-3 h-3 mr-1" />
                Please select a pet
              </p>
            )}
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Phone className="w-4 h-4 inline mr-1" />
                Phone *
              </label>
              <input
                type="tel"
                required
                value={formData.contactPhone}
                onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.contactPhone ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="(*************"
              />
              {errors.contactPhone && (
                <p className="text-xs text-red-600 mt-1 flex items-center">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  {errors.contactPhone}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Mail className="w-4 h-4 inline mr-1" />
                Email *
              </label>
              <input
                type="email"
                required
                value={formData.contactEmail}
                onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.contactEmail ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="<EMAIL>"
              />
              {errors.contactEmail && (
                <p className="text-xs text-red-600 mt-1 flex items-center">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  {errors.contactEmail}
                </p>
              )}
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MessageSquare className="w-4 h-4 inline mr-1" />
              Additional Notes (Optional)
            </label>
            <textarea
              rows={3}
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              placeholder="Any special requirements, allergies, or information about your pet..."
            />
          </div>

          {/* Submit Button */}
          <div className="flex space-x-3 pt-6">
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className="flex-1 px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className={`flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2 ${
                isSubmitting
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : 'bg-gradient-to-r from-blue-600 to-green-600 text-white hover:from-blue-700 hover:to-green-700 transform hover:scale-105 shadow-lg'
              }`}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Sending Request...</span>
                </>
              ) : (
                <>
                  <CheckCircle className="w-5 h-5" />
                  <span>Send Booking Request</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
