'use client';

import { useState, useEffect } from 'react';
import { 
  Crown, 
  Star, 
  Gift, 
  Zap, 
  Shield, 
  Truck, 
  Clock, 
  Users,
  ArrowRight,
  Check,
  X,
  Sparkles,
  Trophy,
  Target
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { MembershipStatus, RewardItem, RewardTransaction, MEMBERSHIP_FEATURES } from '@/types/user';
import { RewardsService, UserService } from '@/lib/firestore';
import { useAuth } from '@/contexts/AuthContext';

interface MembershipRewardsProps {
  membershipStatus: MembershipStatus;
  rewardPoints: number;
  onMembershipUpdate: (status: MembershipStatus) => void;
  onPointsUpdate: (points: number) => void;
}

export default function MembershipRewards({ 
  membershipStatus, 
  rewardPoints, 
  onMembershipUpdate, 
  onPointsUpdate 
}: MembershipRewardsProps) {
  const { user } = useAuth();
  const [showUpgrade, setShowUpgrade] = useState(false);
  const [showRewards, setShowRewards] = useState(false);
  const [availableRewards, setAvailableRewards] = useState<RewardItem[]>([]);
  const [rewardHistory, setRewardHistory] = useState<RewardTransaction[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (showRewards) {
      loadRewards();
    }
  }, [showRewards]);

  const loadRewards = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const [rewards, history] = await Promise.all([
        RewardsService.getAvailableRewards(),
        RewardsService.getUserRewardTransactions(user.id)
      ]);
      
      setAvailableRewards(rewards);
      setRewardHistory(history);
    } catch (error) {
      console.error('Error loading rewards:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeMembership = async () => {
    if (!user) return;

    try {
      setLoading(true);
      await UserService.upgradeMembership(user.id);
      onMembershipUpdate('pro');
      setShowUpgrade(false);
    } catch (error) {
      console.error('Error upgrading membership:', error);
      alert('Failed to upgrade membership. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRedeemReward = async (reward: RewardItem) => {
    if (!user || rewardPoints < reward.pointsCost) return;

    if (!confirm(`Redeem ${reward.name} for ${reward.pointsCost} points?`)) return;

    try {
      setLoading(true);
      await RewardsService.redeemPoints(
        user.id,
        reward.pointsCost,
        reward.id,
        reward.name
      );
      
      onPointsUpdate(rewardPoints - reward.pointsCost);
      await loadRewards(); // Refresh the rewards list
    } catch (error) {
      console.error('Error redeeming reward:', error);
      alert('Failed to redeem reward. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const currentFeatures = MEMBERSHIP_FEATURES[membershipStatus];
  const proFeatures = MEMBERSHIP_FEATURES.pro;

  const getRewardIcon = (category: string) => {
    switch (category) {
      case 'service_discount':
        return <Star className="w-5 h-5 text-yellow-500" />;
      case 'free_service':
        return <Gift className="w-5 h-5 text-green-500" />;
      case 'merchandise':
        return <Trophy className="w-5 h-5 text-purple-500" />;
      case 'cashback':
        return <Target className="w-5 h-5 text-blue-500" />;
      default:
        return <Gift className="w-5 h-5 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Membership Status Card */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className={`p-6 text-white ${
          membershipStatus === 'pro' 
            ? 'bg-gradient-to-r from-yellow-500 to-orange-500' 
            : 'bg-gradient-to-r from-gray-500 to-gray-600'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-lg">
                {membershipStatus === 'pro' ? (
                  <Crown className="w-6 h-6" />
                ) : (
                  <Shield className="w-6 h-6" />
                )}
              </div>
              <div>
                <h3 className="text-lg font-semibold">
                  {membershipStatus === 'pro' ? 'Fetchly Pro' : 'Fetchly Free'}
                </h3>
                <p className={`text-sm ${
                  membershipStatus === 'pro' ? 'text-yellow-100' : 'text-gray-200'
                }`}>
                  {membershipStatus === 'pro' 
                    ? 'Premium member with exclusive benefits' 
                    : 'Basic membership'}
                </p>
              </div>
            </div>
            
            {membershipStatus === 'free' && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowUpgrade(true)}
                className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                <Sparkles className="w-5 h-5" />
                <span>Upgrade</span>
              </motion.button>
            )}
          </div>
        </div>

        {/* Membership Features */}
        <div className="p-6">
          <h4 className="font-semibold text-gray-900 mb-4">Your Benefits</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-3">
              <Users className="w-5 h-5 text-blue-500" />
              <span className="text-sm">
                Up to {currentFeatures.maxPets} pets
              </span>
            </div>
            
            <div className="flex items-center space-x-3">
              {currentFeatures.priorityBooking ? (
                <Check className="w-5 h-5 text-green-500" />
              ) : (
                <X className="w-5 h-5 text-red-500" />
              )}
              <span className="text-sm">Priority booking</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <Star className="w-5 h-5 text-yellow-500" />
              <span className="text-sm">
                {currentFeatures.discountPercentage}% discount on services
              </span>
            </div>
            
            <div className="flex items-center space-x-3">
              {currentFeatures.freeDelivery ? (
                <Check className="w-5 h-5 text-green-500" />
              ) : (
                <X className="w-5 h-5 text-red-500" />
              )}
              <span className="text-sm">Free delivery</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <Zap className="w-5 h-5 text-purple-500" />
              <span className="text-sm">
                {currentFeatures.rewardPointsMultiplier}x reward points
              </span>
            </div>
            
            <div className="flex items-center space-x-3">
              {currentFeatures.emergencySupport ? (
                <Check className="w-5 h-5 text-green-500" />
              ) : (
                <X className="w-5 h-5 text-red-500" />
              )}
              <span className="text-sm">24/7 emergency support</span>
            </div>
          </div>
        </div>
      </div>

      {/* Rewards Points Card */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <Star className="w-6 h-6" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Reward Points</h3>
                <p className="text-purple-100 text-sm">Earn points, get rewards</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold">{rewardPoints.toLocaleString()}</div>
              <p className="text-purple-100 text-sm">Available points</p>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold text-gray-900">How to Earn Points</h4>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setShowRewards(true)}
              className="text-purple-600 hover:text-purple-700 font-medium text-sm"
            >
              View Rewards →
            </motion.button>
          </div>
          
          <div className="space-y-3 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Complete a booking</span>
              <span className="font-medium text-purple-600">+10 points</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Leave a review</span>
              <span className="font-medium text-purple-600">+5 points</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Refer a friend</span>
              <span className="font-medium text-purple-600">+50 points</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Monthly bonus (Pro)</span>
              <span className="font-medium text-purple-600">+25 points</span>
            </div>
          </div>
        </div>
      </div>

      {/* Upgrade Modal */}
      <AnimatePresence>
        {showUpgrade && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowUpgrade(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 w-full max-w-md"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Crown className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Upgrade to Fetchly Pro</h3>
                <p className="text-gray-600">Unlock premium features and exclusive benefits</p>
              </div>

              <div className="space-y-4 mb-6">
                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900">$20</div>
                    <div className="text-sm text-gray-600">per month</div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-green-500" />
                    <span className="text-sm">Up to 10 pets</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-green-500" />
                    <span className="text-sm">Priority booking</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-green-500" />
                    <span className="text-sm">15% discount on all services</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-green-500" />
                    <span className="text-sm">Free monthly grooming</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-green-500" />
                    <span className="text-sm">24/7 emergency support</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-green-500" />
                    <span className="text-sm">2x reward points</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-green-500" />
                    <span className="text-sm">Free delivery</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <button
                  onClick={handleUpgradeMembership}
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 disabled:from-gray-300 disabled:to-gray-300 text-white font-medium py-3 px-4 rounded-lg transition-all"
                >
                  {loading ? 'Upgrading...' : 'Upgrade Now'}
                </button>
                
                <button
                  onClick={() => setShowUpgrade(false)}
                  className="w-full py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors"
                >
                  Maybe Later
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Rewards Modal */}
      <AnimatePresence>
        {showRewards && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowRewards(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold">Reward Store</h3>
                <button
                  onClick={() => setShowRewards(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="mb-6">
                <div className="bg-purple-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {rewardPoints.toLocaleString()} Points
                  </div>
                  <div className="text-sm text-purple-700">Available to spend</div>
                </div>
              </div>

              {loading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-20 bg-gray-200 rounded-lg"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {availableRewards.map((reward) => (
                    <div
                      key={reward.id}
                      className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-purple-300 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        {getRewardIcon(reward.category)}
                        <div>
                          <h4 className="font-medium text-gray-900">{reward.name}</h4>
                          <p className="text-sm text-gray-600">{reward.description}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Star className="w-4 h-4 text-purple-500" />
                            <span className="text-sm font-medium text-purple-600">
                              {reward.pointsCost} points
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <button
                        onClick={() => handleRedeemReward(reward)}
                        disabled={rewardPoints < reward.pointsCost || loading}
                        className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-300 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                      >
                        Redeem
                      </button>
                    </div>
                  ))}
                  
                  {availableRewards.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <Gift className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                      <p>No rewards available at the moment</p>
                    </div>
                  )}
                </div>
              )}

              {rewardHistory.length > 0 && (
                <div className="mt-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Recent Activity</h4>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {rewardHistory.slice(0, 5).map((transaction) => (
                      <div
                        key={transaction.id}
                        className="flex items-center justify-between text-sm"
                      >
                        <span className="text-gray-600">{transaction.description}</span>
                        <span className={`font-medium ${
                          transaction.type === 'earned' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {transaction.type === 'earned' ? '+' : '-'}{Math.abs(transaction.points)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
