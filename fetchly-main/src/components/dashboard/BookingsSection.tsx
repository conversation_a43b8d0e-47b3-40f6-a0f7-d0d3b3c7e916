'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { collection, query, where, orderBy, onSnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Calendar, Clock, User, DollarSign, MessageSquare, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface Booking {
  id: string;
  providerId: string;
  providerName: string;
  serviceName: string;
  petName: string;
  petType: string;
  scheduledDate: string;
  scheduledTime: string;
  totalPrice: number;
  notes: string;
  status: 'pending_provider_approval' | 'approved_awaiting_payment' | 'accepted' | 'rejected' | 'completed' | 'cancelled';
  paymentRequired?: {
    amount: number;
    currency: string;
    status: string;
    requestedAt: any;
  };
  paymentLink?: string;
  createdAt: any;
  updatedAt: any;
}

export default function BookingsSection() {
  const { user } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);

  // Load user's bookings from Firebase in real-time
  useEffect(() => {
    if (!user?.id) return;

    console.log('📋 Setting up bookings listener for user:', user.id);

    // Query with proper ordering (indexes now deployed)
    const q = query(
      collection(db, 'bookings'),
      where('userId', '==', user.id),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const bookingsData: Booking[] = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        const booking: Booking = {
          id: doc.id,
          providerId: data.providerId,
          providerName: data.providerName || 'Provider',
          serviceName: data.serviceName || 'Pet Service',
          petName: data.petName || 'Pet',
          petType: data.petType || 'dog',
          scheduledDate: data.scheduledDate || '',
          scheduledTime: data.scheduledTime || '',
          totalPrice: data.totalPrice || 0,
          notes: data.notes || '',
          status: data.status || 'pending_provider_approval',
          paymentRequired: data.paymentRequired,
          paymentLink: data.paymentLink,
          createdAt: data.createdAt,
          updatedAt: data.updatedAt
        };
        bookingsData.push(booking);
      });

      console.log('📊 Loaded bookings for user:', bookingsData.length);
      setBookings(bookingsData);
      setLoading(false);
    }, (error) => {
      console.error('❌ Error loading user bookings:', error);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [user?.id]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'accepted':
      case 'approved_awaiting_payment':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'rejected':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'pending_provider_approval':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'confirmed':
      case 'paid':
        return <CheckCircle className="w-5 h-5 text-blue-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'Accepted';
      case 'approved_awaiting_payment':
        return 'Approved - Payment Required';
      case 'rejected':
        return 'Declined';
      case 'pending_provider_approval':
        return 'Pending Approval';
      case 'confirmed':
        return 'Confirmed';
      case 'paid':
        return 'Paid';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'approved_awaiting_payment':
        return 'bg-blue-100 text-blue-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending_provider_approval':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'paid':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading your bookings...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-gray-900">Your Bookings</h3>
          <p className="text-gray-600 text-sm">Track your pet service appointments</p>
        </div>
        <div className="text-sm text-gray-500">
          {bookings.length} booking{bookings.length !== 1 ? 's' : ''}
        </div>
      </div>

      {bookings.length === 0 ? (
        <div className="text-center py-8">
          <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-2">No bookings yet</p>
          <p className="text-sm text-gray-400">Book a service to see your appointments here</p>
        </div>
      ) : (
        <div className="space-y-4">
          {bookings.slice(0, 5).map((booking) => (
            <div key={booking.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{booking.serviceName}</h4>
                      <p className="text-sm text-gray-500">with {booking.providerName}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{formatDate(booking.scheduledDate)}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{formatTime(booking.scheduledTime)}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">🐾 {booking.petName} ({booking.petType})</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <DollarSign className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">${booking.totalPrice}</span>
                    </div>
                  </div>

                  {booking.notes && (
                    <div className="mb-3">
                      <div className="flex items-start space-x-2">
                        <MessageSquare className="w-4 h-4 text-gray-400 mt-0.5" />
                        <p className="text-sm text-gray-600">{booking.notes}</p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex flex-col items-end space-y-2">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(booking.status)}
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                      {getStatusText(booking.status)}
                    </span>
                  </div>
                  
                  {booking.status === 'pending_provider_approval' && (
                    <p className="text-xs text-gray-500 text-right">
                      Waiting for provider response
                    </p>
                  )}
                  
                  {(booking.status === 'accepted' || booking.status === 'approved_awaiting_payment') && (
                    <div className="text-right">
                      <p className="text-xs text-green-600 font-medium">
                        Booking approved!
                      </p>
                      {booking.paymentRequired && (
                        <div className="mt-1">
                          <p className="text-xs text-blue-600">
                            Payment required: ${booking.paymentRequired.amount}
                          </p>
                          {booking.paymentLink && (
                            <a
                              href={booking.paymentLink}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-xs text-blue-600 underline hover:text-blue-800 mt-1 block"
                            >
                              Pay Now
                            </a>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                  
                  {booking.status === 'rejected' && (
                    <p className="text-xs text-red-600 text-right">
                      Booking declined
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
          
          {bookings.length > 5 && (
            <div className="text-center pt-4">
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                View all bookings ({bookings.length})
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
