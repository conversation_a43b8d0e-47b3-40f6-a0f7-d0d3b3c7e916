'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  collection, 
  query, 
  where, 
  onSnapshot, 
  doc, 
  updateDoc, 
  serverTimestamp,
  getDocs,
  orderBy,
  limit
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { User, Wifi, WifiOff } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface OnlineUser {
  id: string;
  displayName: string;
  email: string;
  role: 'pet_owner' | 'provider' | 'admin';
  avatar?: string;
  isOnline: boolean;
  lastSeen: any;
  businessName?: string; // For providers
}

export default function OnlineUsersModule() {
  const { user } = useAuth();
  const router = useRouter();
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);
  const [friends, setFriends] = useState<string[]>([]);
  const [followedProviders, setFollowedProviders] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  // Update user's online status
  useEffect(() => {
    if (!user?.id) return;

    const updateOnlineStatus = async (isOnline: boolean) => {
      try {
        const userRef = doc(db, 'users', user.id);
        await updateDoc(userRef, {
          isOnline,
          lastSeen: serverTimestamp()
        });
      } catch (error) {
        console.error('Error updating online status:', error);
      }
    };

    // Set user as online when component mounts
    updateOnlineStatus(true);

    // Set user as offline when page unloads
    const handleBeforeUnload = () => {
      updateOnlineStatus(false);
    };

    // Set user as offline when tab becomes hidden
    const handleVisibilityChange = () => {
      if (document.hidden) {
        updateOnlineStatus(false);
      } else {
        updateOnlineStatus(true);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      updateOnlineStatus(false);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [user?.id]);

  // Load user's friends and followed providers
  useEffect(() => {
    if (!user?.id) return;

    const loadUserConnections = async () => {
      try {
        // Load friends
        const friendsQuery = query(
          collection(db, 'friendships'),
          where('userId', '==', user.id),
          where('status', '==', 'accepted')
        );
        const friendsSnapshot = await getDocs(friendsQuery);
        const friendIds = friendsSnapshot.docs.map(doc => doc.data().friendId);

        // Also check reverse friendships
        const reverseFriendsQuery = query(
          collection(db, 'friendships'),
          where('friendId', '==', user.id),
          where('status', '==', 'accepted')
        );
        const reverseFriendsSnapshot = await getDocs(reverseFriendsQuery);
        const reverseFriendIds = reverseFriendsSnapshot.docs.map(doc => doc.data().userId);

        const allFriendIds = [...new Set([...friendIds, ...reverseFriendIds])];
        setFriends(allFriendIds);

        // Load followed providers
        const followsQuery = query(
          collection(db, 'follows'),
          where('followerId', '==', user.id)
        );
        const followsSnapshot = await getDocs(followsQuery);
        const providerIds = followsSnapshot.docs.map(doc => doc.data().providerId);
        setFollowedProviders(providerIds);

      } catch (error) {
        console.error('Error loading user connections:', error);
      }
    };

    loadUserConnections();
  }, [user?.id]);

  // Listen for online users
  useEffect(() => {
    if (!user?.id || (friends.length === 0 && followedProviders.length === 0)) {
      setLoading(false);
      return;
    }

    const relevantUserIds = [...friends, ...followedProviders];
    
    if (relevantUserIds.length === 0) {
      setLoading(false);
      return;
    }

    // Firestore 'in' query has a limit of 10 items, so we need to batch
    const batches = [];
    for (let i = 0; i < relevantUserIds.length; i += 10) {
      const batch = relevantUserIds.slice(i, i + 10);
      batches.push(batch);
    }

    const unsubscribes: (() => void)[] = [];

    batches.forEach(batch => {
      const onlineQuery = query(
        collection(db, 'users'),
        where('__name__', 'in', batch),
        where('isOnline', '==', true)
      );

      const unsubscribe = onSnapshot(onlineQuery, (snapshot) => {
        const batchOnlineUsers = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as OnlineUser[];

        setOnlineUsers(prevUsers => {
          // Remove users from this batch and add new ones
          const filteredUsers = prevUsers.filter(user => !batch.includes(user.id));
          return [...filteredUsers, ...batchOnlineUsers];
        });
      });

      unsubscribes.push(unsubscribe);
    });

    setLoading(false);

    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe());
    };
  }, [user?.id, friends, followedProviders]);

  const handleUserClick = (clickedUser: OnlineUser) => {
    if (clickedUser.role === 'provider') {
      router.push(`/provider/profile?id=${clickedUser.id}`);
    } else {
      router.push(`/profile?id=${clickedUser.id}`);
    }
  };

  const formatLastSeen = (lastSeen: any) => {
    if (!lastSeen) return 'Just now';
    
    const now = new Date();
    const lastSeenDate = lastSeen.toDate ? lastSeen.toDate() : new Date(lastSeen);
    const diffMs = now.getTime() - lastSeenDate.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex items-center space-x-2 mb-4">
          <Wifi className="w-5 h-5 text-green-500" />
          <h3 className="font-semibold text-gray-800">Online</h3>
        </div>
        <div className="animate-pulse space-y-3">
          {[1, 2, 3].map(i => (
            <div key={i} className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mt-1"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Wifi className="w-5 h-5 text-green-500" />
          <h3 className="font-semibold text-gray-800">Online</h3>
          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
            {onlineUsers.length}
          </span>
        </div>
      </div>

      {onlineUsers.length === 0 ? (
        <div className="text-center py-8">
          <WifiOff className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <p className="text-gray-500 text-sm">No friends or providers online</p>
          <p className="text-gray-400 text-xs mt-1">
            Connect with pet owners and providers to see when they're online
          </p>
        </div>
      ) : (
        <div className="space-y-3 max-h-64 overflow-y-auto">
          {onlineUsers.map(onlineUser => (
            <div
              key={onlineUser.id}
              onClick={() => handleUserClick(onlineUser)}
              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
            >
              <div className="relative">
                {onlineUser.avatar ? (
                  <img
                    src={onlineUser.avatar}
                    alt={onlineUser.displayName}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-white" />
                  </div>
                )}
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <p className="font-medium text-gray-900 truncate">
                    {onlineUser.role === 'provider' && onlineUser.businessName 
                      ? onlineUser.businessName 
                      : onlineUser.displayName
                    }
                  </p>
                  {onlineUser.role === 'provider' && (
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">
                      Provider
                    </span>
                  )}
                </div>
                <p className="text-xs text-green-600 font-medium">
                  {onlineUser.isOnline ? 'Online now' : formatLastSeen(onlineUser.lastSeen)}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
