import { collection, addDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export async function createSampleNotifications(userId: string) {
  const notificationsRef = collection(db, 'notifications');
  
  const sampleNotifications = [
    {
      userId,
      title: 'New Booking Request',
      message: '<PERSON> has requested a dog walking service for tomorrow at 2 PM.',
      type: 'booking',
      read: false,
      createdAt: Timestamp.now(),
      actionUrl: '/provider/dashboard?tab=bookings'
    },
    {
      userId,
      title: 'Payment Received',
      message: 'You received $45.00 for pet sitting services from <PERSON>.',
      type: 'payment',
      read: false,
      createdAt: Timestamp.fromDate(new Date(Date.now() - 3600000)), // 1 hour ago
      actionUrl: '/provider/dashboard?tab=earnings'
    },
    {
      userId,
      title: 'New 5-Star Review!',
      message: '<PERSON> left you a 5-star review: "Amazing service! My dog loved the grooming session."',
      type: 'review',
      read: true,
      createdAt: Timestamp.fromDate(new Date(Date.now() - 10800000)), // 3 hours ago
      actionUrl: '/provider/dashboard?tab=reviews'
    },
    {
      userId,
      title: 'Community Post Liked',
      message: 'Your post about "Best Dog Training Tips" received 15 new likes!',
      type: 'community',
      read: true,
      createdAt: Timestamp.fromDate(new Date(Date.now() - 86400000)), // 1 day ago
      actionUrl: '/community'
    },
    {
      userId,
      title: 'Emergency Alert',
      message: 'A pet owner in your area is looking for emergency veterinary services.',
      type: 'emergency',
      read: false,
      createdAt: Timestamp.fromDate(new Date(Date.now() - 300000)), // 5 minutes ago
      actionUrl: '/emergency'
    },
    {
      userId,
      title: 'Profile Update Required',
      message: 'Please update your availability calendar for the upcoming week.',
      type: 'system',
      read: true,
      createdAt: Timestamp.fromDate(new Date(Date.now() - 172800000)), // 2 days ago
      actionUrl: '/provider/dashboard?tab=settings'
    }
  ];

  try {
    for (const notification of sampleNotifications) {
      await addDoc(notificationsRef, notification);
    }
    console.log('Sample notifications created successfully!');
  } catch (error) {
    console.error('Error creating sample notifications:', error);
  }
}
