"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleStripeWebhook = exports.getStripeBalance = exports.getStripeDashboardLink = void 0;
const functions = require("firebase-functions");
const admin = require("firebase-admin");
const stripe_1 = require("stripe");
const stripe = new stripe_1.default(functions.config().stripe.secret_key, {
    apiVersion: '2023-10-16',
});
// Helper to get the Stripe account ID for a user
async function getStripeAccountId(userId) {
    var _a;
    const userDoc = await admin.firestore().collection('users').doc(userId).get();
    return ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.stripeAccountId) || null;
}
// Get the Stripe dashboard login link for a connected account
exports.getStripeDashboardLink = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User not authenticated');
    }
    try {
        const userId = context.auth.uid;
        const accountId = await getStripeAccountId(userId);
        if (!accountId) {
            throw new functions.https.HttpsError('not-found', 'No Stripe account found');
        }
        const loginLink = await stripe.accounts.createLoginLink(accountId);
        return { url: loginLink.url };
    }
    catch (error) {
        console.error('Error creating Stripe dashboard link:', error);
        throw new functions.https.HttpsError('internal', 'Failed to create Stripe dashboard link');
    }
});
// Get the account balance for a connected account
exports.getStripeBalance = functions.https.onCall(async (data, context) => {
    var _a, _b, _c;
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User not authenticated');
    }
    try {
        const userId = context.auth.uid;
        const accountId = await getStripeAccountId(userId);
        if (!accountId) {
            throw new functions.https.HttpsError('not-found', 'No Stripe account found');
        }
        const balance = await stripe.balance.retrieve({
            stripeAccount: accountId,
        });
        return {
            available: ((_a = balance.available[0]) === null || _a === void 0 ? void 0 : _a.amount) || 0,
            pending: ((_b = balance.pending[0]) === null || _b === void 0 ? void 0 : _b.amount) || 0,
            currency: ((_c = balance.available[0]) === null || _c === void 0 ? void 0 : _c.currency) || 'usd',
        };
    }
    catch (error) {
        console.error('Error fetching Stripe balance:', error);
        throw new functions.https.HttpsError('internal', 'Failed to fetch Stripe balance');
    }
});
// Handle Stripe webhooks
exports.handleStripeWebhook = functions.https.onRequest(async (req, res) => {
    const sig = req.headers['stripe-signature'];
    const endpointSecret = functions.config().stripe.webhook_secret;
    let event;
    try {
        event = stripe.webhooks.constructEvent(req.rawBody, sig, endpointSecret);
    }
    catch (err) {
        console.error(`Webhook signature verification failed: ${err}`);
        res.status(400).send(`Webhook Error: ${err}`);
        return;
    }
    // Handle the event
    switch (event.type) {
        case 'account.updated':
            const account = event.data.object;
            await handleAccountUpdated(account);
            break;
        // Add more event types as needed
        default:
            console.log(`Unhandled event type ${event.type}`);
    }
    // Return a 200 response to acknowledge receipt of the event
    res.json({ received: true });
});
async function handleAccountUpdated(account) {
    const userId = account.metadata.userId;
    if (!userId)
        return;
    const userRef = admin.firestore().collection('users').doc(userId);
    await userRef.update({
        isStripeConnected: account.details_submitted,
        stripeDashboardLink: `https://dashboard.stripe.com/connect/accounts/${account.id}`,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });
}
//# sourceMappingURL=stripe.js.map