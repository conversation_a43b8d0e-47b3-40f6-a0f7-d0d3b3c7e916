{"version": 3, "file": "custom-email-handlers.js", "sourceRoot": "", "sources": ["../src/custom-email-handlers.ts"], "names": [], "mappings": ";;;AAAA,gDAAgD;AAChD,wCAAwC;AACxC,yCAAyC;AAEzC,uDAAuD;AACvD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;IACvB,KAAK,CAAC,aAAa,EAAE,CAAC;AACxB,CAAC;AAED,kCAAkC;AAClC,MAAM,iBAAiB,GAAG,GAAG,EAAE;;IAC7B,OAAO,UAAU,CAAC,eAAe,CAAC;QAChC,IAAI,EAAE,CAAA,MAAA,SAAS,CAAC,MAAM,EAAE,CAAC,KAAK,0CAAE,IAAI,KAAI,oBAAoB;QAC5D,IAAI,EAAE,QAAQ,CAAC,CAAA,MAAA,SAAS,CAAC,MAAM,EAAE,CAAC,KAAK,0CAAE,IAAI,KAAI,KAAK,CAAC;QACvD,MAAM,EAAE,KAAK;QACb,IAAI,EAAE;YACJ,IAAI,EAAE,MAAA,SAAS,CAAC,MAAM,EAAE,CAAC,KAAK,0CAAE,IAAI;YACpC,IAAI,EAAE,MAAA,SAAS,CAAC,MAAM,EAAE,CAAC,KAAK,0CAAE,QAAQ;SACzC;QACD,GAAG,EAAE;YACH,OAAO,EAAE,OAAO;SACjB;KACF,CAAC,CAAC;AACL,CAAC,CAAC;AAEF;;;GAGG;AACU,QAAA,uBAAuB,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;;IACpF,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;IAEvB,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;IAChF,CAAC;IAED,IAAI,CAAC;QACH,sCAAsC;QACtC,MAAM,SAAS,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC,KAAK,EAAE;YACpE,GAAG,EAAE,mCAAmC;YACxC,eAAe,EAAE,KAAK;SACvB,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,iBAAiB,EAAE,CAAC;QAExC,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,sBAAsB,MAAA,SAAS,CAAC,MAAM,EAAE,CAAC,KAAK,0CAAE,IAAI,GAAG;YAC7D,EAAE,EAAE,KAAK;YACT,OAAO,EAAE,6BAA6B;YACtC,IAAI,EAAE,yBAAyB,CAAC,SAAS,CAAC;SAC3C,CAAC;QAEF,MAAM,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAExC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,wCAAwC,EAAE,CAAC;IAC9E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC7D,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,qCAAqC,CAAC,CAAC;IAC1F,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACU,QAAA,2BAA2B,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;;IACxF,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;IAEpC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;IAChF,CAAC;IAED,IAAI,CAAC;QACH,0CAA0C;QAC1C,MAAM,gBAAgB,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,6BAA6B,CAAC,KAAK,EAAE;YAC/E,GAAG,EAAE,mCAAmC;YACxC,eAAe,EAAE,KAAK;SACvB,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,iBAAiB,EAAE,CAAC;QAExC,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,sBAAsB,MAAA,SAAS,CAAC,MAAM,EAAE,CAAC,KAAK,0CAAE,IAAI,GAAG;YAC7D,EAAE,EAAE,KAAK;YACT,OAAO,EAAE,mCAAmC;YAC5C,IAAI,EAAE,6BAA6B,CAAC,gBAAgB,EAAE,WAAW,IAAI,MAAM,CAAC;SAC7E,CAAC;QAEF,MAAM,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAExC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;IAC5E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QACjE,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,mCAAmC,CAAC,CAAC;IACxF,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,2BAA2B,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;;IACxF,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;IAEjD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3B,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,qCAAqC,CAAC,CAAC;IAClG,CAAC;IAED,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,iBAAiB,EAAE,CAAC;QAExC,iCAAiC;QACjC,MAAM,eAAe,GAAG;YACtB,IAAI,EAAE,uBAAuB,MAAA,SAAS,CAAC,MAAM,EAAE,CAAC,KAAK,0CAAE,IAAI,GAAG;YAC9D,EAAE,EAAE,QAAQ;YACZ,OAAO,EAAE,6CAA6C;YACtD,IAAI,EAAE,mCAAmC,CAAC,WAAW,IAAI,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;SAClF,CAAC;QAEF,iCAAiC;QACjC,MAAM,eAAe,GAAG;YACtB,IAAI,EAAE,uBAAuB,MAAA,SAAS,CAAC,MAAM,EAAE,CAAC,KAAK,0CAAE,IAAI,GAAG;YAC9D,EAAE,EAAE,QAAQ;YACZ,OAAO,EAAE,6CAA6C;YACtD,IAAI,EAAE,mCAAmC,CAAC,WAAW,IAAI,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;SAClF,CAAC;QAEF,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC;YACrC,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC;SACtC,CAAC,CAAC;QAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,8CAA8C,EAAE,CAAC;IACpF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QAClE,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,2CAA2C,CAAC,CAAC;IAChG,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,mBAAmB,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;;IAChF,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,iCAAiC;IAE9E,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;QACtB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,+BAA+B,CAAC,CAAC;IAC5F,CAAC;IAED,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,iBAAiB,EAAE,CAAC;QAExC,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,uBAAuB,MAAA,SAAS,CAAC,MAAM,EAAE,CAAC,KAAK,0CAAE,IAAI,GAAG;YAC9D,EAAE,EAAE,KAAK;YACT,OAAO,EAAE,6BAA6B,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,0BAA0B;YAC7G,IAAI,EAAE,2BAA2B,CAAC,WAAW,IAAI,MAAM,EAAE,MAAM,CAAC;SACjE,CAAC;QAEF,MAAM,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAExC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;IAC1E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,iCAAiC,CAAC,CAAC;IACtF,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,0BAA0B;AAC1B,SAAS,yBAAyB,CAAC,SAAiB;IAClD,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;uBA0Bc,SAAS;;;;;;;;;;;;;;;;;;;uBAmBT,SAAS,oDAAoD,SAAS;;;;;;;;;;;;;;;;;GAiB1F,CAAC;AACJ,CAAC;AAED,SAAS,6BAA6B,CAAC,gBAAwB,EAAE,WAAmB;IAClF,OAAO;;;;;;;;;;;;;;;;;;mFAkB0E,WAAW;;;;;;;;uBAQvE,gBAAgB;;;;;;;;;;;uBAWhB,gBAAgB,oDAAoD,gBAAgB;;;;;;;;;;;;;;;;;GAiBxG,CAAC;AACJ,CAAC;AAED,SAAS,mCAAmC,CAAC,WAAmB,EAAE,QAAgB,EAAE,IAAmB;IACrG,MAAM,UAAU,GAAG,IAAI,KAAK,KAAK,CAAC;IAElC,OAAO;;;;;;6BAMoB,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;;;;;;qGAMsC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;;;;;;mFAMpD,WAAW;;;cAGhF,UAAU;QACV,CAAC,CAAC,kEAAkE,QAAQ,YAAY;QACxF,CAAC,CAAC,yFACJ;;;YAGA,UAAU,CAAC,CAAC,CAAC;;;;;;;;;WASd,CAAC,CAAC,CAAC;;;;;;;;WAQH;;;sCAG2B,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE,OAAO,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE;;;;;;;;;;;;;;;;GAgBxG,CAAC;AACJ,CAAC;AAED,SAAS,2BAA2B,CAAC,WAAmB,EAAE,MAAc;IACtE,MAAM,SAAS,GAAG,MAAM,KAAK,SAAS,CAAC;IAEvC,OAAO;;;;;;yCAMgC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;;;;;;iHAMsC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;;;;;;mFAMhE,WAAW;;;yDAGrC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;;;YAG/E,SAAS,CAAC,CAAC,CAAC;;;;;;;;WAQb,CAAC,CAAC,CAAC;;;;;;;;WAQH;;;;;;;sCAO2B,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE,OAAO,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE;;;;;;;;;;;;;;;;GAgBxG,CAAC;AACJ,CAAC"}