{"name": "fetchly-functions", "version": "1.0.0", "description": "Firebase Functions for Fetchly Platform", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"@types/stripe": "^8.0.416", "firebase-admin": "^12.7.0", "firebase-functions": "^4.9.0", "stripe": "^18.4.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "private": true}