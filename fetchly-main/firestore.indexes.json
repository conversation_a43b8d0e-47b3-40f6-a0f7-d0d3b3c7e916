{"indexes": [{"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "joinedDate", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "verified", "order": "ASCENDING"}]}, {"collectionGroup": "pets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "pets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "providerProfiles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "providerProfiles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "location.city", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "services", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "services", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "price", "order": "ASCENDING"}]}, {"collectionGroup": "services", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "comments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "postId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isStory", "order": "ASCENDING"}, {"fieldPath": "expiresAt", "order": "DESCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isStory", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "petOwnerId", "order": "ASCENDING"}, {"fieldPath": "bookingDate", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "bookingDate", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "bookingDate", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION", "fields": [{"fieldPath": "petOwnerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "providerEarnings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "providerClients", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "lastBooking", "order": "DESCENDING"}]}, {"collectionGroup": "providerCalendar", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "petOwnerTransactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "rewardTransactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "blogPosts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "authorId", "order": "ASCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}, {"collectionGroup": "blogPosts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublished", "order": "ASCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}, {"collectionGroup": "blogPosts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "chatRoomId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "earnings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "earnedAt", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "providers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "providers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "city", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "providers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "serviceType", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "services", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "active", "order": "ASCENDING"}]}, {"collectionGroup": "vaccinations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "dateAdministered", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "id", "order": "ASCENDING"}, {"fieldPath": "avatar", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "id", "order": "ASCENDING"}, {"fieldPath": "banner", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "avatar", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "banner", "order": "ASCENDING"}]}, {"collectionGroup": "chats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participantIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "lastMessageTime", "order": "DESCENDING"}]}, {"collectionGroup": "chats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participantIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isSupportChat", "order": "ASCENDING"}, {"fieldPath": "participantIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "lastMessageTime", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "chatId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "senderId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "chatId", "order": "ASCENDING"}, {"fieldPath": "readBy", "arrayConfig": "CONTAINS"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "chat_notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isRead", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "chat_notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "chatId", "order": "ASCENDING"}, {"fieldPath": "isRead", "order": "ASCENDING"}]}, {"collectionGroup": "suspensions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "suspendedAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "onlineUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isOnline", "order": "ASCENDING"}, {"fieldPath": "lastSeen", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "calendar_events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "startDate", "order": "ASCENDING"}]}], "fieldOverrides": []}