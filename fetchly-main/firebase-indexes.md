# Firebase Firestore Indexes Required

## Required Indexes for Fetchly Platform

### 1. Messages Collection Index
**Collection**: `messages`
**Fields**:
- `receiverId` (Ascending)
- `timestamp` (Descending)

**How to create**:
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project: `fetchly-724b6`
3. Go to Firestore Database
4. Click on "Indexes" tab
5. Click "Create Index"
6. Set:
   - Collection ID: `messages`
   - Field 1: `receiverId` (Ascending)
   - Field 2: `timestamp` (Descending)
7. Click "Create"

### 2. Support Tickets Index
**Collection**: `supportTickets`
**Fields**:
- `userId` (Ascending)
- `createdAt` (Descending)

**How to create**:
1. Same steps as above but with:
   - Collection ID: `supportTickets`
   - Field 1: `userId` (Ascending)
   - Field 2: `createdAt` (Descending)

### 3. Alternative: Use Firebase CLI

If you prefer using Firebase CLI:

```bash
# Install Firebase CLI if not already installed
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firestore in your project
firebase init firestore

# Deploy indexes (if you have firestore.indexes.json)
firebase deploy --only firestore:indexes
```

### 4. Firestore Rules Update

Make sure your Firestore rules allow the queries:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Messages collection
    match /messages/{messageId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.senderId || 
         request.auth.uid == resource.data.receiverId);
    }
    
    // Support tickets collection
    match /supportTickets/{ticketId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow read, write: if request.auth != null && 
        request.auth.token.role == 'admin';
    }
    
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null; // Allow reading other users for chat
    }
  }
}
```

## Quick Fix Links

If you're getting the index error, click this link to create the index automatically:
https://console.firebase.google.com/v1/r/project/fetchly-724b6/firestore/indexes?create_composite=Ck5wcm9qZWN0cy9mZXRjaGx5LTcyNGI2L2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9tZXNzYWdlcy9pbmRleGVzL18QARoOCgpyZWNlaXZlcklkEAEaDQoJdGltZXN0YW1wEAIaDAoIX19uYW1lX18QAg

This will take you directly to the Firebase console with the index pre-configured.
