#!/bin/bash

# Fetchly Production Deployment Script
# This script handles the complete deployment process to Heroku

echo "🚀 Starting Fetchly Production Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Heroku CLI is installed
if ! command -v heroku &> /dev/null; then
    print_error "Heroku CLI is not installed. Please install it first."
    exit 1
fi

# Check if logged into Heroku
if ! heroku auth:whoami &> /dev/null; then
    print_error "Not logged into Heroku. Please run 'heroku login' first."
    exit 1
fi

# Step 1: Build the application
print_status "Building the application..."
npm run build
if [ $? -ne 0 ]; then
    print_error "Build failed. Please fix the errors and try again."
    exit 1
fi
print_success "Build completed successfully!"

# Step 2: Check if Heroku app exists
APP_NAME="fetchlypr"
if heroku apps:info $APP_NAME &> /dev/null; then
    print_success "Heroku app '$APP_NAME' exists."
else
    print_status "Creating Heroku app '$APP_NAME'..."
    heroku create $APP_NAME
    if [ $? -ne 0 ]; then
        print_error "Failed to create Heroku app."
        exit 1
    fi
    print_success "Heroku app created successfully!"
fi

# Step 3: Set Heroku buildpacks
print_status "Setting Heroku buildpacks..."
heroku buildpacks:clear -a $APP_NAME
heroku buildpacks:add heroku/nodejs -a $APP_NAME

# Step 4: Set environment variables (you'll need to update these with actual values)
print_status "Setting environment variables..."

# Basic configuration
heroku config:set NODE_ENV=production -a $APP_NAME
heroku config:set NEXT_PUBLIC_APP_URL=https://fetchlypr.herokuapp.com -a $APP_NAME

# You'll need to set these with your actual values:
print_warning "Please set the following environment variables manually:"
echo "heroku config:set NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key -a $APP_NAME"
echo "heroku config:set NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain -a $APP_NAME"
echo "heroku config:set NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id -a $APP_NAME"
echo "heroku config:set STRIPE_SECRET_KEY=your_stripe_secret -a $APP_NAME"
echo "heroku config:set NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable -a $APP_NAME"
echo "heroku config:set JWT_SECRET=your_jwt_secret -a $APP_NAME"

# Step 5: Add git remote if not exists
if git remote get-url heroku &> /dev/null; then
    print_success "Heroku remote already exists."
else
    print_status "Adding Heroku remote..."
    heroku git:remote -a $APP_NAME
fi

# Step 6: Commit changes if any
print_status "Checking for uncommitted changes..."
if [[ -n $(git status -s) ]]; then
    print_status "Committing changes..."
    git add .
    git commit -m "Production deployment - $(date)"
fi

# Step 7: Deploy to Heroku
print_status "Deploying to Heroku..."
git push heroku main
if [ $? -ne 0 ]; then
    print_error "Deployment failed. Check the logs above."
    exit 1
fi

# Step 8: Open the app
print_success "Deployment completed successfully!"
print_status "Opening the app..."
heroku open -a $APP_NAME

# Step 9: Show logs
print_status "Showing recent logs..."
heroku logs --tail -a $APP_NAME

print_success "🎉 Fetchly has been deployed to production!"
print_success "URL: https://fetchlypr.herokuapp.com"
