const admin = require('firebase-admin');
const { initializeApp, cert } = require('firebase-admin/app');
const { getFirestore } = require('firebase-admin/firestore');

// Initialize Firebase Admin
if (!admin.apps.length) {
  // For local development, you might need to set up service account
  // For now, we'll use the default credentials
  initializeApp();
}

const db = getFirestore();

async function fixProviderAccounts() {
  try {
    console.log('🔍 Checking for provider users without provider profiles...');
    
    // Get all users with role 'provider'
    const usersSnapshot = await db.collection('users').where('role', '==', 'provider').get();
    
    if (usersSnapshot.empty) {
      console.log('ℹ️ No provider users found');
      return;
    }
    
    console.log(`📊 Found ${usersSnapshot.size} provider users`);
    
    for (const userDoc of usersSnapshot.docs) {
      const userData = userDoc.data();
      const userId = userDoc.id;
      
      console.log(`\n👤 Checking provider user: ${userData.name} (${userData.email})`);
      
      // Check if provider profile exists
      const providerSnapshot = await db.collection('providers').where('userId', '==', userId).get();
      
      if (providerSnapshot.empty) {
        console.log(`❌ Missing provider profile for user: ${userData.name}`);
        
        // Create provider profile
        const providerData = {
          userId: userId,
          businessName: userData.name + "'s Business",
          ownerName: userData.name,
          email: userData.email,
          phone: userData.phone || '',
          serviceType: 'General Pet Services',
          address: '',
          city: '',
          state: '',
          zipCode: '',
          description: 'Welcome to my pet services business!',
          experience: '0-1 years',
          specialties: [],
          status: 'pending',
          verified: false,
          featured: false,
          businessHours: {
            monday: { open: '09:00', close: '17:00', closed: false },
            tuesday: { open: '09:00', close: '17:00', closed: false },
            wednesday: { open: '09:00', close: '17:00', closed: false },
            thursday: { open: '09:00', close: '17:00', closed: false },
            friday: { open: '09:00', close: '17:00', closed: false },
            saturday: { open: '09:00', close: '17:00', closed: false },
            sunday: { open: '09:00', close: '17:00', closed: true }
          },
          documents: {},
          businessPhotos: [],
          rating: 0,
          reviewCount: 0,
          totalBookings: 0,
          totalRevenue: 0,
          completionRate: 0,
          responseTime: '< 1 hour',
          responseRate: 0,
          membershipTier: 'free',
          fetchPoints: 0,
          commissionsaved: 0,
          socialMedia: {},
          settings: {
            emailNotifications: true,
            smsNotifications: false,
            bookingNotifications: true,
            marketingEmails: false,
            autoAcceptBookings: false,
            requireDeposit: false,
            cancellationPolicy: 'flexible'
          },
          createdAt: admin.firestore.Timestamp.now(),
          updatedAt: admin.firestore.Timestamp.now()
        };
        
        await db.collection('providers').add(providerData);
        console.log(`✅ Created provider profile for: ${userData.name}`);
      } else {
        console.log(`✅ Provider profile already exists for: ${userData.name}`);
      }
    }
    
    console.log('\n🎉 Provider account fix completed!');
    
  } catch (error) {
    console.error('❌ Error fixing provider accounts:', error);
  }
}

// Run the fix
fixProviderAccounts().then(() => {
  console.log('Script completed');
  process.exit(0);
}).catch((error) => {
  console.error('Script failed:', error);
  process.exit(1);
});
