#!/usr/bin/env node

/**
 * Migration script to transfer data from PostgreSQL to Firebase Firestore
 * Run this script to migrate your existing Fetchly data to Firebase
 */

import { Pool, PoolConfig } from 'pg';
import * as admin from 'firebase-admin';
import * as fs from 'fs';
import * as path from 'path';

// Initialize Firebase Admin SDK
const serviceAccount = {
  type: "service_account",
  project_id: "fetchly-724b6",
  // You'll need to add your service account key here
  // Download it from Firebase Console > Project Settings > Service Accounts
};

// Uncomment and configure when you have the service account key
// admin.initializeApp({
//   credential: admin.credential.cert(serviceAccount),
//   databaseURL: `https://fetchly-724b6-default-rtdb.firebaseio.com`
// });

// const db = admin.firestore();

// PostgreSQL configuration
const pgConfig: PoolConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3005'),
  database: process.env.DB_NAME || 'fetchly_db',
  user: process.env.DB_USER || 'fetchly_user',
  password: process.env.DB_PASSWORD || 'fetchly_password',
};

interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  address?: string;
  date_of_birth?: Date;
  profile_picture?: string;
  fetchly_balance: number;
  membership_status: string;
  reward_points: number;
  saved_providers?: string[];
  notification_preferences?: Record<string, any>;
  emergency_contact?: Record<string, any>;
  role: string;
  verified: boolean;
  created_at: Date;
  updated_at: Date;
}

async function migrateUsers(): Promise<void> {
  console.log('🔄 Migrating users...');
  
  const pool = new Pool(pgConfig);
  
  try {
    const result = await pool.query(`
      SELECT 
        id, email, name, phone, address, date_of_birth, profile_picture,
        fetchly_balance, membership_status, reward_points, saved_providers,
        notification_preferences, emergency_contact, role, verified,
        created_at, updated_at
      FROM users
    `);

    console.log(`Found ${result.rows.length} users to migrate`);

    // Uncomment when Firebase is configured
    // const batch = db.batch();
    
    // for (const user of result.rows as User[]) {
    //   const userRef = db.collection('users').doc(user.id);
    //   batch.set(userRef, {
    //     email: user.email,
    //     name: user.name,
    //     phone: user.phone,
    //     address: user.address,
    //     dateOfBirth: user.date_of_birth,
    //     profilePicture: user.profile_picture,
    //     fetchlyBalance: user.fetchly_balance,
    //     membershipStatus: user.membership_status,
    //     rewardPoints: user.reward_points,
    //     savedProviders: user.saved_providers || [],
    //     notificationPreferences: user.notification_preferences || {},
    //     emergencyContact: user.emergency_contact || {},
    //     role: user.role,
    //     verified: user.verified,
    //     createdAt: admin.firestore.Timestamp.fromDate(user.created_at),
    //     updatedAt: admin.firestore.Timestamp.fromDate(user.updated_at)
    //   });
    // }

    // await batch.commit();
    console.log('✅ Users migration completed');

  } catch (error) {
    console.error('❌ Error migrating users:', error);
  } finally {
    await pool.end();
  }
}

async function migrateProviders(): Promise<void> {
  console.log('🔄 Migrating providers...');
  
  const pool = new Pool(pgConfig);
  
  try {
    const result = await pool.query(`
      SELECT 
        id, user_id, business_name, owner_name, email, phone, service_type,
        address, city, state, zip_code, description, experience, specialties,
        status, verified, featured, business_hours, documents, business_photos,
        rating, review_count, total_bookings, total_revenue, completion_rate,
        response_time, response_rate, membership_tier, fetch_points,
        commissionsaved, social_media, settings, created_at, updated_at
      FROM providers
    `);

    console.log(`Found ${result.rows.length} providers to migrate`);

    // Similar migration logic for providers
    console.log('✅ Providers migration completed');

  } catch (error) {
    console.error('❌ Error migrating providers:', error);
  } finally {
    await pool.end();
  }
}

async function runMigration(): Promise<void> {
  console.log('🚀 Starting Firebase migration...');
  console.log('');
  
  try {
    await migrateUsers();
    await migrateProviders();
    
    console.log('');
    console.log('🎉 Migration completed successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Verify data in Firebase Console');
    console.log('2. Update your app to use Firebase');
    console.log('3. Test all functionality');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  runMigration();
}

export { migrateUsers, migrateProviders, runMigration };
