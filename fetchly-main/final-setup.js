const { Pool } = require('pg');
const readline = require('readline');
require('dotenv').config({ path: '.env.local' });

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3005'),
  user: 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  database: 'postgres'
};

async function testConnection() {
  const pool = new Pool(dbConfig);
  
  try {
    const client = await pool.connect();
    console.log('✅ PostgreSQL connection successful');
    client.release();
    await pool.end();
    return true;
  } catch (error) {
    console.error('❌ PostgreSQL connection failed:', error.message);
    await pool.end();
    return false;
  }
}

async function setupComplete() {
  console.log('🚀 Starting Fetchly PostgreSQL Setup...');
  console.log('');
  
  // Test PostgreSQL connection
  console.log('🔍 Testing PostgreSQL connection...');
  const connected = await testConnection();
  
  if (!connected) {
    console.log('');
    console.log('❌ Cannot connect to PostgreSQL. Please check:');
    console.log('1. PostgreSQL service is running: net start postgresql-x64-17');
    console.log('2. PostgreSQL is running on port 3005');
    console.log('3. Your postgres user password is correct');
    console.log('');
    console.log('Update POSTGRES_PASSWORD in .env.local with the correct password');
    process.exit(1);
  }
  
  console.log('');
  console.log('🔧 Setting up Fetchly database...');
  
  try {
    // Run database setup
    const { execSync } = require('child_process');
    execSync('npm run db:setup', { stdio: 'inherit' });
    
    console.log('');
    console.log('🎉 Setup completed successfully!');
    console.log('');
    console.log('🔐 Admin Account Created:');
    console.log(`   Email: ${process.env.ADMIN_EMAIL}`);
    console.log('   Password: [SECURELY STORED]');
    console.log('   Role: admin');
    console.log('');
    console.log('📊 Analytics Configuration:');
    console.log('   ✅ All metrics start from zero');
    console.log('   ✅ Real database data only');
    console.log('   ✅ No demo or fake data');
    console.log('   ✅ Clean analytics dashboard');
    console.log('');
    console.log('🚀 Ready to start:');
    console.log('   1. Run: npm run dev:full');
    console.log('   2. Open: http://localhost:3000');
    console.log('   3. Login with admin credentials');
    console.log('   4. Begin adding real users and data');
    console.log('');
    console.log('🔒 Security Features Active:');
    console.log('   ✅ JWT authentication with refresh tokens');
    console.log('   ✅ bcrypt password hashing');
    console.log('   ✅ Rate limiting on all endpoints');
    console.log('   ✅ Input validation and sanitization');
    console.log('   ✅ SQL injection protection');
    console.log('   ✅ XSS prevention');
    console.log('   ✅ CORS configuration');
    console.log('   ✅ File upload security');
    console.log('');
    
    const answer = await new Promise(resolve => {
      rl.question('Would you like to start the application now? (y/n): ', resolve);
    });
    
    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      console.log('');
      console.log('🚀 Starting Fetchly application...');
      console.log('   Frontend: http://localhost:3000');
      console.log('   API: http://localhost:3000/api');
      console.log('   Socket.IO: http://localhost:3002');
      console.log('');
      console.log('Press Ctrl+C to stop the application');
      console.log('');
      
      // Start the application
      execSync('npm run dev:full', { stdio: 'inherit' });
    } else {
      console.log('');
      console.log('To start the application later, run:');
      console.log('   npm run dev:full');
      console.log('');
      console.log('Happy coding! 🎉');
    }
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.log('');
    console.log('Please check the error above and try again.');
    console.log('You can also run individual commands:');
    console.log('   npm run db:setup     # Set up database');
    console.log('   npm run admin:setup  # Set up admin user');
    console.log('   npm run dev:full     # Start application');
  }
  
  rl.close();
}

async function main() {
  console.log('');
  console.log('╔══════════════════════════════════════════════════════════════╗');
  console.log('║                    🐾 FETCHLY SETUP 🐾                      ║');
  console.log('║              PostgreSQL Backend Configuration               ║');
  console.log('╚══════════════════════════════════════════════════════════════╝');
  console.log('');
  
  // Check if admin credentials are configured
  if (!process.env.ADMIN_EMAIL || !process.env.ADMIN_PASSWORD) {
    console.log('❌ Admin credentials not found in .env.local');
    console.log('');
    console.log('Please ensure your .env.local file contains:');
    console.log('   ADMIN_EMAIL=<EMAIL>');
    console.log('   ADMIN_PASSWORD=your_secure_password');
    console.log('');
    process.exit(1);
  }
  
  console.log('✅ Admin credentials configured');
  console.log('✅ PostgreSQL configuration found');
  console.log('✅ Environment variables loaded');
  console.log('');
  
  await setupComplete();
}

main().catch(console.error);
