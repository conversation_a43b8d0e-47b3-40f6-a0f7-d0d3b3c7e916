# 🎬 Stories Feature Usage Guide

## Overview
Complete Instagram/Facebook-style Stories feature with auto-advance, tap controls, progress bars, and 24-hour expiry.

## 🚀 Quick Start

### 1. Basic Usage (Already Integrated)
The Stories feature is already integrated into your Community page via `StoriesSection` component.

### 2. API Endpoints

#### Fetch Active Stories
```javascript
// GET /api/stories
const response = await fetch('/api/stories');
const { data: userStories } = await response.json();
```

#### Create New Story
```javascript
// POST /api/stories
const response = await fetch('/api/stories', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId: 'user123',
    userName: 'John Doe',
    userAvatar: 'https://example.com/avatar.jpg',
    mediaUrl: 'https://example.com/story.jpg',
    type: 'image', // or 'video'
    content: 'Optional text content',
    isPublic: true
  })
});
```

#### Track Story Views
```javascript
// POST /api/stories/[storyId]/view
await fetch(`/api/stories/${storyId}/view`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ viewerId: 'viewer123' })
});
```

### 3. Using Stories Service Directly

```javascript
import { StoriesService } from '@/lib/services/stories-service';

// Fetch active stories
const userStories = await StoriesService.getActiveStories();

// Create a story
const storyId = await StoriesService.createStory({
  userId: 'user123',
  userName: 'John Doe',
  userAvatar: '/avatar.jpg',
  mediaUrl: '/story-image.jpg',
  type: 'image',
  content: 'Check out my pet!',
  isPublic: true
});

// Get user's stories
const myStories = await StoriesService.getUserStories('user123');

// Get analytics
const analytics = await StoriesService.getStoryAnalytics('user123');
```

### 4. Custom Story Viewer Component

```jsx
import StoryViewer from '@/components/stories/StoryViewer';

function MyComponent() {
  const [showViewer, setShowViewer] = useState(false);
  const [userStories, setUserStories] = useState([]);

  const openStories = async () => {
    const stories = await StoriesService.getActiveStories();
    setUserStories(stories);
    setShowViewer(true);
  };

  return (
    <div>
      <button onClick={openStories}>View Stories</button>
      
      {showViewer && (
        <StoryViewer
          userStories={userStories}
          initialUserIndex={0}
          initialStoryIndex={0}
          onClose={() => setShowViewer(false)}
          onStoryView={(storyId, userId) => {
            // Track story view
            console.log(`Story ${storyId} viewed by ${userId}`);
          }}
        />
      )}
    </div>
  );
}
```

## 🎮 User Controls

### Touch/Mouse Controls
- **Tap Left Side**: Previous story
- **Tap Right Side**: Next story
- **Hold**: Pause story
- **Release**: Resume story

### Keyboard Controls
- **←**: Previous story
- **→ or Space**: Next story
- **Escape**: Close stories
- **P**: Toggle play/pause

### Video Controls
- **Play/Pause Button**: Control video playback
- **Mute/Unmute Button**: Control audio

## 📊 Features

### ✅ Auto-Advance
- Stories auto-advance after 5 seconds (images)
- Videos play for their duration (max 15 seconds)
- Smooth progress bar animation

### ✅ Progress Indicators
- Multi-segment progress bar
- Shows current story position
- Time remaining indicator

### ✅ Media Support
- **Images**: JPEG, PNG, GIF, WebP
- **Videos**: MP4, WebM, OGG
- **Max file size**: 50MB
- **Responsive**: Works on all screen sizes

### ✅ Expiry System
- Stories expire after 24 hours
- Automatic cleanup available
- Server-side filtering

### ✅ View Tracking
- Track who viewed each story
- View count analytics
- Real-time updates

### ✅ Professional UI
- Instagram/Facebook-style interface
- Smooth animations and transitions
- Mobile-optimized touch controls
- Accessibility support

## 🔧 Configuration

### Story Settings
```javascript
// In src/types/stories.ts
export const STORY_CONFIG = {
  DEFAULT_DURATION: 5000,        // 5 seconds per image
  VIDEO_MAX_DURATION: 15000,     // 15 seconds max for videos
  PROGRESS_UPDATE_INTERVAL: 50,  // Progress update frequency
  HOLD_THRESHOLD: 200,           // Hold time to pause
  EXPIRY_HOURS: 24,              // Stories expire after 24 hours
  MAX_STORIES_PER_USER: 10,      // Max stories per user
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB max file size
};
```

### Firestore Rules
Stories use the existing Firestore rules. Make sure your `stories` collection allows:
- Read: Public stories for all authenticated users
- Write: Users can create their own stories
- Update: For view tracking

## 🎨 Customization

### Custom Progress Bar
```jsx
<StoryProgress
  segments={5}
  currentSegment={2}
  progress={60}
  duration={5000}
/>
```

### Custom Media Component
```jsx
<StoryMedia
  story={storyObject}
  isActive={true}
  onLoadComplete={() => console.log('Media loaded')}
  onError={(error) => console.error('Media error:', error)}
/>
```

## 📱 Mobile Optimization

- **Touch-friendly**: Large tap areas
- **Gesture support**: Swipe and hold
- **Performance**: Optimized for mobile devices
- **Responsive**: Adapts to all screen sizes

## 🔍 Analytics

```javascript
const analytics = await StoriesService.getStoryAnalytics('user123');
console.log(analytics);
// {
//   totalStories: 5,
//   totalViews: 123,
//   averageViews: 24.6,
//   topStory: { ... }
// }
```

## 🧹 Maintenance

### Cleanup Expired Stories
```javascript
// Manual cleanup
const deletedCount = await StoriesService.deleteExpiredStories();

// Or via API
const response = await fetch('/api/stories', { method: 'DELETE' });
```

### Recommended: Set up a cron job or Cloud Function to run cleanup daily.

## 🚀 Production Ready

The Stories feature is fully production-ready with:
- ✅ Error handling and fallbacks
- ✅ Performance optimizations
- ✅ Mobile responsiveness
- ✅ Accessibility support
- ✅ Analytics and tracking
- ✅ Professional UI/UX
- ✅ 24-hour expiry system
- ✅ View tracking and analytics

## 🎯 Integration Status

**✅ Already Integrated:**
- Community page Stories section
- Story creation and viewing
- Auto-advance and controls
- Progress indicators
- View tracking
- Professional UI

**Ready to Use:**
- Open your Community page
- Click "+" to create a story
- Click any story circle to view
- Enjoy Instagram/Facebook-style experience!

Your Stories feature is now live and ready for users! 🎉
