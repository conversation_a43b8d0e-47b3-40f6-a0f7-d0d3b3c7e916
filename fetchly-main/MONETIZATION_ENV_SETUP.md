# Fetchly Monetization System - Environment Setup

## Required Stripe Environment Variables

Add these to your `.env.local` file:

```bash
# Stripe Subscription Price IDs (create these in Stripe Dashboard)
NEXT_PUBLIC_STRIPE_PRO_PRICE_ID=price_1234567890abcdef
NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID=price_0987654321fedcba

# Stripe Boost Price IDs (create these in Stripe Dashboard)
NEXT_PUBLIC_STRIPE_TOP_SEARCH_PRICE_ID=price_topinsearch123
NEXT_PUBLIC_STRIPE_FEATURED_TODAY_PRICE_ID=price_featuredtoday456

# Stripe Webhook Secret (from Stripe Dashboard)
STRIPE_WEBHOOK_SECRET=whsec_1234567890abcdef
```

## Stripe Dashboard Setup Instructions

### 1. Create Subscription Products & Prices

1. Go to Stripe Dashboard → Products
2. Create "Fetchly Pro Subscription":
   - Name: "Fetchly Pro"
   - Description: "Unlimited bookings, analytics, calendar sync, priority search"
   - Pricing: $9.99/month recurring
   - Copy the Price ID to `NEXT_PUBLIC_STRIPE_PRO_PRICE_ID`

3. Create "Fetchly Premium Subscription":
   - Name: "Fetchly Premium"
   - Description: "Everything in Pro + featured listing, SMS alerts, custom URL, weekly boosts"
   - Pricing: $29.99/month recurring
   - Copy the Price ID to `NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID`

### 2. Create Boost Products & Prices

1. Create "Top in Search Boost":
   - Name: "Top in Search"
   - Description: "Appear at the top of search results for 7 days"
   - Pricing: $4.99 one-time
   - Copy the Price ID to `NEXT_PUBLIC_STRIPE_TOP_SEARCH_PRICE_ID`

2. Create "Featured Today Boost":
   - Name: "Featured Today"
   - Description: "Featured placement on homepage for 24 hours"
   - Pricing: $1.99 one-time
   - Copy the Price ID to `NEXT_PUBLIC_STRIPE_FEATURED_TODAY_PRICE_ID`

### 3. Configure Webhooks

1. Go to Stripe Dashboard → Developers → Webhooks
2. Add endpoint: `https://yourdomain.com/api/webhooks/stripe`
3. Select these events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
4. Copy the webhook signing secret to `STRIPE_WEBHOOK_SECRET`

## Firestore Security Rules Update

The monetization system requires these additional Firestore collections. Add to your `firestore.rules`:

```javascript
// Provider Subscriptions
match /providerSubscriptions/{subscriptionId} {
  allow read: if isAuthenticated() && 
              (isOwner(subscriptionId) || isAdmin());
  allow write: if false; // Only server-side updates
}

// Provider Boosts
match /providerBoosts/{boostId} {
  allow read: if isAuthenticated();
  allow write: if false; // Only server-side updates
}

// Booking Add-ons
match /bookingAddOns/{addOnId} {
  allow read: if isAuthenticated();
  allow create: if isAuthenticated() && 
                isProvider() &&
                isOwner(request.resource.data.providerId);
  allow update, delete: if isAuthenticated() && 
                        isProvider() &&
                        isOwner(resource.data.providerId);
}
```

## Testing the Monetization System

### 1. Test Subscription Flow
1. Sign in as a provider
2. Go to Dashboard → Subscription tab
3. Click "Upgrade to Pro" or "Upgrade to Premium"
4. Complete Stripe checkout with test card: `4242 4242 4242 4242`
5. Verify subscription status updates in dashboard

### 2. Test Boost Purchases
1. Go to Dashboard → Boost tab
2. Purchase "Top in Search" or "Featured Today"
3. Use test card: `4242 4242 4242 4242`
4. Verify boost appears as active

### 3. Test Booking Commission
1. Create a booking with add-ons
2. Verify pricing breakdown shows 10% platform fee
3. Complete payment and verify commission split

### 4. Test Tip System
1. Complete a booking
2. Use tip interface to add tip
3. Verify 5% platform fee on tips

## Production Deployment Checklist

- [ ] All Stripe Price IDs configured
- [ ] Webhook endpoint configured and tested
- [ ] Firestore security rules updated
- [ ] Environment variables set in production
- [ ] Test all payment flows with live Stripe keys
- [ ] Monitor webhook delivery in Stripe Dashboard
- [ ] Set up Stripe monitoring and alerts

## Revenue Tracking

The system automatically tracks:
- Subscription revenue (monthly recurring)
- Boost purchase revenue (one-time)
- Booking commission revenue (10% of service + add-ons)
- Tip commission revenue (5% of tips)
- Cancellation fees (100% to platform)
- Express booking fees ($5 to platform)

All revenue data is stored in the `transactions` collection for reporting and analytics.
