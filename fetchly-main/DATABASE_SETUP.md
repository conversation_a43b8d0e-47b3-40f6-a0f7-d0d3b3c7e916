# Fetchly PostgreSQL Database Setup Guide

This guide will help you set up a local PostgreSQL database for the Fetchly application.

## Prerequisites

1. **Node.js** (v18 or higher)
2. **PostgreSQL** (v12 or higher)

## Step 1: Install PostgreSQL

### Windows:
1. Download PostgreSQL from: https://www.postgresql.org/download/windows/
2. Run the installer and follow the setup wizard
3. Remember the password you set for the `postgres` user
4. Default port is 5432 (keep this unless you have conflicts)

### macOS:
```bash
# Using Homebrew
brew install postgresql
brew services start postgresql

# Or download from: https://www.postgresql.org/download/macos/
```

### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## Step 2: Configure PostgreSQL

1. **Start PostgreSQL service** (if not already running):
   - Windows: PostgreSQL should start automatically after installation
   - macOS: `brew services start postgresql`
   - Linux: `sudo systemctl start postgresql`

2. **Access PostgreSQL command line**:
   ```bash
   # Windows (from Command Prompt or PowerShell)
   psql -U postgres

   # macOS/Linux
   sudo -u postgres psql
   ```

3. **Set up the database** (optional - the setup script will do this):
   ```sql
   CREATE USER fetchly_user WITH PASSWORD 'fetchly_password';
   CREATE DATABASE fetchly_db OWNER fetchly_user;
   GRANT ALL PRIVILEGES ON DATABASE fetchly_db TO fetchly_user;
   \q
   ```

## Step 3: Update Environment Variables

Update your `.env.local` file with the correct PostgreSQL credentials:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=fetchly_db
DB_USER=fetchly_user
DB_PASSWORD=fetchly_password

# If you used a different password for postgres user during installation:
POSTGRES_PASSWORD=your_postgres_password_here

# Other existing variables...
DATABASE_URL=postgresql://fetchly_user:fetchly_password@localhost:5432/fetchly_db
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production_2024_fetchly
REFRESH_TOKEN_SECRET=your_super_secret_refresh_token_key_change_this_in_production_2024_fetchly
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12
SOCKET_IO_PORT=3002
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
```

## Step 4: Install Dependencies

Make sure all required npm packages are installed:

```bash
npm install
```

## Step 5: Run Database Setup

Once PostgreSQL is running, execute the database setup script:

```bash
node database/setup.js
```

This script will:
- Create the `fetchly_user` and `fetchly_db` if they don't exist
- Run all database migrations
- Create tables with proper relationships and indexes
- Insert sample reward items
- Set up triggers for automatic timestamp updates

## Step 6: Verify Database Setup

You can verify the setup by connecting to the database:

```bash
psql -U fetchly_user -d fetchly_db -h localhost
```

Then check if tables were created:
```sql
\dt
```

You should see tables like: `users`, `pets`, `bookings`, `transactions`, etc.

## Step 7: Start the Application

Start both the Next.js server and Socket.IO server:

```bash
# Start the application with Socket.IO
node server.js

# Or for development with auto-reload
npm run dev
```

The application will be available at:
- **Next.js App**: http://localhost:3000
- **Socket.IO Server**: http://localhost:3002

## Troubleshooting

### PostgreSQL Connection Issues

1. **Service not running**:
   - Windows: Check Services app for "postgresql" service
   - macOS: `brew services list | grep postgresql`
   - Linux: `sudo systemctl status postgresql`

2. **Wrong credentials**:
   - Make sure the password in `.env.local` matches what you set during installation
   - Try connecting manually: `psql -U postgres -h localhost`

3. **Port conflicts**:
   - Check if port 5432 is available: `netstat -an | grep 5432`
   - If needed, change the port in PostgreSQL config and `.env.local`

4. **Permission issues**:
   - Make sure the `fetchly_user` has proper permissions
   - Try running the setup script with the postgres superuser

### Application Issues

1. **Module not found errors**:
   ```bash
   npm install
   ```

2. **Socket.IO connection issues**:
   - Make sure port 3002 is not blocked by firewall
   - Check if the Socket.IO server is running on the correct port

3. **Database query errors**:
   - Check the database logs for detailed error messages
   - Verify all tables were created properly

## Database Schema Overview

The database includes the following main tables:

- **users**: User accounts with authentication and profile data
- **pets**: Pet profiles with medical records
- **service_providers**: Business profiles for service providers
- **services**: Available services offered by providers
- **bookings**: Service bookings and appointments
- **transactions**: Fetchly Balance transactions
- **reward_transactions**: Reward points history
- **reward_items**: Available rewards for redemption
- **chat_rooms**: Chat room management
- **messages**: Real-time messaging
- **files**: File and image storage metadata

## Security Features

The database setup includes:

- **Password hashing** with bcrypt
- **JWT authentication** with refresh tokens
- **Rate limiting** for API endpoints
- **SQL injection protection** with parameterized queries
- **Role-based access control**
- **Input validation** and sanitization
- **CORS configuration**
- **Security headers** for API responses

## Next Steps

After successful setup:

1. **Test authentication** by registering a new user
2. **Create pet profiles** and test CRUD operations
3. **Test real-time chat** functionality
4. **Verify transaction** and reward systems
5. **Test file upload** capabilities

For production deployment, make sure to:
- Change all default passwords and secrets
- Use environment-specific configuration
- Set up proper SSL/TLS certificates
- Configure database backups
- Set up monitoring and logging
